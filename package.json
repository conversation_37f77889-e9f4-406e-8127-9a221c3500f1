{"name": "console-rocketmq", "version": "1.0.0", "description": "百度智能云 | RocketMQ", "main": "bootstrap.ts", "scripts": {"dev": "NODE_OPTIONS=--openssl-legacy-provider npx bce-cli dev --template=public-console --config=bce-config.js", "build": "NODE_OPTIONS=--openssl-legacy-provider npx bce-cli build --template=public-console --config=bce-config.js --no-i18n"}, "author": "yang<PERSON><PERSON>", "license": "MIT", "dependencies": {"@baidu/bce-bcc-sdk-enum": "^1.0.0-rc.91", "@baidu/bce-template": "^1.0.34", "@baidu/san-monaco-editor": "2.0.0-beta.8", "@baidu/sui-icon": "^1.0.41", "@baidu/xicon-san": "^0.0.48", "bignumber.js": "^9.1.2", "dayjs": "^1.11.13", "echarts": "^5.5.1", "js-base64": "^3.7.7", "resize-observer-polyfill": "^1.5.1", "san": "^3.7.7"}, "devDependencies": {"@babel/eslint-parser": "^7.24.8", "@babel/eslint-plugin": "^7.24.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@baidu/bce-cli": "^1.1.6-beta.6", "@ecomfe/eslint-config": "^8.0.0", "@ecomfe/stylelint-config": "^1.1.2", "@types/lodash": "^3.10.9", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.0", "postcss-less": "^3.1.4", "prettier": "^3.3.3", "prettier-eslint": "^16.3.0", "stylelint": "^14.16.1", "typescript": "^4.9.5"}, "externals": ["san"]}