/**
 * @file 工具模块启动配置
 */

const path = require('path');
module.exports = {
    // 模块相关配置
    service: 'ROCKETMQ', // 在P3M平台注册的service type，默认name全大写
    pathname: 'rocketmq', // 表明访问的path，默认代码库后几位字母
    flags: '', // 获取的功能清单的模块，默认是service

    // mock配置
    mockup: {
        caching: true,
        rules: ['/api/rocketmq/(.*)'], // 默认值 /api/pathname/*
        root: '.mockup' // 默认 /.mockup
    },

    // debug调试
    debug: {
        // '@baiducloud/runtime': 'http://localhost:8989/runtime.js',
        // '@baiducloud/xxx-sdk': 'http://localhost:8990/xxx-sdk.js'
    },

    // 代理地址
    // proxyTarget: 'https://qasandbox.bcetest.baidu.com',
    // proxyTarget: 'https://console.bce.baidu.com',

    // 需要排除的依赖，模块会帮助配置esl，sdk则需要依赖模块的配置
    dependencies: {
        // '@baiducloud/xxx-sdk': '@baiducloud/fe-xxx-sdk/x.x.x.x/xxx-sdk.js',
        '@baidu/sui': '@baiducloud/sui/1.0.342.1/sui.js',
        '@baidu/sui-biz': '@baiducloud/sui-biz/********/sui-biz.js',
        '@baidu/bce-tag-sdk': '@baiducloud/fe-tag-sdk/********/tag-sdk.js',
        '@baidu/bce-tag-sdk-san': '@baiducloud/fe-tag-sdk/********/tag-sdk-san.min.js',
        echarts: '@echarts/5.5.1/echarts.min.js'
    },
    // 自定义 esl模块加载 map 属性配置
    eslConfigAddMap: {
        '@baidu/sui': '@baidu/sui@1.0.342.1' // 必须要加，方便UMD模块的 sui-biz 对于 @baidu/sui 的依赖能找到
    },

    // 自定义webpack，可选，默认一个入口 index.js/index.ts
    webpack: {
        entry: {},
        resolve: {
            alias: {
                '@src': path.resolve('./src'),
                '@common': path.resolve('./common'),
                '@pages': path.resolve('./pages'),
                '@components': path.resolve('./components'),
                '@styles': path.resolve('./styles'),
                '@type': path.resolve('./types'),
                '@static': path.resolve('./static')
            }
        },
        externals: ['@baidu/sui', '@baidu/sui-biz', '@baiducloud/runtime', '@baiducloud/httpclient'],
        // 该配置的作用是让压缩代码插件使用webpack4默认的terser-webpack-plugin，而非UglifyJSPlugin
        optimization: {}
    },
    babelOptions: {
        plugins: [
            ['@babel/plugin-proposal-private-methods', {loose: true}],
            ['@babel/plugin-proposal-optional-chaining', {loose: true}],
            ['@babel/plugin-proposal-private-property-in-object', {loose: true}],
            ['@babel/plugin-proposal-nullish-coalescing-operator', {loose: true}]
        ]
    }
};
