/**
 * 全产品通用装饰器
 * @file common.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {SanComponent} from 'san';

export const debounce = (wait: number, options?: _.DebounceSettings) => {
    return (_target: any, _properKey: string, descriptor: PropertyDescriptor) => {
        const fn = descriptor.value;
        const func = function (...args: any[]) {
            fn.apply(this, args);
        };
        descriptor.value = _.debounce(func, wait, options);
    };
};

export const throttle = (wait: number, options?: _.ThrottleSettings) => {
    return (_target: any, _properKey: string, descriptor: PropertyDescriptor) => {
        const fn = descriptor.value;

        const func = function (...args: any[]) {
            fn.apply(this, args);
        };

        descriptor.value = _.throttle(func, wait, {trailing: false, ...options});
    };
};

/**
 * 数据输入组件装饰器，触发FormItem的校验
 *
 * @param {string[]} types 触发FormItem进行校验的事件名称
 * @returns {Funtion} 返回装饰器函数
 */
export const asInput = (...types: string[]) => {
    return <T extends new (...args: any[]) => SanComponent<any>>(target: T): T => {
        return class extends target {
            fire(this: SanComponent<any> & {parentComponent: SanComponent<any>}, name: string, event: any) {
                super.fire(name, event);

                if (['change', 'blur', ...types].includes(name)) {
                    this.dispatch('UI:form-item-interact', {
                        type: name,
                        e: event
                    });
                }
            }
        };
    };
};
