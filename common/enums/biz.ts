/**
 * 枚举
 * <AUTHOR>
 */

import {Enum} from '@baiducloud/runtime';

/**
 * 付费方式，付费方式，可选：Postpaid / Prepaid
 */
export enum Payment {
    Postpaid = 'Postpaid',
    Prepaid = 'Prepaid'
}

export let PaymentType = new Enum(
    {
        alias: 'PREPAID',
        text: '包年包月',
        value: Payment.Prepaid,
        desc: '先付费后使用，价格更低廉',
        tag: '推荐',
        class: 'prepaid'
    },
    {
        alias: 'POSTPAID',
        text: '按量付费',
        value: Payment.Postpaid,
        desc: '先使用后付费，按需开通',
        class: 'postpaid'
    }
);
export const PaymentsArr = PaymentType.toArray();
export const DefaultPaymentType = Payment.Prepaid;

// 集群状态配置
export enum ClusterStatusType {
    NEW = 'NEW',
    DEPLOYING = 'DEPLOYING',
    ACTIVE = 'ACTIVE',
    DEPLOY_ROLLBACKING = 'DEPLOY_ROLLBACKING',
    DEPLOY_FAILED = 'DEPLOY_FAILED',
    DEPLOY_ROLLBACK_FAILED = 'DEPLOY_ROLLBACK_FAILED',
    PRE_UPDATING = 'PRE_UPDATING',
    UPDATING = 'UPDATING',
    UPDATE_ROLLBACKING = 'UPDATE_ROLLBACKING',
    UPDATE_ROLLBACK_FAILED = 'UPDATE_ROLLBACK_FAILED',
    PRE_REBOOTING = 'PRE_REBOOTING',
    REBOOTING = 'REBOOTING',
    REBOOT_ROLLBACKING = 'REBOOT_ROLLBACKING',
    REBOOT_ROLLBACK_FAILED = 'REBOOT_ROLLBACK_FAILED',
    PRE_DEPLOY_ROLLBACKING = 'PRE_DEPLOY_ROLLBACKING',
    PRE_UPDATE_ROLLBACKING = 'PRE_UPDATE_ROLLBACKING',
    PRE_REBOOT_ROLLBACKING = 'PRE_REBOOT_ROLLBACKING',
    PRE_SUSPENDING = 'PRE_SUSPENDING',
    SUSPENDING = 'SUSPENDING',
    SUSPENDED = 'SUSPENDED',
    SUSPEND_ROLLBACKING = 'SUSPEND_ROLLBACKING',
    SUSPEND_ROLLBACK_FAILED = 'SUSPEND_ROLLBACK_FAILED',
    PRE_RELEASING = 'PRE_RELEASING',
    PRE_RESUMING = 'PRE_RESUMING',
    RESUMING = 'RESUMING',
    RESUME_ROLLBACKING = 'RESUME_ROLLBACKING',
    RESUME_ROLLBACK_FAILED = 'RESUME_ROLLBACK_FAILED',
    PRE_SUSPEND_ROLLBACKING = 'PRE_SUSPEND_ROLLBACKING',
    PRE_RESUME_ROLLBACKING = 'PRE_RESUME_ROLLBACKING'
}

// 集群状态配置，注意前后顺序
export const ClusterStatus = new Enum(
    {alias: ClusterStatusType.NEW, text: '新建', value: ClusterStatusType.NEW, klass: 'rolling'},
    {alias: ClusterStatusType.ACTIVE, text: '服务中', value: ClusterStatusType.ACTIVE, klass: 'normal'},
    {alias: ClusterStatusType.DEPLOYING, text: '正在部署', value: ClusterStatusType.DEPLOYING, klass: 'rolling'},
    {alias: ClusterStatusType.DEPLOY_FAILED, text: '部署失败', value: ClusterStatusType.DEPLOY_FAILED, klass: 'error'},
    {
        alias: ClusterStatusType.PRE_DEPLOY_ROLLBACKING,
        text: '待部署回滚',
        value: ClusterStatusType.PRE_DEPLOY_ROLLBACKING,
        klass: 'rolling'
    },
    {
        alias: ClusterStatusType.DEPLOY_ROLLBACKING,
        text: '部署回滚中',
        value: ClusterStatusType.DEPLOY_ROLLBACKING,
        klass: 'rolling'
    },
    {
        alias: ClusterStatusType.DEPLOY_ROLLBACK_FAILED,
        text: '部署回滚失败',
        value: ClusterStatusType.DEPLOY_ROLLBACK_FAILED,
        klass: 'error'
    },
    {alias: ClusterStatusType.PRE_REBOOTING, text: '待重启', value: ClusterStatusType.PRE_REBOOTING, klass: 'rolling'},
    {alias: ClusterStatusType.REBOOTING, text: '重启中', value: ClusterStatusType.REBOOTING, klass: 'rolling'},
    {
        alias: ClusterStatusType.PRE_REBOOT_ROLLBACKING,
        text: '待重启回滚',
        value: ClusterStatusType.PRE_REBOOT_ROLLBACKING,
        klass: 'rolling'
    },
    {
        alias: ClusterStatusType.REBOOT_ROLLBACKING,
        text: '重启回滚中',
        value: ClusterStatusType.REBOOT_ROLLBACKING,
        klass: 'rolling'
    },
    {
        alias: ClusterStatusType.REBOOT_ROLLBACK_FAILED,
        text: '重启回滚失败',
        value: ClusterStatusType.REBOOT_ROLLBACK_FAILED,
        klass: 'error'
    },
    {alias: ClusterStatusType.PRE_UPDATING, text: '待变更', value: ClusterStatusType.PRE_UPDATING, klass: 'rolling'},
    {alias: ClusterStatusType.UPDATING, text: '变更中', value: ClusterStatusType.UPDATING, klass: 'rolling'},
    {
        alias: ClusterStatusType.PRE_UPDATE_ROLLBACKING,
        text: '待变更回滚',
        value: ClusterStatusType.PRE_UPDATE_ROLLBACKING,
        klass: 'rolling'
    },
    {
        alias: ClusterStatusType.UPDATE_ROLLBACKING,
        text: '变更回滚中',
        value: ClusterStatusType.UPDATE_ROLLBACKING,
        klass: 'rolling'
    },
    {
        alias: ClusterStatusType.UPDATE_ROLLBACK_FAILED,
        text: '变更回滚失败',
        value: ClusterStatusType.UPDATE_ROLLBACK_FAILED,
        klass: 'error'
    },
    {
        alias: ClusterStatusType.PRE_SUSPENDING,
        text: '待停服',
        value: ClusterStatusType.PRE_SUSPENDING,
        klass: 'rolling'
    },
    {alias: ClusterStatusType.SUSPENDING, text: '停服中', value: ClusterStatusType.SUSPENDING, klass: 'rolling'},
    {alias: ClusterStatusType.SUSPENDED, text: '已停服', value: ClusterStatusType.SUSPENDED, klass: 'warning'},
    {
        alias: ClusterStatusType.PRE_SUSPEND_ROLLBACKING,
        text: '待停服回滚',
        value: ClusterStatusType.PRE_SUSPEND_ROLLBACKING,
        klass: 'rolling'
    },
    {
        alias: ClusterStatusType.SUSPEND_ROLLBACKING,
        text: '停服回滚中',
        value: ClusterStatusType.SUSPEND_ROLLBACKING,
        klass: 'rolling'
    },
    {
        alias: ClusterStatusType.SUSPEND_ROLLBACK_FAILED,
        text: '停服回滚失败',
        value: ClusterStatusType.SUSPEND_ROLLBACK_FAILED,
        klass: 'error'
    },
    {alias: ClusterStatusType.PRE_RELEASING, text: '待释放', value: ClusterStatusType.PRE_RELEASING, klass: 'rolling'},
    {alias: ClusterStatusType.PRE_RESUMING, text: '待恢复', value: ClusterStatusType.PRE_RESUMING, klass: 'rolling'},
    {alias: ClusterStatusType.RESUMING, text: '恢复中', value: ClusterStatusType.RESUMING, klass: 'rolling'},
    {
        alias: ClusterStatusType.PRE_RESUME_ROLLBACKING,
        text: '待恢复回滚',
        value: ClusterStatusType.PRE_RESUME_ROLLBACKING,
        klass: 'rolling'
    },
    {
        alias: ClusterStatusType.RESUME_ROLLBACKING,
        text: '恢复回滚中',
        value: ClusterStatusType.RESUME_ROLLBACKING,
        klass: 'rolling'
    },
    {
        alias: ClusterStatusType.RESUME_ROLLBACK_FAILED,
        text: '恢复回滚失败',
        value: ClusterStatusType.RESUME_ROLLBACK_FAILED,
        klass: 'error'
    }
);

export const Encryption_Transit_Type = new Enum(
    {alias: 'PLAINTEXT', text: 'PLAINTEXT', value: 'PLAINTEXT'},
    {alias: 'PERMISSIVE', text: 'PERMISSIVE', value: 'PERMISSIVE'},
    {alias: 'SSL', text: 'SSL', value: 'SSL'}
);

export enum ClusterOperationType {
    'DELETE' = 'DELETE',
    'TICKET' = 'TICKET',
    'STOP' = 'STOP',
    'START' = 'START',
    'SCALE' = 'SCALE',
    'PAYMENT_UPDATE' = 'PAYMENT_UPDATE'
}
// 集群更多操作项
export const CLUSTER_MORE_OPERATIONS = new Enum(
    {alias: 'SCALE', text: '变更规格', value: ClusterOperationType.SCALE},
    {alias: 'DELETE', text: '删除集群', value: ClusterOperationType.DELETE},
    {alias: 'TICKET', text: '提交工单', value: ClusterOperationType.TICKET},
    {alias: 'STOP', text: '停止集群', value: ClusterOperationType.STOP},
    {alias: 'START', text: '启动集群', value: ClusterOperationType.START},
    {alias: 'PAYMENT_UPDATE', text: '计费变更', value: ClusterOperationType.PAYMENT_UPDATE}
);

// 创建集群模块
export enum ClusterRefType {
    REGION = 'region-config',
    CLUSTER = 'cluster-config',
    NODE = 'node-config',
    DISK = 'disk-config',
    NETWORK = 'network-config',
    ACCESS = 'access-config',
    TAG = 'tag-config',
    COUPON = 'coupon-config'
}

// 集群配置
export const ClusterDefaultConf = new Enum({alias: 'default', text: '默认配置', value: 'default'});

export const VersionStatus = new Enum(
    {alias: 'TRIAL', text: '（试用）', value: 'TRIAL'},
    {alias: 'ACTIVE', text: '', value: 'ACTIVE'},
    {alias: 'RECOMMENDED', text: '（推荐）', value: 'RECOMMENDED'}
);

export enum ArchType {
    DLEDGER = 'DLEDGER',
    MASTER_SLAVE = 'MASTER_SLAVE'
}
// 部署架构
export const ArchList = new Enum(
    {alias: ArchType.DLEDGER, text: 'DLedger', value: ArchType.DLEDGER},
    {alias: ArchType.MASTER_SLAVE, text: 'Master-Slave', value: ArchType.MASTER_SLAVE}
);

/**
 * 刷盘方式，刷盘方式，可选：ASYNC_FLUSH / SYNC_FLUSH，默认 ASYNC_FLUSH
 */
export enum FlushDiskType {
    AsyncFlush = 'ASYNC_FLUSH',
    SyncFlush = 'SYNC_FLUSH'
}
// 副本机制
export const FlushList = new Enum(
    {alias: FlushDiskType.AsyncFlush, text: '异步复制', value: FlushDiskType.AsyncFlush},
    {alias: FlushDiskType.SyncFlush, text: '同步复制', value: FlushDiskType.SyncFlush}
);

/**
 * 部署模式
 */
export enum ModeType {
    HP = 'HP',
    HA = 'HA'
}
// 部署方式
export const ModeList = new Enum(
    {alias: ModeType.HP, text: '单可用区', value: ModeType.HP},
    {alias: ModeType.HA, text: '多可用区', value: ModeType.HA}
);

export const diskNums4 = new Enum(
    {value: 1, text: '1', alias: '1'},
    {value: 2, text: '2', alias: '2'},
    {value: 4, text: '4', alias: '4'}
);

export const diskNums2 = new Enum({value: 1, text: '1', alias: '1'}, {value: 2, text: '2', alias: '2'});

export const diskNums8 = new Enum(
    {value: 1, text: '1', alias: '1'},
    {value: 2, text: '2', alias: '2'},
    {value: 4, text: '4', alias: '4'},
    {value: 8, text: '8', alias: '8'}
);

export const PublicAccessModeList = new Enum({alias: 'AUTO_ASSIGN', text: '自动分配', value: 'AUTO_ASSIGN'});

/**
 * 认证方式
 */
export enum AuthenticationModeType {
    None = 'NONE',
    SaslPlain = 'SASL_PLAIN'
}
/**
 * 认证方式
 */
export const AuthenticationModeList = new Enum(
    {
        alias: AuthenticationModeType.None,
        value: AuthenticationModeType.None,
        // radio展示label文案
        text: 'None（无需身份认证）',
        help: '客户端无需身份认证，并且允许所有操作。',
        // 由value映射的展示文案
        label: 'NONE'
    },
    {
        alias: AuthenticationModeType.SaslPlain,
        value: AuthenticationModeType.SaslPlain,
        // radio展示label文案
        text: 'SASL/PLAIN身份认证',
        help: 'PLAIN使用SASL框架提供用户名和密码验证方法。',
        // 由value映射的展示文案
        label: 'SASL/PLAIN'
    }
);

/**
 * 传输加密模式，传输加密模式，默认PLAINTEXT（关闭），可选：PLAINTEXT / PERMISSIVE / SSL。如果开启公网，应该至少开启PERMISSIVE
 */
export enum EncryptionInTransitType {
    Permissive = 'PERMISSIVE',
    Plaintext = 'PLAINTEXT',
    SSL = 'SSL'
}
export const EncryptionInTransitList = new Enum(
    {
        alias: EncryptionInTransitType.SSL,
        value: EncryptionInTransitType.SSL,
        text: EncryptionInTransitType.SSL,
        help: '服务端与客户端之间通过密文传输，安全性较高，性能较低。'
    },
    {
        alias: EncryptionInTransitType.Plaintext,
        value: EncryptionInTransitType.Plaintext,
        text: EncryptionInTransitType.Plaintext,
        help: '服务端与客户端之间通过明文传输，安全性较低，性能较高。'
    },
    {
        alias: EncryptionInTransitType.Permissive,
        value: EncryptionInTransitType.Permissive,
        text: EncryptionInTransitType.Permissive,
        help: '服务端与客户端之间即能通过明文传输又能通过密文传输，传输方式由客户端决定。'
    }
);

// 代理节点状态枚举
export enum NodeClientStatusType {
    NEW = 'NEW',
    ALIVE = 'ALIVE',
    DEAD = 'DEAD',
    LOST = 'LOST'
}

// 节点状态
export const ClusterNodeClientStatus = new Enum(
    {alias: NodeClientStatusType.NEW, text: '等待部署', value: NodeClientStatusType.NEW, klass: 'rolling'},
    {alias: NodeClientStatusType.ALIVE, text: '服务中', value: NodeClientStatusType.ALIVE, klass: 'normal'},
    {alias: NodeClientStatusType.DEAD, text: '异常', value: NodeClientStatusType.DEAD, klass: 'error'},
    {alias: NodeClientStatusType.LOST, text: '丢失', value: NodeClientStatusType.LOST, klass: 'error'}
);

/**
 * 任务类型
 */
export enum ClusterActionType {
    UpgradeBrokerNodeType = 'UPGRADE_BROKER_NODE_TYPE',
    DowngradeBrokerNodeType = 'DOWNGRADE_BROKER_NODE_TYPE',
    ExpandBrokerDiskCapacity = 'EXPAND_BROKER_DISK_CAPACITY',
    IncreaseBrokerCount = 'INCREASE_BROKER_COUNT',
    ResumeCluster = 'RESUME_CLUSTER',
    SuspendCluster = 'SUSPEND_CLUSTER',
    RestartBrokerNode = 'RESTART_BROKER_NODE'
}

// 任务类型
export const ClusterActionTypeList = new Enum(
    {alias: '', text: '全部类型', value: ''},
    {
        alias: ClusterActionType.IncreaseBrokerCount,
        text: '增加节点数量',
        value: ClusterActionType.IncreaseBrokerCount
    },
    {
        alias: ClusterActionType.UpgradeBrokerNodeType,
        text: '升级节点规格',
        value: ClusterActionType.UpgradeBrokerNodeType
    },
    {
        alias: ClusterActionType.DowngradeBrokerNodeType,
        text: '降低节点规格',
        value: ClusterActionType.DowngradeBrokerNodeType
    },
    {
        alias: ClusterActionType.ExpandBrokerDiskCapacity,
        text: '扩容节点磁盘',
        value: ClusterActionType.ExpandBrokerDiskCapacity
    },
    {
        alias: ClusterActionType.ResumeCluster,
        text: '启动集群服务',
        value: ClusterActionType.ResumeCluster
    },
    {
        alias: ClusterActionType.SuspendCluster,
        text: '停止集群服务',
        value: ClusterActionType.SuspendCluster
    },
    {
        alias: ClusterActionType.RestartBrokerNode,
        text: '重启集群节点',
        value: ClusterActionType.RestartBrokerNode
    }
);

/** 操作类型枚举 */
export enum OperationType {
    IncreaseBrokerCount = 'INCREASE_BROKER_COUNT',
    IncreaseBrokerCountRollback = 'INCREASE_BROKER_COUNT_ROLLBACK',
    DowngradeBrokerNodeType = 'DOWNGRADE_BROKER_NODE_TYPE',
    DowngradeBrokerNodeTypeRollback = 'DOWNGRADE_BROKER_NODE_TYPE_ROLLBACK',
    UpgradeBrokerNodeType = 'UPGRADE_BROKER_NODE_TYPE',
    UpgradeBrokerNodeTypeRollback = 'UPGRADE_BROKER_NODE_TYPE_ROLLBACK',
    ExpandBrokerDiskCapacity = 'EXPAND_BROKER_DISK_CAPACITY',
    ExpandBrokerDiskCapacityRollback = 'EXPAND_BROKER_DISK_CAPACITY_ROLLBACK',
    RestartBroker = 'RESTART_BROKER',
    RestartBrokerRollback = 'RESTART_BROKER_ROLLBACK',
    RestartBrokerNode = 'RESTART_BROKER_NODE',
    RestartBrokerNodeRollback = 'RESTART_BROKER_NODE_ROLLBACK',
    ResumeCluster = 'RESUME_CLUSTER',
    ResumeClusterRollback = 'RESUME_CLUSTER_ROLLBACK',
    SuspendCluster = 'SUSPEND_CLUSTER',
    SuspendClusterRollback = 'SUSPEND_CLUSTER_ROLLBACK'
}

export const OperationTypeList = new Enum(
    {
        alias: OperationType.IncreaseBrokerCount,
        text: '扩容节点',
        value: OperationType.IncreaseBrokerCount
    },
    {
        alias: OperationType.IncreaseBrokerCountRollback,
        text: '回滚扩容',
        value: OperationType.IncreaseBrokerCountRollback
    },
    {
        alias: OperationType.UpgradeBrokerNodeType,
        text: '升配节点',
        value: OperationType.UpgradeBrokerNodeType
    },
    {
        alias: OperationType.UpgradeBrokerNodeTypeRollback,
        text: '回滚升配',
        value: OperationType.UpgradeBrokerNodeTypeRollback
    },
    {
        alias: OperationType.DowngradeBrokerNodeType,
        text: '降配节点',
        value: OperationType.DowngradeBrokerNodeType
    },
    {
        alias: OperationType.DowngradeBrokerNodeTypeRollback,
        text: '回滚降配',
        value: OperationType.DowngradeBrokerNodeTypeRollback
    },
    {
        alias: OperationType.ExpandBrokerDiskCapacity,
        text: '扩容磁盘',
        value: OperationType.ExpandBrokerDiskCapacity
    },
    {
        alias: OperationType.ExpandBrokerDiskCapacityRollback,
        text: '回滚扩容',
        value: OperationType.ExpandBrokerDiskCapacityRollback
    },
    {
        alias: OperationType.RestartBroker,
        text: '重启节点',
        value: OperationType.RestartBroker
    },
    {
        alias: OperationType.RestartBrokerNode,
        text: '重启节点',
        value: OperationType.RestartBrokerNode
    },
    {
        alias: OperationType.RestartBrokerNodeRollback,
        text: '回滚重启',
        value: OperationType.RestartBrokerNodeRollback
    },
    {
        alias: OperationType.RestartBrokerRollback,
        text: '回滚重启',
        value: OperationType.RestartBrokerRollback
    },
    {
        alias: OperationType.ResumeCluster,
        text: '启动集群',
        value: OperationType.ResumeCluster
    },
    {
        alias: OperationType.ResumeClusterRollback,
        text: '回滚启动',
        value: OperationType.ResumeClusterRollback
    },
    {
        alias: OperationType.SuspendCluster,
        text: '停止集群',
        value: OperationType.SuspendCluster
    },
    {
        alias: OperationType.SuspendClusterRollback,
        text: '回滚停止',
        value: OperationType.SuspendClusterRollback
    }
);

// 任务状态
export enum ClusterActionStatusType {
    PREPARE = 'PREPARE',
    NEW = 'NEW',
    PENDING = 'PENDING',
    RUNNING = 'RUNNING',
    FINISHED = 'FINISHED',
    FAILED = 'FAILED',
    SUSPENDED = 'SUSPENDED',
    CANCELED = 'CANCELLED'
}

// 任务状态枚举
export const ClusterActionStatus = new Enum(
    {
        alias: ClusterActionStatusType.PREPARE,
        text: '待调度',
        value: ClusterActionStatusType.PREPARE,
        klass: 'warning'
    },
    {
        alias: ClusterActionStatusType.NEW,
        text: '待执行',
        value: ClusterActionStatusType.NEW,
        klass: 'active'
    },
    {
        alias: ClusterActionStatusType.PENDING,
        text: '执行中',
        value: ClusterActionStatusType.PENDING,
        klass: 'processing'
    },
    {
        alias: ClusterActionStatusType.RUNNING,
        text: '执行中',
        value: ClusterActionStatusType.RUNNING,
        klass: 'processing'
    },
    {
        alias: ClusterActionStatusType.FINISHED,
        text: '成功',
        value: ClusterActionStatusType.FINISHED,
        klass: 'success'
    },
    {
        alias: ClusterActionStatusType.FAILED,
        text: '失败',
        value: ClusterActionStatusType.FAILED,
        klass: 'error'
    },
    {
        alias: ClusterActionStatusType.SUSPENDED,
        text: '暂停',
        value: ClusterActionStatusType.SUSPENDED,
        klass: 'inactive'
    },
    {
        alias: ClusterActionStatusType.CANCELED,
        text: '取消',
        value: ClusterActionStatusType.CANCELED,
        klass: 'inactive'
    }
);

/**
 * 主题管理-权限 枚举
 */
export enum ClusterTopicPermissionType {
    BOTH = 6,
    PUBLISH = 2,
    SUBSCRIBE = 4
}

/**
 * 主题管理-权限 枚举
 */
export const ClusterTopicPermissionTypeList = new Enum(
    {
        alias: ClusterTopicPermissionType.BOTH,
        text: '发布+订阅',
        value: ClusterTopicPermissionType.BOTH
    },
    {
        alias: ClusterTopicPermissionType.PUBLISH,
        text: '发布',
        value: ClusterTopicPermissionType.PUBLISH
    },
    {
        alias: ClusterTopicPermissionType.SUBSCRIBE,
        text: '订阅',
        value: ClusterTopicPermissionType.SUBSCRIBE
    }
);

// 消费组 消息模式 messageModel 枚举
export enum ClusterConsumerMessageModelType {
    BROADCASTING = 'BROADCASTING',
    CLUSTERING = 'CLUSTERING',
    UNKNOWN = 'UNKNOWN'
}

// 消费组 消息模式 messageModel
export const ClusterConsumerMessageModel = new Enum(
    {
        alias: ClusterConsumerMessageModelType.BROADCASTING,
        text: '广播模式',
        value: ClusterConsumerMessageModelType.BROADCASTING
    },
    {
        alias: ClusterConsumerMessageModelType.CLUSTERING,
        text: '集群模式',
        value: ClusterConsumerMessageModelType.CLUSTERING
    },
    {
        alias: ClusterConsumerMessageModelType.UNKNOWN,
        text: '未知',
        value: ClusterConsumerMessageModelType.UNKNOWN
    }
);

/** 权限枚举 */
export enum IPermissionType {
    Deny = 'DENY',
    Pub = 'PUB',
    PubSub = 'PUB_SUB',
    Sub = 'SUB'
}

export const TopicPerssions = new Enum(
    {alias: IPermissionType.PubSub, text: '发布+订阅', value: IPermissionType.PubSub},
    {alias: IPermissionType.Pub, text: '发布', value: IPermissionType.Pub},
    {alias: IPermissionType.Sub, text: '订阅', value: IPermissionType.Sub},
    {alias: IPermissionType.Deny, text: '无权限', value: IPermissionType.Deny}
);
export const TopicPerssionArr = TopicPerssions.toArray();

export const ConsumerGroupPerssions = new Enum(
    {alias: IPermissionType.Sub, text: '订阅', value: IPermissionType.Sub},
    {alias: IPermissionType.Deny, text: '无权限', value: IPermissionType.Deny}
);
export const ConsumerGroupPerssionArr = ConsumerGroupPerssions.toArray();

export enum TimeType {
    Custom = 'custom',
    Earliest = 0,
    Latest = -1
}

export const TimeTypeArr = [
    {text: '自定义', value: TimeType.Custom},
    {text: '最早', value: TimeType.Earliest},
    {text: '最晚', value: TimeType.Latest}
];

export const ActionsStatus = new Enum(
    {
        alias: ClusterActionStatusType.PREPARE,
        text: '待调度',
        value: ClusterActionStatusType.PREPARE,
        klass: 'warning'
    },
    {
        alias: ClusterActionStatusType.NEW,
        text: '待执行',
        value: ClusterActionStatusType.NEW,
        klass: 'active'
    },
    {
        alias: ClusterActionStatusType.PENDING,
        text: '待执行',
        value: ClusterActionStatusType.PENDING,
        klass: 'active'
    },
    {
        alias: ClusterActionStatusType.RUNNING,
        text: '执行中',
        value: ClusterActionStatusType.RUNNING,
        klass: 'processing'
    },
    {
        alias: ClusterActionStatusType.FINISHED,
        text: '成功',
        value: ClusterActionStatusType.FINISHED,
        klass: 'success'
    },
    {
        alias: ClusterActionStatusType.FAILED,
        text: '失败',
        value: ClusterActionStatusType.FAILED,
        klass: 'error'
    },
    {
        alias: ClusterActionStatusType.SUSPENDED,
        text: '暂停',
        value: ClusterActionStatusType.SUSPENDED,
        klass: 'inactive'
    },
    {
        alias: ClusterActionStatusType.CANCELED,
        text: '取消',
        value: ClusterActionStatusType.CANCELED,
        klass: 'inactive'
    }
);
