/**
 * 枚举
 * <AUTHOR>
 */

import {Enum} from '@baiducloud/runtime';

// 默认所有
export const ALL_ENUM = new Enum({alias: 'ALL', text: '全部', value: ''});

// 预付费时长
export const PREPAY_TIME_LIST = new Enum(
    {alias: '1', text: '1个月', value: 1},
    {alias: '2', text: '2个月', value: 2},
    {alias: '3', text: '3个月', value: 3},
    {alias: '4', text: '4个月', value: 4},
    {alias: '5', text: '5个月', value: 5},
    {alias: '6', text: '6个月', value: 6},
    {alias: '7', text: '7个月', value: 7},
    {alias: '8', text: '8个月', value: 8},
    {alias: '9', text: '9个月', value: 9},
    {alias: '12', text: '1年', value: 12},
    {alias: '24', text: '2年', value: 24},
    {alias: '36', text: '3年', value: 36},
);

// 自动付费按月时间选择框
export const AUTO_RENEW_MONTH = new Enum(
    {alias: '1', text: '1', value: 1},
    {alias: '2', text: '2', value: 2},
    {alias: '3', text: '3', value: 3},
    {alias: '4', text: '4', value: 4},
    {alias: '5', text: '5', value: 5},
    {alias: '6', text: '6', value: 6},
    {alias: '7', text: '7', value: 7},
    {alias: '8', text: '8', value: 8},
    {alias: '9', text: '9', value: 9},
);

// 自动付费按年时间选择框
export const AUTO_RENEW_YEAR = new Enum(
    {alias: '1', text: '1', value: 1},
    {alias: '2', text: '2', value: 2},
    {alias: '3', text: '3', value: 3},
);
