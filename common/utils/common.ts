/**
 * 通用处理方法：可在各个产品中复用
 * @file util.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import {isSandbox} from '../config/biz';
dayjs.extend(utc);

export const isInvalid = (item: any): boolean => {
    return ['', undefined, null].includes(item);
};

// 空文本
export const formatEmpty = (value?: string | number) => (!value && value !== 0 ? '-' : value);

export const pickEmpty = (obj: Object) => {
    // @ts-ignore
    return _.pick(obj, _.identity);
};

// 格式化时间
export const formatTime = (time?: dayjs.ConfigType, format?: string) => {
    return time ? dayjs(time).format(format || 'YYYY-MM-DD HH:mm:ss') : formatEmpty();
};

// 将时间转化为utc格式
export const formatUtcTime = (time: dayjs.ConfigType, pattern?: string) => {
    const t = dayjs(time);
    if (!t.isValid()) {
        return null;
    }
    return `${dayjs(time).utc().format('YYYY-MM-DDTHH:mm:ss')}Z`;
};

// 在数组中查找，能找到，返回对应的key对应文本，否则返回空
export const getItemInfoInArr = (
    arr: Array<{[x: string]: any}>,
    target: string | number,
    key: string = 'value',
    label: string = 'text'
) => {
    const item = _.find(arr, i => i[key] === target);
    return item && item[label] ? item[label] : '';
};

/**
 * 系列化对象的url的query对象
 * 例如对象 {a: 1, b: 2} 转化为 a=1&b=2 的字符串
 * @param {Object} query query对象
 * @returns {string} 拼接后的字符串
 */
export const serializeQuery = (query: Record<string, string | number | string[] | number[]>) => {
    if (!query) {
        return '';
    }
    let search = '';
    for (const key in query) {
        if (query.hasOwnProperty(key)) {
            const value = query[key];
            if (value === undefined || value === null) {
                continue;
            }
            // 如果`value`是数组，其`toString`会自动转为逗号分隔的字符串
            search += '&' + key + '=' + encodeURI(value.toString());
        }
    }
    return search.slice(1);
};

/**
 * 将url参数处理为对象{key: value}
 * eg: xx?a=1&b=2 ——》{a: 1, b: 2}
 * @param {string} hash hash值
 * @returns {Object} hash转化后的对象
 */
export const formatUrlQuery = (hash: string) => {
    let arr = hash.split('?');
    if (!arr[1]) {
        return {};
    }

    let res = arr[1].split('&');
    let params: Record<string, string> = {};
    for (let i = 0; i < res.length; i++) {
        const a = res[i].split('=');
        params[a[0]] = decodeURI(a[1]);
    }
    return params;
};

/**
 * 更新url参数值
 * eg1 url：https://console.bce.baidu.com/bmr/?_=111#/bmr/cluster/detail?clusterId=222
 * eg2 url: https://console.bce.baidu.com/bmr/#/bmr/cluster/detail?clusterId=222
 * @param {*} params 需更新的参数及值
 * @param {*} notPrev 是否不要之前的参数
 */
export const updateUrlQuery = (params: Record<string, string>, notPrev: boolean = false) => {
    const href = location.href;
    let prevParams = formatUrlQuery(location.hash);
    const splitArr = href.split('?');
    // eg1: ['https://console.bce.baidu.com/bmr/', '_=111#/bmr/cluster/detail', 'clusterId=222']
    // eg2: ['https://console.bce.baidu.com/bmr/#/bmr/cluster/detail', 'clusterId=222']
    let newUrl = splitArr[0] || '';
    if (splitArr[1] && splitArr[1].includes('_=')) {
        newUrl += '?' + splitArr[1];
    }
    newUrl +=
        '?' +
        serializeQuery({
            ...(notPrev ? {} : prevParams),
            ...params
        });

    window.history.replaceState('', '', newUrl);
};

/**
 * 请求轮询
 * 1. 支持错误重试次数达设置 retryCount 后，停止轮询
 * 2. 页面可见时，开始轮询（结合自定义 shouldPoll 条件判断）
 * 3. 页面不可见时，停止轮询
 * 4. 可设置是否立即请求
 * @param func 需要轮询的函数，应该返回一个 Promise
 * @param options 配置项
 */
// TODO：还需要打断当前的请求 目前还没实现
export const requestInterval = (
    func: () => Promise<any>,
    options?: {
        retryCount?: number;
        time?: number;
        immediate?: boolean;
        shouldPoll?: () => boolean; // 自定义轮询触发条件
    }
) => {
    let retryAttempts = 0; // 当前重试次数
    let timeoutId: NodeJS.Timeout | null = null;

    const {retryCount = 3, time = 10 * 1000, immediate = false, shouldPoll} = options || {};

    // 默认的轮询触发条件：页面可见
    function defaultShouldPoll() {
        return document.visibilityState === 'visible';
    }

    function customShouldPoll() {
        if (typeof shouldPoll === 'function') {
            return shouldPoll();
        }

        return true;
    }

    // 最终的轮询条件，要求满足默认条件和自定义条件
    const finalShouldPoll = () => {
        return defaultShouldPoll() && customShouldPoll();
    };

    const executeFunc = async () => {
        try {
            await func(); // 调用传入的函数
            timeoutId !== null && startPolling(); // 继续下一次轮询
            retryAttempts = 0; // 成功后重置重试计数
        } catch (error) {
            retryAttempts += 1;
            console.error(`Polling failed attempt ${retryAttempts}:`, error);
            if (retryAttempts >= retryCount) {
                console.warn('Reached max retry count. Stopping polling.');
                stopPolling();
                return;
            }
        }
    };

    const startPolling = (immediate?: boolean) => {
        if (timeoutId) {
            stopPolling();
        }
        timeoutId = setTimeout(
            async () => {
                if (finalShouldPoll()) {
                    await executeFunc();
                }
            },
            immediate ? 0 : time
        );
    };

    const stopPolling = () => {
        if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
        }
    };

    // 页面可见性变化监听器
    const visibilityChangeHandler = () => {
        if (defaultShouldPoll()) {
            executeFunc();
        } else {
            stopPolling();
        }
    };

    // 初始状态检查
    startPolling(immediate);

    // 添加页面可见性变化监听器
    document.addEventListener('visibilitychange', visibilityChangeHandler);

    // 返回停止轮询的方法，以便手动控制
    return () => {
        stopPolling();
        document.removeEventListener('visibilitychange', visibilityChangeHandler);
    };
};

// 监控通用数据
export const getBcmMetricCommonParams = () => {
    return {
        region: window.$context.getCurrentRegionId(),
        userId: window.$context.getUserId()
    };
};

/**
 * 深度扩展
 *
 * @param {Object} src 目标对象
 * @param {Object} opt 扩充对象
 */
export function deepExtend(src: NormalObject, opt: NormalObject) {
    _.each(opt, function (item, key) {
        if (_.isArray(item)) {
            deepExtend((src[key] = src[key] || []), item);
        } else if (_.isFunction(item)) {
            src[key] = item;
        } else if (_.isObject(item)) {
            deepExtend((src[key] = src[key] || {}), item);
        } else {
            src[key] = item;
        }
    });
}

// 是否为集团云onecloud账户;
export const isOneCloudId = () =>
    window.$context.getUserId() ===
    (isSandbox ? '********************************' : '0c0b3c9dbb6e41308d3bfd587d908922');
