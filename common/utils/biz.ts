/**
 * 产品通用处理方法
 * @file biz.ts
 * <AUTHOR>
 */

import {DiskType} from '@baidu/bce-bcc-sdk-enum';
import {baseClient} from '@common/client/base';
import {TagSDK} from '@baidu/bce-tag-sdk';
import _ from 'lodash';
import {
    ArchList,
    FlushList,
    ModeList,
    Payment,
    PaymentType,
    AuthenticationModeList,
    EncryptionInTransitType,
    AuthenticationModeType,
    ClusterTopicPermissionTypeList,
    IPermissionType,
    TopicPerssions,
    ConsumerGroupPerssions,
    ArchType,
    ClusterStatusType
} from '@common/enums';
import {PriceUnit, TimeUnit, Units} from '@common/config';
import dayjs from 'dayjs';
import {Notification} from '@baidu/sui';
import {ZoneListItem} from '@pages/cluster/create/node-config/index.d';
import {IClusterItem, IAllowedActions, IFeClusterItem} from '@pages/cluster/list/index.d';
import {IClusterDetail, IFeClusterDetail} from '@pages/cluster/detail/index.d';

/**
 * 标签信息过滤器
 * @type {Object}
 */
export const TagFilter = {
    tagText(item) {
        let tagHtml = '';
        _.each(item.tags, (item, index) => {
            if (index < 2) {
                let text = `${_.escape(item.tagKey)} : ${_.escape(item.tagValue) || '-'}`;
                text = text.length > 16 ? text.slice(0, 16) + '...' : text;
                tagHtml += `${text}<br/>`;
            }
        });
        item.tags.length > 2 && (tagHtml = item.tags.length + '个标签');
        return tagHtml;
    },
    tagTip(item) {
        let tags = '';
        _.each(item.tags, (item, index) => {
            tags += `${_.escape(item.tagKey)} : ${_.escape(item.tagValue) || '-'}<br/>`;
        });
        return tags;
    }
};

export const getSdk = (name: 'TAG', options = {}) => {
    const context = window.$context;
    switch (name) {
        case 'TAG':
            return new TagSDK({
                ...options,
                serviceType: 'ROCKETMQ',
                client: baseClient,
                context
            });
        default:
            return null;
    }
};

export const formatRegion = (region: string) => {
    const allRegion = window.$context.getEnum('AllRegion');
    return allRegion.getTextFromValue(region);
};

export const formatPayment = (payment: string) => PaymentType.getTextFromValue(payment);

/**
 * 根据预付费时长获取timeLength和timeUnit
 * @param timeLength
 * @returns
 */
export const formatTimeLengthUnit = (timeLength: number) => {
    const isMonth = timeLength <= 9;
    const time = isMonth ? timeLength : timeLength / 12;
    return {
        timeLength: time,
        timeUnit: isMonth ? 'month' : 'year'
    };
};

// 自动续费周期
export const getPayLoop = (renewTime: number, renewTimeUnit: string) => {
    return '每' + renewTime + (renewTimeUnit.toLowerCase() === 'month' ? '月' : '年');
};

export const setDisplay = (bool: boolean) => {
    return bool ? '' : 'display:none;';
};

export const isPrepaid = (payment: string) => payment === PaymentType.PREPAID;

/**
 * 渲染单个可用区展示label，用于创建集群-信息配置
 * eg：'cn-bj-a' ——》可用区A
 * @param label
 * @returns
 */
export const renderZoneLabel = (label: string) => {
    const arr = label.split('-');
    return !arr.length ? label : `可用区${arr[arr.length - 1].toUpperCase()}`;
};

/**
 * 渲染数组类型的可用区，
 * eg: ['cn-bj-a', 'cn-bj-b'] ——》可用区A、B
 * @param logicalZones
 * @returns
 */
export const renderArrZoneLabel = (logicalZones: string[]) => {
    const zonesStr = _.map(logicalZones, zone => {
        const arr = zone.split('-');
        return !arr.length ? zone : `${arr[arr.length - 1].toUpperCase()}`;
    }).join('、');
    return `可用区${zonesStr}`;
};

/**
 * 处理选择的可用区按展示顺序排序后的值
 * @param data
 * @returns
 */
export const formatSelectedOrderZones = (data: {logicalZones: string[]; zoneList: ZoneListItem[]}) => {
    const {logicalZones = [], zoneList} = data || {};
    if (logicalZones.length <= 1) {
        return logicalZones;
    }
    return zoneList.filter(item => logicalZones.includes(item.value)).map(item => item.value);
};

/**
 * 根据表单（部署方式、可用区），获取到传给后端的可用区
 * @param {string} mode 部署方式：单可用区 / 多可用区
 * @param {string | string[]} logicalZone 可用区表单值
 * @returns
 */
export const getLogicalZonesByFormData = (mode: string, logicalZone: string | string[]) => {
    return mode === ModeList.HA ? logicalZone : [logicalZone];
};

/**
 * 根据后端认证方式值展示
 * @param authenticationMode
 * @returns
 */
export const renderAuthentictionMode = (authenticationMode: AuthenticationModeType) => {
    return AuthenticationModeList.fromValue(authenticationMode).label;
};

/**
 * 根据表单（认证方式），获取到传给后端的认证方式
 * @param {string} authenticationMode 认证方式
 * @returns
 */
export const getAuthenticationModeByFormData = (authenticationMode: AuthenticationModeType) => {
    return [authenticationMode];
};

/**
 * 是否未开启身份认证
 * @param {AuthenticationModeType[]} authenticationMode 认证方式
 * @returns {boolean} 是否未开启身份认证
 */
export const isAuthenticationModeNone = (authenticationMode: AuthenticationModeType[]) => {
    return authenticationMode[0] === AuthenticationModeType.None;
};

/**
 * 计算组内节点数
 * 1.dledger：组内节点数
 * 2.master-slave：主从比例相加
 */
export const computeNumberOfNodesPerBroker = (data: {
    arch: string;
    /** dledger架构组内节点数 */
    dledgerNumberOfNodesPerBroker: number;
    /** master-slave架构主从比例 */
    masterSlaveNumberOfNodesPerBroker: number;
}) => {
    const {arch, dledgerNumberOfNodesPerBroker, masterSlaveNumberOfNodesPerBroker} = data;
    return arch === ArchList.DLEDGER ? dledgerNumberOfNodesPerBroker : 1 + masterSlaveNumberOfNodesPerBroker;
};

// 总节点数量计算
export const computeNumberOfBrokerNodes = (formData: {
    /** 节点组数量 */
    numberOfBrokers: number;
    /** 部署架构 */
    arch: string;
    /** dledger架构组内节点数 */
    dledgerNumberOfNodesPerBroker: number;
    /** master-slave架构主从比例 */
    masterSlaveNumberOfNodesPerBroker: number;
}) => {
    const {numberOfBrokers, arch, dledgerNumberOfNodesPerBroker, masterSlaveNumberOfNodesPerBroker} = formData;
    const brokerNum = computeNumberOfNodesPerBroker({
        arch,
        dledgerNumberOfNodesPerBroker,
        masterSlaveNumberOfNodesPerBroker
    });
    return brokerNum * numberOfBrokers;
};

export const formatStorageType = (storageType: string) => DiskType.getTextFromValue(storageType);

export const formatStorageSize = (data: {storageSize: number; numberOfDisks: number; storageType: string}) => {
    const {storageSize, numberOfDisks} = data;
    const storageMetaTotal = storageSize * numberOfDisks;
    return numberOfDisks > 1
        ? `${storageMetaTotal}${Units.GB}（${storageSize}${Units.GB} × ${numberOfDisks}）`
        : `${storageMetaTotal}${Units.GB}`;
};

export const formatTotalStorageSize = (data: {
    storageSize: number;
    numberOfDisks: number;
    storageType?: string;
    numberOfBrokerNodes: number;
}) => {
    const {storageSize, numberOfDisks, numberOfBrokerNodes} = data;
    const storageMetaTotal = storageSize * numberOfDisks;
    return `${storageMetaTotal * (numberOfBrokerNodes || 0)}${Units.GB}`;
};

export const formatPrice = (price: number, payment: Payment, timeLength: number) => {
    const {timeUnit, timeLength: newTimeLength} = formatTimeLengthUnit(timeLength);
    const priceStr = `¥${price || 0}`;
    const unit = PriceUnit[payment];
    const paymentStr =
        payment === Payment.Postpaid ? `/${unit}` : `/${newTimeLength}${timeUnit === 'month' ? '个月' : '年'}`;

    return priceStr + paymentStr;
};

export const formatComputePrice = (price: number) => {
    const dayInMonth = dayjs().daysInMonth();
    const cell = price * 60 * 24;
    return `(预计¥${cell.toFixed(2)}/天 ¥${(cell * dayInMonth).toFixed(1)}/月)`;
};

/**
 * 处理部署架构展示：用于确认订单页 和 集群详情页
 * @param data
 * @returns
 */
export const formatArch = (arch: string) => ArchList.getTextFromValue(arch);

/**
 * 架构提示文案
 * @param arch 部署架构
 * @param numberOfNodesPerBroker 组内节点数
 */
export const formatArchHelpTip = (arch: ArchType, numberOfNodesPerBroker: number) => {
    if (arch === ArchType.DLEDGER) {
        return `DLedger 多副本架构，组内节点数为${numberOfNodesPerBroker}个`;
    }

    return `Master-Slave主从异步架构，组内节点数为${numberOfNodesPerBroker}个`;
};
/**
 * 处理部署方式展示：用于确认订单页、集群列表、集群详情页
 * @param data
 * @returns
 */
export const formatMode = (data: {mode: string; selectedOrderZones: string[]}) => {
    const {mode, selectedOrderZones} = data;
    return `${ModeList.getTextFromValue(mode)}（${renderArrZoneLabel(selectedOrderZones)}）`;
};

export const formatFlushDiskType = (type: string) => FlushList.getTextFromValue(type);

export const formatArchFlush = (data: {
    arch: string;
    flushDiskType?: string;
    dledgerNumberOfNodesPerBroker?: number;
    masterSlaveNumberOfNodesPerBroker?: number;
}) => {
    const {arch, flushDiskType, dledgerNumberOfNodesPerBroker, masterSlaveNumberOfNodesPerBroker} = data;
    const isMasterSlave = arch === ArchList.MASTER_SLAVE;
    const extraText = isMasterSlave
        ? `${formatFlushDiskType(flushDiskType as string)}，1：${masterSlaveNumberOfNodesPerBroker}`
        : dledgerNumberOfNodesPerBroker;
    return `${formatArch(arch)}（${extraText}）`;
};

export const formatRunningTime = (runningTime: {day: number; hour: number; minute: number; second: number}) => {
    return (
        (runningTime?.day ? runningTime.day + TimeUnit.day : '') +
        (runningTime?.hour ? runningTime.hour + TimeUnit.hour : '') +
        (runningTime?.minute ? runningTime.minute + TimeUnit.minute : '') +
        (runningTime?.second ? runningTime.second + TimeUnit.second : '')
    );
};

export const isSsLPermissive = (encryptionInTransit?: EncryptionInTransitType[]) => {
    if (!encryptionInTransit?.length) {
        return false;
    }

    return encryptionInTransit.some(item => {
        return [EncryptionInTransitType.SSL, EncryptionInTransitType.Permissive].includes(item);
    });
};

export const downloadSSL = async ({api, fileName = 'rocketmq-key.zip'}: {api: string; fileName?: string}) => {
    let xhr = new XMLHttpRequest();
    xhr.open('GET', api);
    xhr.setRequestHeader('Content-Type', 'application/octet-stream');
    xhr.setRequestHeader('X-Region', window.$context.getCurrentRegion().id);
    xhr.setRequestHeader('csrfToken', window.$context.$cookie.get('bce-user-info'));
    xhr.responseType = 'blob';
    xhr.onload = function (e: Event) {
        if (this.status === 200) {
            const a = document.createElement('a');
            a.href = window.URL.createObjectURL(this.response);
            a.download = fileName;
            a.dispatchEvent(new MouseEvent('click'));
        } else {
            Notification.error(`下载报错：${JSON.stringify(e)}`);
        }
    };
    xhr.onerror = function (e) {
        Notification.error(`下载报错：${JSON.stringify(e)}`);
    };
    xhr.send(JSON.stringify({}));
};

/**
 * 根据全量表格columns配置，拿到自定义列组件的datasource和value
 * @param toggleTableColumns 全量表格columns配置
 * @returns {Object}
 */
export const formatToggleColumnData = (toggleTableColumns: ToggleTableColumn[]) => {
    let value: string[] = [];
    const datasource = toggleTableColumns.map(item => {
        if (item.disabled || item.defaultShow) {
            value.push(item.name);
        }
        return {
            text: `${item.label}${item.disabled ? '（默认）' : ''}`,
            value: item.name,
            disabled: item.disabled
        };
    });
    return {
        datasource,
        value
    };
};

/**
 * 根据表格当前展示columns和全量表格columns配置、自定义列当前选中的value值，过滤出表格展示的columns
 * @param columns
 * @param toggleTableColumns
 * @param value
 */
export const filterTableColumns = (data: {
    columns: ToggleTableColumn[];
    TableColumns: ToggleTableColumn[];
    value: string[];
}) => {
    const {columns, TableColumns, value} = data;

    return _.filter(TableColumns, item => value.indexOf(item.name) > -1).map(item => {
        const column = columns.find(c => c.name === item.name);
        // 如果已存在该列，则从当前columns中获取，否则再从全量columns配置中获取
        return column || item;
    });
};

// 获取主题的权限 理论取值范围为0~7，但接口中用到的实际取值只有2，4，6，分别对应可写，可读，可读写。
// 理论上我们只展示用户创建的主题，而用户创建时权限只能输入2，4，6，所以后端返回值只会有2，4，6。但是为了保险起见，建议将其他数值展示为“其他”。
export const formatTopicPermission = (permission: string) =>
    ClusterTopicPermissionTypeList.getTextFromValue(permission) || '其他';

export const formatTopicPerm = (permValue: IPermissionType) => {
    return TopicPerssions.getTextFromValue(permValue) || permValue;
};

export const formatGroupPerm = (permValue: IPermissionType) => {
    return ConsumerGroupPerssions.getTextFromValue(permValue) || permValue;
};

export const formatClusterItem = (row: IClusterItem | IClusterDetail): IFeClusterItem | IFeClusterDetail => {
    const isActive = row.status === ClusterStatusType.ACTIVE;
    return {
        ...row,
        canUpdate: isActive,
        isAllUpdateMax: row.isBrokerNodeTypeMax && row.isBrokerDiskCapacityMax,
        canUpdatePayment: isActive && row.payment === Payment.Postpaid
    };
};

/**
 * 集群状态处于非Active状态时，禁用集群的一些操作
 * 主题管理：新建、编辑、删除
 * 消费组管理：新建、编辑、删除
 * 用户管理：新建、编辑、删除、更多操作
 * @param {IClusterDetail} detail 集群详情
 * @returns {boolean} 是否禁用
 */
export const isClusterStatusDisabled = (detail: IClusterDetail) => {
    if (!detail) {
        return true;
    }
    return detail.status !== ClusterStatusType.ACTIVE;
};
