/**
 * RocketMQ接口
 * @file biz.ts
 * <AUTHOR>
 */

import {BaseClient} from './base';
import {Base64} from 'js-base64';
import {IMessageList} from '@common/client/types/message';
import {serializeQuery} from '@common/utils';
import {ILogContent, ILogList} from './types/log';
import {INodeList} from './types/cluster';
export const API_PREFIX = '/api/rocketmq/v1';
// export const API_PREFIX = `//iapi.baidu-int.com/m1/360106-0-default/api/rocketmq/v1`;

interface IParams {
    /** 拼接到path当中 */
    [prop: string]: any;
    /** 作为params传参 */
    params?: {
        [prop: string]: any;
    };
}

class Client extends BaseClient {
    // 创建集群
    clusterCreate(param: Object, options: Object = {}) {
        return this.post(`${API_PREFIX}/clusters`, param, options);
    }
    // 集群列表
    clusterList(param: Object, options: Object = {}) {
        return this.get(`${API_PREFIX}/clusters`, param, options);
    }

    // 删除集群
    clusterDelete(p: IParams, options: Object = {}) {
        const {params, clusterId} = p;
        return this.delete(`${API_PREFIX}/clusters/${clusterId}`, params, options);
    }

    // 停止集群
    clusterStop(p: IParams, options: Object = {}) {
        const {params, clusterId} = p;
        return this.put(`${API_PREFIX}/clusters/${clusterId}/stop`, params, options);
    }
    // 启动集群
    clusterStart(p: IParams, options: Object = {}) {
        const {params, clusterId} = p;
        return this.put(`${API_PREFIX}/clusters/${clusterId}/start`, params, options);
    }

    // 创建集群-集群版本列表
    listAvailableVersion(param: Object, options: Object = {}) {
        return this.get(`${API_PREFIX}/clusters/versions`, param, options);
    }

    // 编辑标签
    tagAssign(param: Object, options: Object = {}) {
        return this.post(`${API_PREFIX}/tags/assign`, param, options);
    }

    // 查看接入点信息
    getAccessPoints(p: IParams, options: Object = {}) {
        const {clusterId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/access-endpoints`, params, options);
    }

    // 下载证书
    certs = (clusterId: string) => `${API_PREFIX}/clusters/${clusterId}/certs`;

    // 检查集群是否重名
    checkClusterName(params: object, options: Object = {}) {
        return this.post(`${API_PREFIX}/clusters/check-name`, params, options);
    }

    // 可用区列表
    zoneList(params: object, options: Object = {}) {
        return this.get(`${API_PREFIX}/clusters/zones`, params, options);
    }

    // 套餐列表
    getFlavors(params: object = {}, options: Object = {}) {
        return this.post(`${API_PREFIX}/clusters/flavors`, params, options);
    }

    // 用户白名单/api/rocketmq/users/acls
    getUserWhiteListAcl(params: object = {}, options: Object = {}) {
        return this.post(`${API_PREFIX}/users/acls`, params, options);
    }

    // 创建集群-可用vpc
    vpcList(param: object = {}, options: object = {}) {
        return this.get(`${API_PREFIX}/clusters/vpcs`, param, options);
    }

    // 创建集群-可用的子网和可用区
    subnetList(p: IParams, options: Object = {}) {
        const {vpcId, params} = p;
        return this.post(`${API_PREFIX}/clusters/vpcs/${vpcId}/subnets`, params, options);
    }

    // 创建集群-安全组列表
    securityGroupList(p: IParams, options: Object = {}) {
        const {vpcId, params} = p;
        return this.get(`${API_PREFIX}/clusters/vpcs/${vpcId}/security-groups`, params, options);
    }

    // 创建集群—获取价格
    getPrices(params: object = {}, options: object = {}) {
        return this.post(`${API_PREFIX}/clusters/prices`, params, options);
    }

    // 检查集群资源：bcc 资源、子网 ip 资源、安全组规则、配置项
    checkResources(params: object = {}, options: object = {}) {
        return this.post(`${API_PREFIX}/clusters/check-resources`, params, options);
    }

    // 创建集群-确认订单
    createCluster(params: object = {}, options: object = {}) {
        return this.post(`${API_PREFIX}/clusters`, params, options);
    }

    // 集群详情
    clusterDetail(p: IParams, options: object = {}) {
        const {clusterId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}`, params, options);
    }

    // 集群节点
    clusterNodes(p: IParams, options: object = {}): Promise<INodeList> {
        const {clusterId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/nodes`, params, options);
    }

    // 集群配置
    clusterConfigurations(p: IParams, options: object = {}) {
        const {clusterId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/configurations`, params, options);
    }

    // 节点组列表
    monitorBrokers(p: IParams, options: object = {}) {
        const {clusterId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/monitor/brokers`, params, options);
    }

    // 节点组列表
    monitorNodes(p: IParams, options: object = {}) {
        const {clusterId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/monitor/nodes`, params, options);
    }

    // 主题列表
    monitorTopics(p: IParams, options: object = {}) {
        const {clusterId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/monitor/topics`, params, options);
    }

    // 消费组列表
    monitorConsumerGroups(p: IParams, options: object = {}) {
        const {clusterId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/monitor/consumergroups`, params, options);
    }

    // 消费组订阅的主题列表
    monitorGroupTopics(p: IParams, options: object = {}) {
        const {clusterId, groupName, params} = p;
        const url = `${API_PREFIX}/clusters/${clusterId}/monitor/consumergroups/${groupName}/topics`;
        return this.get(url, params, options);
    }

    // 监控指标项
    monitorItems(params: object = {}, options: object = {}) {
        return this.get(`${API_PREFIX}/clusters/monitor/monitor-items`, params, options);
    }

    // 监控磁盘列表
    monitorSubDimensions(p: IParams, options: object = {}) {
        const {clusterId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/monitor/monitor-dimensions`, params, options);
    }

    // 任务列表
    actionList(p: IParams = {}, options: object = {}) {
        const {clusterId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/actions`, params, options);
    }

    // 任务操作列表
    operationList(p: IParams = {}, options: object = {}) {
        const {clusterId, actionId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/actions/${actionId}/operations`, params, options);
    }

    // 任务操作详情
    operationDetail(p: IParams = {}, options: object = {}) {
        const {clusterId, actionId, operationId, params} = p;
        return this.get(
            `${API_PREFIX}/clusters/${clusterId}/actions/${actionId}/operations/${operationId}`,
            params,
            options
        );
    }

    // 集群任务操作stage组信息
    operationKinds(p: IParams = {}, options: object = {}) {
        const {clusterId, actionId, operationId, params, groupName} = p;
        const url = `${API_PREFIX}/clusters/${
            clusterId
        }/actions/${actionId}/operations/${operationId}/groups/${groupName}`;
        return this.get(url, params, options);
    }

    /** ---主题 start------ */
    // 主题列表
    getTopicList(
        p: {
            clusterId: string;
            params: {
                pageNo: number;
                pageSize: number;
                topicName?: string;
            };
        },
        options: object = {}
    ) {
        const {clusterId, params} = p;
        return this.get(
            `${API_PREFIX}/clusters/${clusterId}/topics`,
            {
                ...params,
                ...(params.topicName ? {topicName: Base64.encodeURI(params.topicName)} : {})
            },
            options
        );
    }
    // 创建主题
    topicCreate(p: IParams = {}, options: object = {}) {
        const {clusterId, params} = p;
        return this.post(`${API_PREFIX}/clusters/${clusterId}/topics`, params, options);
    }
    // 删除主题
    topicDelete(p: IParams = {}, options: object = {}) {
        const {clusterId, topicNames} = p;
        return this.delete(
            `${API_PREFIX}/clusters/${clusterId}/topics`,
            {
                topicNames: topicNames.map((topicNames: string) => Base64.encodeURI(topicNames))
            },
            options
        );
    }
    // 更新主题
    topicUpdate(p: IParams = {}, options: object = {}) {
        const {clusterId, topicName, params} = p;
        return this.put(`${API_PREFIX}/clusters/${clusterId}/topics/${Base64.encodeURI(topicName)}`, params, options);
    }
    // 获取主题详情
    getTopicDetail(p: IParams = {}, options: object = {}) {
        const {clusterId, topicName, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/topics/${Base64.encodeURI(topicName)}`, params, options);
    }
    // 获取主题队列列表
    getTopicQueuesList(p: IParams = {}, options: object = {}) {
        const {clusterId, topicName, params} = p;
        return this.get(
            `${API_PREFIX}/clusters/${clusterId}/topics/${Base64.encodeURI(topicName)}/queues`,
            params,
            options
        );
    }
    // 获取主题消费组列表
    getTopicConsumerGroupsList(p: IParams = {}, options: object = {}) {
        const {clusterId, topicName, params} = p;
        return this.get(
            `${API_PREFIX}/clusters/${clusterId}/topics/${Base64.encodeURI(topicName)}/consumer-groups`,
            params,
            options
        );
    }
    // 获取主题消费组的队列消费信息
    getTopicConsumerGroupsQueue(p: IParams = {}, options: object = {}) {
        const {clusterId, topicName, groupName, params} = p;
        const prefix = `${API_PREFIX}/clusters/${clusterId}/topics/${Base64.encodeURI(topicName)}`;
        return this.get(`${prefix}/consumer-groups/${Base64.encodeURI(groupName)}/queues`, params, options);
    }
    /** ---主题 end------ */

    /** ----------------------消费组管理相关------------------------- */
    // 获取消费组列表
    consumerGroupsList(
        p: {
            clusterId: string;
            params: {
                pageNo?: number;
                pageSize?: number;
                groupName?: string;
            };
        },
        options: object = {}
    ) {
        const {clusterId, params} = p;
        return this.get(
            `${API_PREFIX}/clusters/${clusterId}/consumer-groups`,
            {
                ...params,
                ...(params.groupName ? {groupName: Base64.encodeURI(params.groupName)} : {})
            },
            options
        );
    }

    // 批量删除消费组
    deleteConsumerGroups(p: IParams = {}, options: object = {}) {
        const {clusterId, groupNames} = p;
        return this.delete(
            `${API_PREFIX}/clusters/${clusterId}/consumer-groups`,
            {
                groupNames: groupNames.map((groupName: string) => Base64.encodeURI(groupName))
            },
            options
        );
    }

    // 创建消费组
    createConsumerGroup(p: IParams = {}, options: object = {}) {
        const {clusterId, params} = p;
        return this.post(`${API_PREFIX}/clusters/${clusterId}/consumer-groups`, params, options);
    }

    // 更新消费组
    updateConsumerGroup(p: IParams = {}, options: object = {}) {
        const {clusterId, params, groupName} = p;
        return this.put(
            `${API_PREFIX}/clusters/${clusterId}/consumer-groups/${Base64.encodeURI(groupName)}`,
            params,
            options
        );
    }

    // 获取消费组详情
    getConsumerGroupDetail(p: IParams = {}, options: object = {}) {
        const {clusterId, params = {}, groupName} = p;
        return this.get(
            `${API_PREFIX}/clusters/${clusterId}/consumer-groups/${Base64.encodeURI(groupName)}`,
            params,
            options
        );
    }

    // 获取消费者客户端列表
    getConsumerGroupClients(p: IParams = {}, options: object = {}) {
        const {clusterId, params = {}, groupName} = p;
        return this.get(
            `${API_PREFIX}/clusters/${clusterId}/consumer-groups/${Base64.encodeURI(groupName)}/clients`,
            params,
            options
        );
    }

    // 获取消费组订阅主题列表
    getConsumerGroupTopics(p: IParams = {}, options: object = {}) {
        const {clusterId, params, groupName} = p;
        return this.get(
            `${API_PREFIX}/clusters/${clusterId}/consumer-groups/${Base64.encodeURI(groupName)}/topics`,
            params,
            options
        );
    }

    // 获取消费组订阅关系列表
    getConsumerGroupSubscriptions(p: IParams = {}, options: object = {}) {
        const {clusterId, params, groupName} = p;
        return this.get(
            `${API_PREFIX}/clusters/${clusterId}/consumer-groups/${Base64.encodeURI(groupName)}/subscriptions`,
            params,
            options
        );
    }

    // 获取主题订阅组的队列消费信息
    getConsumerGroupQueues(p: IParams = {}, options: object = {}) {
        const {clusterId, params, groupName, topicName} = p;
        return this.get(
            `${API_PREFIX}/clusters/${clusterId}/topics/${Base64.encodeURI(topicName)}/consumer-groups/${Base64.encodeURI(groupName)}/queues`,
            params,
            options
        );
    }
    /** ---------------------------结束----------------------------- */
    /** ------- 用户和权限管理接口 ------- start */
    // acl用户列表
    aclUserList(p: IParams = {}) {
        const {clusterId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/acl/users`, params);
    }
    // 删除用户
    deleteAclUser(p: IParams = {}) {
        const {clusterId, accessKey} = p;
        return this.delete(`${API_PREFIX}/clusters/${clusterId}/acl/users/${accessKey}`);
    }
    // 重置密码
    resetAclPassword(p: IParams = {}) {
        const {clusterId, accessKey, params} = p;
        return this.put(`${API_PREFIX}/clusters/${clusterId}/acl/users/${accessKey}/password`, params);
    }
    // 更新用户
    changeAclUser(p: IParams = {}) {
        const {clusterId, accessKey, params} = p;
        return this.put(`${API_PREFIX}/clusters/${clusterId}/acl/users/${accessKey}`, params);
    }
    // 创建用户
    createAclUser(p: IParams = {}) {
        const {clusterId, params} = p;
        return this.post(`${API_PREFIX}/clusters/${clusterId}/acl/users`, params);
    }
    // 获取用户信息
    getAclUser(p: IParams = {}) {
        const {clusterId, accessKey} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/acl/users/${accessKey}`);
    }

    // 获取ACL用户主题权限列表
    aclUserTopicPerm(p: IParams = {}) {
        const {clusterId, params, accessKey} = p;
        if (params?.keyword?.length) {
            params.keyword = params.keyword.map((item: string) => encodeURI(item));
        }
        return this.get(`${API_PREFIX}/clusters/${clusterId}/acl/users/${accessKey}/topic-perm`, params);
    }

    // 更新ACL用户主题权限列表
    updateAclUserTopicPerm(p: IParams = {}) {
        const {clusterId, params, accessKey} = p;
        return this.put(`${API_PREFIX}/clusters/${clusterId}/acl/users/${accessKey}/topic-perm`, params);
    }
    // 获取ACL用户消费组权限列表
    aclUserGroupPerm(p: IParams = {}) {
        const {clusterId, params, accessKey} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/acl/users/${accessKey}/group-perm`, params);
    }

    // 更新ACL用户消费组权限列表
    updateAclUserGroupPerm(p: IParams = {}) {
        const {clusterId, params, accessKey} = p;
        return this.put(`${API_PREFIX}/clusters/${clusterId}/acl/users/${accessKey}/group-perm`, params);
    }

    // 获取拥有指定主题权限的ACL用户列表
    getTopicUsers(p: IParams = {}) {
        const {clusterId, params, topicName} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/topics/${Base64.encodeURI(topicName)}/users`, params);
    }
    // 获取拥有指定消费组权限的ACL用户列表
    getConsumerUsers(p: IParams = {}) {
        const {clusterId, params, groupName} = p;
        return this.get(
            `${API_PREFIX}/clusters/${clusterId}/consumer-groups/${Base64.encodeURI(groupName)}/users`,
            params
        );
    }
    /** ------- 用户和权限管理接口 ------- end */
    // 节点扩容
    scaleBrokerNumber(p: IParams = {}, options: object = {}) {
        const {clusterId, params} = p;
        return this.put(`${API_PREFIX}/clusters/${clusterId}/brokers/broker-count`, params, options);
    }
    // 节点扩容询价
    scaleBrokerNumberPrice(p: IParams = {}, options: object = {}) {
        const {clusterId, params} = p;
        return this.post(`${API_PREFIX}/clusters/${clusterId}/prices/broker-count`, params, options);
    }
    // 节点升配
    scaleNodeType(p: IParams = {}, options: object = {}) {
        const {clusterId, params} = p;
        return this.put(`${API_PREFIX}/clusters/${clusterId}/brokers/node-type`, params);
    }
    // 节点升配询价
    scaleNodeTypePrice(p: IParams = {}, options: object = {}) {
        const {clusterId, params} = p;
        return this.post(`${API_PREFIX}/clusters/${clusterId}/prices/node-type`, params, options);
    }
    // 磁盘扩容
    scaleDisk(p: IParams = {}, options: object = {}) {
        const {clusterId, params} = p;
        return this.put(`${API_PREFIX}/clusters/${clusterId}/brokers/disk-capacity`, params, options);
    }
    // 磁盘扩容询价
    scaleDiskPrice(p: IParams = {}, options: object = {}) {
        const {clusterId, params} = p;
        return this.post(`${API_PREFIX}/clusters/${clusterId}/prices/disk-capacity`, params, options);
    }

    /** 后付费转预付费 */
    switchToPrepaid(p: IParams = {}, options: object = {}) {
        const {clusterId, params} = p;
        return this.put(`${API_PREFIX}/clusters/${clusterId}/orders/to-prepay`, params, options);
    }
    // 节点重启接口
    restartClusterNode(p: IParams = {}, options: object = {}) {
        const {clusterId, params} = p;
        return this.put(`${API_PREFIX}/clusters/${clusterId}/brokers/restart`, params, options);
    }
    // 获取日志文件列表
    getLogFiles(p: IParams = {}, options: object = {}): Promise<ILogList> {
        const {clusterId, serviceId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/brokers/${serviceId}/logs`, params, options);
    }
    // 获取日志文件内容
    getLogFileContent(p: IParams = {}, options: object = {}): Promise<ILogContent> {
        const {clusterId, serviceId, filename, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/brokers/${serviceId}/logs/${filename}`, params, options);
    }
    // 发送消息接口
    messageSend(p: IParams = {}, options: object = {}) {
        const {clusterId, topicName, params} = p;
        return this.post(
            `${API_PREFIX}/clusters/${clusterId}/topics/${Base64.encodeURI(topicName)}/messages`,
            params,
            options
        );
    }
    // 集群消费组相关-重置位点
    resetClusterOffset(p: IParams = {}, options: object = {}) {
        const {clusterId, groupName, params} = p;
        const query = serializeQuery(params || {});
        return this.put(
            `${API_PREFIX}/clusters/${clusterId}/consumer-groups/${Base64.encodeURI(groupName)}/offset?${query}`,
            options
        );
    }

    // 查询消息列表
    listMessages(p: IParams = {}, options: object = {}): Promise<IMessageList> {
        const {clusterId, params, queryItems} = p;
        const query = serializeQuery(queryItems);
        return this.post(`${API_PREFIX}/clusters/${clusterId}/messages?${query}`, params, options);
    }

    // 查询消息详情
    getMessage(p: IParams = {}, options: object = {}) {
        const {clusterId, messageId, params} = p;
        return this.get(`${API_PREFIX}/clusters/${clusterId}/messages/${messageId}`, params, options);
    }
}

export const api = new Client();
