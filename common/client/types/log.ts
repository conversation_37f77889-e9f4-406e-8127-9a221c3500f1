export interface ILogListItem {
    /**
     * 文件名
     */
    filename: string;
    /**
     * 文件最后修改时间
     */
    modifyTime: number;
    /**
     * 文件大小
     */
    totalSize: number;
    [property: string]: any;
}

export type ILogList = ILogListItem[];

/**
 * 当前页结果，当前页的结果
 */
export interface ILogContent {
    pageNo: number;
    pageSize: number;
    totalSize: number;
    result: {
        /**
         * 文件内容
         */
        content: string;
        /**
         * 文件内容结束偏移
         */
        endOffset: number;
        /**
         * 日志文件名
         */
        filename: string;
        /**
         * 日志文件最后修改时间
         */
        modifyTime: number;
        /**
         * 文件内容起始偏移
         */
        startOffset: number;
        [property: string]: any;
    };
}
