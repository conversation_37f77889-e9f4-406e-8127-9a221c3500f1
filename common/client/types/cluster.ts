/**
 * 节点列表信息
 */
export interface INodeItem {
    /**
     * 所属节点组
     */
    brokerId: string;
    /**
     * 节点组角色
     */
    brokerRole: string;
    /**
     * CPU 使用率
     */
    cpuUsedPercent: number;
    /**
     * 磁盘使用率
     */
    dataDiskUsedPercent: string;
    /**
     * 实例 ID
     */
    instanceId: string;
    /**
     * 可用区
     */
    logicalZone: string;
    /**
     * 内存使用率
     */
    memUsedPercent: string;
    /**
     * 节点 ID，正整数
     */
    nodeId: number;
    /**
     * 启动时长
     */
    runningTime: string;
    /**
     * 节点状态
     */
    status: string;
    [property: string]: any;
}

export type INodeList = ListPage<INodeItem>;
