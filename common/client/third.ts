/**
 * 第三方产品接口
 */
import {BaseClient} from './base';

class ThirdClient extends BaseClient {
    // iam对应接口
    // 判断激活接口
    iamStsRoleQuery(param: Object, options?: Object) {
        return this.post('/api/iam/sts/role/query', param, options);
    }
    // 激活接口
    iamStsRoleActivate(param: Object, options?: Object) {
        return this.post('/api/iam/sts/role/activate', param, options);
    }
    // 用户信息接口
    iamAccountDetail(param?: Object, options?: Object) {
        return this.post('/api/iam/account/detail', param, options);
    }
    // 获取标签列表
    getSearchTagList(param: Object, options = {}) {
        return this.post('/api/tag/list', param, options);
    }

    // 获取代金券列表
    getCouponList(param: Object, options: Object = {}) {
        return this.post('/api/coupon/avail', param, options);
    }

    // 激活代金券
    activeCoupon(param: Object, options: Object = {}) {
        return this.post('/api/coupon/activate', param, options);
    }

    // 监控数据接口
    bcmMetricData(param: Object, options: Object = {}) {
        return this.post('/api/bcm/v2/csm/data/metricAllData', param, options);
    }
}

export const thirdApi = new ThirdClient();
