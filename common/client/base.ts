/**
 * client.js
 *
 * @file client.js
 * <AUTHOR>
 */

import {ServiceFactory} from '@baiducloud/runtime';
import HttpClient from '@baiducloud/httpclient';

export class BaseClient extends HttpClient {
    /**
     * RestClient的delete方法不支持body传参，单独封装
     *
     * @param {*} url 请求url
     * @param {*} data 请求数据
     * @param {*} config 额外配置
     */
    constructor() {
        // const config = {data: {}, CSRFToken: !!$flag.CommonCSRFToken};
        super({}, ServiceFactory.resolve('$context'));
    }
}

export const baseClient = new BaseClient();
