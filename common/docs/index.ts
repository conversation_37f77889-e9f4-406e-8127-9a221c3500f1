/**
 * 文档链接
 *
 * @file index.ts
 * <AUTHOR>
 */

import {ServiceFactory} from '@baiducloud/runtime';
import {isSandbox} from '@common/config';

// 这里配置path即可！！！！！！！！！！！！
// 私有化交付时，文档服务的域名会不同，下面的DocService会为业务动态渲染环境中的文档服务域名
export let DOC_LINK = {
    rocketmqIntroduction: '',
    autoRenew: '/doc/Finance/s/gjwvysrlu',
    brokerDeploySet: '',

    // 第三方产品文档
    // vpc产品介绍
    vpcIntrduction: '/doc/VPC/s/Vjwvytu2v',
    // blb产品介绍
    blbIntroduction: '/doc/BLB/s/Ajwvxno34',
    // eip产品介绍
    eipIntroduction: '/doc/EIP/s/fjwvz2pyz',
    // 用户服务协议
    userServiceAgreement: '/doc/Agreements/s/yjwvy1x03'
};
try {
    ServiceFactory.register('$doc', ServiceFactory.create('$doc', DOC_LINK));
    DOC_LINK = ServiceFactory.resolve('$doc') as typeof DOC_LINK;
} catch (e) {
    console.error('无法注册$doc');
}

// 提交工单链接
export const TICKET_LINK = `https://${isSandbox ? 'qasandbox.bcetest.baidu.com' : 'console.bce.baidu.com'}/support/#/ticket/#/ticket/create?productId=302`;

// 订购协议链接
export const AgreementLink = '//console.bce.baidu.com/iam/agreement-v2.html';
