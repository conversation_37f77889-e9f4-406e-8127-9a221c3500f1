/**
 * 路由相关配置
 * <AUTHOR>
 */

import {Payment} from '@common/enums';

const PREFIX = 'rocketmq';
// 路由路径
export const ROUTE_PATH = {
    activate: `/${PREFIX}/activate`,
    clusterList: `/${PREFIX}/cluster/list`,
    clusterCreate: `/${PREFIX}/cluster/create`,
    clusterDetail: `/${PREFIX}/cluster/detail`,
    clusterTopic: `/${PREFIX}/cluster/topic`,
    clusterConsumer: `/${PREFIX}/cluster/consumer`,
    clusterUserAcl: `/${PREFIX}/cluster/user-acl`,
    clusterMonitor: `/${PREFIX}/cluster/monitor`,
    clusterActionList: `/${PREFIX}/cluster/action/list`,
    clusterLog: `/${PREFIX}/cluster/log`,
    clusterScale: `/${PREFIX}/cluster/scale`,
    clusterPaymentUpdate: `/${PREFIX}/cluster/payment/update`,
    clusterMessageList: `/${PREFIX}/cluster/message/list`
};

/** url的from参数：代表从哪个页面跳转过来 */
export enum IUrlFromValue {
    List = 'list',
    Detail = 'detail'
}

/** from参数对应的返回url */
export const FromMapUrl = {
    [IUrlFromValue.List]: `${ROUTE_PATH.clusterList}`,
    [IUrlFromValue.Detail]: `${ROUTE_PATH.clusterDetail}`
};

export const THIRD_ROUTE_PATH = {
    couponList: '/finance/#/finance/coupon/list',
    kafkaClusterCreate: '/kafka/#/kafka/cluster/create',
    rabbitMQClusterCreate: '/rabbitmq/#/rabbitmq/create',
    tagList: '/tag/#/tag/instance/list'
};

export const ClusterCreateSuccUrl = {
    [Payment.Prepaid]: '/finance/#/finance/pay~fromService=',
    [Payment.Postpaid]: '/billing/#/order/success~fromService='
};
