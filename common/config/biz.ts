/**
 * RocketMQ业务相关配置
 * @file biz.ts
 * <AUTHOR>
 */

import {Payment} from '@common/enums';

export const ServiceType = 'RocketMQ';

export const isSandbox = location.hostname.includes('qasandbox');

export const RoleName = 'BceServiceRole_ROCKETMQ';
// 线上激活用户相关信息
export const OnlineRoleInfo = {
    roleName: RoleName,
    serviceId: 'd33f5df4100b48138aa1d68b110f22e0',
    policyId: '55772a8d86e64a34accbc055dd6a8437',
};

// 沙盒激活用户相关信息
export const SanboxRoleInfo = {
    roleName: RoleName,
    serviceId: '2b176c3021004ad7927f6766a9c37557',
    policyId: '6f57ff9c8e1e4e67ad06c897e038440d',
};

// 一些单位
export const Units = {
    Mbps: 'Mbps',
    GB: 'GB',
};

export const TimeUnit = {
    day: '天',
    hour: '小时',
    minute: '分钟',
    second: '秒',
};

// 磁盘大小限制
export const DiskSizeLimit = {
    min: 100,
    max: 32000,
};

export const PublicIpWidthSliderMarks = {
    1: '1Mbps',
    250: '250Mbps',
    500: '500Mbps',
};

export const ProtocolColumns = [
    {name: 'authenticationMode', label: '认证方式', width: 100},
    {name: 'aclEnable', label: '权限控制', width: 100},
    {name: 'encryptEnable', label: '传输加密', width: 100},
];

export const PriceUnit = {
    [Payment.Postpaid]: '分钟',
    [Payment.Prepaid]: '月',
};

export const One_Hour_Time_Ms = 60 * 60 * 1000;

export const NumberOfBrokerNodesHelpTip = '总节点数量 = 组内节点数 * 节点组数量';

export const DiskStorageSizeHelpTip = '磁盘大小为单节点的磁盘大小';
export const DiskTotalStorageSizeHelpTip = '总磁盘容量 = 单节点容量 * 总节点数量';
