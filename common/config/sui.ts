/**
 * 组件库相关配置
 * <AUTHOR>
 */

// sui分页一些公用配置
export const PAGER_SUI = {
    pageSize: 10,
    page: 1,
    pageSizes: [10, 20, 50, 100]
};

// sui表格一些公用配置
export const TABLE_SUI = {
    error: false,
    loading: false,
    datasource: []
};

export const SELECTION_SUI_SINGLE = {
    mode: 'single',
    selectedIndex: []
};

// sui的表格多选配置
export const SELECTION_SUI_MULTI = {
    mode: 'multi',
    selectedIndex: []
};

// 颜色相关
export const COLOR_CONF = {
    defaultColor: '#2468f2',
    disableColor: '#b8babf'
};

export const PAGINATION_LAYOUT = 'total, pageSize, pager, go';

// 输入框长度
export const INPUT_WIDTH = 248;

// 弹框类输入框长度
export const DIALOG_INPUT_WIDTH = 400;

// 弹框类输入框长度-短
export const DIALOG_INPUT_WIDTH_SHOT = 280;

// 下拉高度
export const SELECT_HEIGHT = 28;
// 分页请求所有配置
export const PAGER_MAX = {
    pageNo: 1,
    pageSize: 100000
};
