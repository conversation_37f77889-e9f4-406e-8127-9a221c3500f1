module.exports = {
    extends: [
        '@ecomfe/eslint-config',
        '@ecomfe/eslint-config/typescript' // 使用TS基本规则校验
        // '@ecomfe/eslint-config/typescript/strict' // 或者选择严格模式
    ],
    rules: {
        'comma-dangle': 'off', // 关闭了代码拖尾逗号的问题校验
        '@typescript-eslint/no-unnecessary-type-assertion': 'off', // 关闭类型断言as
        '@typescript-eslint/member-ordering': 'off', // 关闭类成员顺序校验
        '@typescript-eslint/member-delimiter-style': 'off' // 关闭接口 & 类型必须用分号分隔的校验
    }
};
