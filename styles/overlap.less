.status {
    &::before {
        display: inline-block;
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #b8babf;
    }

    &.unavailable::before,
    &.inactive::before {
        background: #b8babf;
    }

    &.normal:before,
    &.success:before {
        background: var(--green-color);
    }

    &.warning:before {
        background: var(--orange-color);
    }

    &.active:before,
    &.process:before {
        background: var(--deep-blue-color);
    }

    &.processing::before {
        content: '';
        background: var(--light-blue-color);
        animation: flash 0.4s ease-in infinite alternate;
    }

    &.fail:before,
    &.error:before {
        background: var(--red-color);
    }

    &.prepare:before {
        background: #fad000;
    }

    &.rolling:before {
        width: 0;
        height: 0;
        background: initial;
    }
}

/* sui formitem 必选样式约束 */
.s-form-item {
    .s-form-item-label-required {
        label {
            position: relative;

            &::before {
                position: absolute;
                left: 0px;
            }
        }
    }
}

.s-detail-cell .detail-cell {
    display: inline-flex;
}

.s-table-filter-menu {
    max-height: 300px;
    overflow-y: auto;
}

// 表格内容最小高度铺满页面
.s-list-page {
    .s-list-content {
        flex: 1;
        display: flex;
        flex-direction: column;

        .table-full-wrap {
            flex: 1;
            display: flex;
            flex-direction: column;

            &:has(.s-table-empty) {
                padding-bottom: 0;

                .foot-pager {
                    display: none;
                }
            }
        }
    }
}
