// color
@red-color: #f33e3e;
@orange-color: #ff9326;
@yellow-color: #fad000;
@green-color: #30bf13;
@blue-color: #2468f2;
@light-blue-color: #528eff;
@deep-blue-color: #114bcc;
@dark-color: #151b26;
@grey-color: #b8babf;

// text color
@text-base-color: @dark-color; // 文字基础黑色
@text-sub-color: #84868c; // 文字辅助颜色
@text-active-color: @deep-blue-color; // 文字突出颜色对应::active伪类
@text-hover-color: @light-blue-color; // 文字hover颜色对应::hover伪类
@text-primary-color: @blue-color; // 文字主蓝色
@text-disabled-color: @grey-color; // 文字不可用颜色

// border color
@border-color: #e8e9eb;

// background color
@background-color: #f7f7f9;

// 打包后的文件为.css文件，无法使用@的变量，使用:root定义变量
:root {
    --red-color: @red-color;
    --orange-color: @orange-color;
    --yellow-color: @yellow-color;
    --green-color: @green-color;
    --blue-color: @blue-color;
    --light-blue-color: @light-blue-color;
    --deep-blue-color: @deep-blue-color;
    --dark-color: @dark-color;
    --grey-color: @grey-color;

    // text color
    --text-base-color: @text-base-color;
    --text-sub-color: @text-sub-color;
    --text-active-color: @text-active-color;
    --text-hover-color: @text-hover-color;
    --text-primary-color: @text-primary-color;
    --text-disabled-color: @text-disabled-color;

    // border-color
    --border-color: @border-color;
    // background-color
    --background-color: @background-color;
}
