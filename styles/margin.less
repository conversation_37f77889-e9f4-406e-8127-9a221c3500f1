.ml(@n, @i: 1) when (@i =< @n) {
    .ml@{i} {
        margin-left: @i * 1px;
    }
    .ml(@n, (@i + 1));
}
.ml(30);

.mt(@n, @i: 1) when (@i =< @n) {
    .mt@{i} {
        margin-top: @i * 1px;
    }
    .mt(@n, (@i + 1));
}
.mt(30);

.mr(@n, @i: 1) when (@i =< @n) {
    .mr@{i} {
        margin-right: @i * 1px;
    }
    .mr(@n, (@i + 1));
}
.mr(30);

.mb(@n, @i: 1) when (@i =< @n) {
    .mb@{i} {
        margin-bottom: @i * 1px;
    }
    .mb(@n, (@i + 1));
}
.mb(30);
