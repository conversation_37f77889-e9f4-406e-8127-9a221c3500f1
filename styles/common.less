// 非button售罄样式
.radio-soldout {
    position: absolute;
    right: -30px;
    top: -20px;
    display: inline-block;
    width: 40px;
    height: 16px;
    border-radius: 8px;
    background: var(--grey-color);
    font-size: 10px;
    content: '售罄';
    line-height: 16px;
    text-align: center;
    color: #fff;
}

// radio-button售罄样式
.radio-button-soldout {
    position: absolute;
    right: 0;
    top: -8px;
    display: inline-block;
    width: 24px;
    border-radius: 2px;
    background: var(--grey-color);
    font-size: 10px;
    content: '售罄';
    line-height: 16px;
    text-align: center;
    color: #fff;
}

.table-soldout {
    position: absolute;
    right: -56px;
    top: 0;
    display: inline-block;
    width: 32px;
    height: 16px;
    border-radius: 2px;
    background: var(--grey-color);
    font-size: 12px;
    content: '售罄';
    line-height: 16px;
    text-align: center;
    color: #fff;
}

.clampMixin(@num) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: @num;
    -webkit-box-orient: vertical;
    white-space: wrap;
}

.clamp(@n, @i: 2) when (@i =< @n) {
    .clamp-@{i} {
        .clampMixin(@i);
    }
    .clamp(@n, (@i + 1));
}

.clamp-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.clamp(5);

// pick 的 icon 都会偏下，加上这个垂直居中
.pick-icon {
    position: relative;
    bottom: 1.5px;
}

.flex {
    display: flex;
}
