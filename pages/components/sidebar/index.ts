/**
 * 这里应该是注册一些sidebar
 *
 * @file sidebar.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {AppSidebar} from '@baidu/sui-biz';
import {ROUTE_PATH, THIRD_ROUTE_PATH} from '@common/config/route';
import {isOneCloudId} from '@common/utils/common';
import './index.less';

@decorators.asSidebar()
export class DefaultSidebar extends Component {
    static template = html`
        <s-biz-sidebar title="消息服务 for RocketMQ" active-name="{= active =}" class="app-sidebar" hasFold>
            <s-biz-sidebar-item title="集群">
                <s-biz-sidebar-item
                    title="集群列表"
                    name="${ROUTE_PATH.clusterList}"
                    link-to="${ROUTE_PATH.clusterList}"
                />
            </s-biz-sidebar-item>
            <s-biz-sidebar-item title="常用功能" s-if="{{!isOneCloud}}">
                <s-biz-sidebar-item
                    title="集群标签"
                    name="${THIRD_ROUTE_PATH.tagList}"
                    link-to="${THIRD_ROUTE_PATH.tagList}"
                    iconLink="link"
                    target="blank"
                />
            </s-biz-sidebar-item>
        </s-biz-sidebar>
    `;

    static components = {
        's-biz-sidebar': AppSidebar,
        's-biz-sidebar-item': AppSidebar.Item
    };

    initData() {
        return {
            isOneCloud: isOneCloudId()
        };
    }

    inited() {
        this.watch('active', name => this.fire('active', {name}));
    }
}
