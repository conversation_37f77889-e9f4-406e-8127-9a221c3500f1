/**
 * 节点扩容
 * @file index.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Table, InputNumber} from '@baidu/sui';
import {NodeConfigTextMap} from '@pages/cluster/create/node-config';
import './index.less';
import {TipIcon} from '@components/tip-icon';
import {
    formatArchFlush,
    formatArchHelpTip,
    formatMode,
    formatPayment,
    formatRegion,
    computeNumberOfBrokerNodes
} from '@common/utils';
import {IClusterDetail} from '@pages/cluster/detail/index.d';
import {BaseScale} from '../base-scale';
import {PaymentTextMap} from '@pages/cluster/create/payment-region';
import {NumberOfBrokerNodesHelpTip} from '@common/config';
const klass = 'm-scale-broker-number';

const commonTpl = html`
    <div slot="h-numberOfBrokers" class="${klass}__head-tip">
        <span>{{col.label}}</span>
        <tip-icon type="question"> {{detail.arch | formatArchHelpTip(detail.numberOfNodesPerBroker)}} </tip-icon>
    </div>
    <div slot="h-numberOfBrokerNodes" class="${klass}__head-tip">
        <span>{{col.label}}</span>
        <tip-icon type="question">${NumberOfBrokerNodesHelpTip}</tip-icon>
    </div>
    <div slot="c-nodeSpec">{{row.nodeSpec}}</div>
`;
export class BrokerNumber extends BaseScale {
    static template = html`
        <template>
            <div class="${klass} m-scale-common">
                <div class="${klass}__item ${klass}__origin">
                    <label class="${klass}__label">变更前：</label>
                    <s-table columns="{{columns}}" datasource="{{originDatasource}}">
                        ${commonTpl}
                        <div slot="c-numberOfBrokers">{{row.numberOfBrokers}}</div>
                    </s-table>
                </div>
                <div class="${klass}__item ${klass}__new">
                    <label class="${klass}__label">变更后：</label>
                    <s-table columns="{{columns}}" datasource="{{newDatasource}}">
                        ${commonTpl}
                        <div slot="c-numberOfBrokers">
                            <s-input-number
                                type="number"
                                value="{=row.numberOfBrokers=}"
                                on-change="onNumberOfBrokersChange"
                                min="{{row.numberOfBrokers}}"
                                max="{{15}}"
                            />
                        </div>
                        <div slot="c-numberOfBrokerNodes">{{numberOfBrokers | filterTotalBrokers(detail)}}</div>
                    </s-table>
                </div>
            </div>
        </template>
    `;

    static components = {
        's-table': Table,
        'tip-icon': TipIcon,
        's-input-number': InputNumber
    };

    static filters = {
        formatArchHelpTip,
        filterTotalBrokers: (numberOfBrokers: IClusterDetail['numberOfBrokers'], detail: IClusterDetail) => {
            const {arch, numberOfNodesPerBroker} = detail;
            return computeNumberOfBrokerNodes({
                numberOfBrokers,
                arch,
                dledgerNumberOfNodesPerBroker: numberOfNodesPerBroker,
                masterSlaveNumberOfNodesPerBroker: numberOfNodesPerBroker - 1
            });
        }
    };
    static computed = {};

    initData() {
        return {
            columns: [
                {
                    name: 'numberOfBrokers',
                    label: NodeConfigTextMap.numberOfBrokers
                },
                {
                    name: 'numberOfBrokerNodes',
                    label: NodeConfigTextMap.numberOfBrokerNodes
                },
                {
                    name: 'nodeSpec',
                    label: NodeConfigTextMap.nodeType
                }
            ],
            datasource: [],
            numberOfBrokers: 1
        };
    }

    attached() {
        this.handleDatasource();
    }

    handleDatasource() {
        const detail = this.data.get('detail');
        const {numberOfBrokers, nodeSpec, arch, numberOfNodesPerBroker} = detail;
        const datasource = [
            {
                numberOfBrokers,
                numberOfBrokerNodes: computeNumberOfBrokerNodes({
                    numberOfBrokers,
                    arch,
                    dledgerNumberOfNodesPerBroker: numberOfNodesPerBroker,
                    masterSlaveNumberOfNodesPerBroker: numberOfNodesPerBroker - 1
                }),
                nodeSpec
            }
        ];
        this.data.set('originDatasource', datasource);
        this.data.set('newDatasource', datasource);
        this.data.set('numberOfBrokers', numberOfBrokers);
        this.handlePriceChange();
    }

    onNumberOfBrokersChange(e: {value: IClusterDetail['numberOfNodesPerBroker']}) {
        this.data.set('numberOfBrokers', e.value);
        this.handlePriceChange();
    }

    handlePriceChange() {
        this.fire('get-price', {});
    }

    getPriceParams() {
        const numberOfBrokers = this.data.get('numberOfBrokers');
        return {
            numberOfBrokers
        };
    }

    async check() {
        const {numberOfBrokers: originNumberOfBrokers} = this.data.get('detail');
        const numberOfBrokers = this.data.get('numberOfBrokers');

        if (originNumberOfBrokers === numberOfBrokers) {
            return Promise.reject('');
        }

        return Promise.resolve();
    }

    getOrderItemData() {
        const {payment, region, arch, mode, flushDiskType, numberOfNodesPerBroker, zoneNames, nodeSpec} =
            this.data.get('detail');
        const numberOfBrokers = this.data.get('numberOfBrokers');
        return {
            title: '变更后节点配置',
            class: `${klass}__order`,
            datasource: [
                {label: '所在地区：', value: formatRegion(region)},
                {label: PaymentTextMap.payment, value: formatPayment(payment)},
                {
                    label: NodeConfigTextMap.arch,
                    value: formatArchFlush({
                        arch,
                        flushDiskType,
                        dledgerNumberOfNodesPerBroker: numberOfNodesPerBroker,
                        masterSlaveNumberOfNodesPerBroker: numberOfNodesPerBroker - 1
                    })
                },

                {label: NodeConfigTextMap.mode, value: formatMode({mode, selectedOrderZones: zoneNames})},
                {label: NodeConfigTextMap.nodeType, value: nodeSpec},
                {
                    label: NodeConfigTextMap.numberOfBrokers,
                    value: numberOfBrokers
                },

                {
                    label: NodeConfigTextMap.numberOfBrokerNodes,
                    value: computeNumberOfBrokerNodes({
                        numberOfBrokers,
                        arch,
                        dledgerNumberOfNodesPerBroker: numberOfNodesPerBroker,
                        masterSlaveNumberOfNodesPerBroker: numberOfNodesPerBroker - 1
                    })
                }
            ]
        };
    }

    getOrderParams() {
        return {
            numberOfBrokers: this.data.get('numberOfBrokers')
        };
    }
}
