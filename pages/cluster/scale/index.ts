/**
 * 扩容
 * @file index.ts
 * <AUTHOR>
 */

import {html, decorators, CreatePage, redirect} from '@baiducloud/runtime';
import {AppCreatePage, AppDetailCell} from '@baidu/sui-biz';
import {Steps, Radio, Form, Button, Notification, Loading} from '@baidu/sui';
import {ClusterCreateSuccUrl, PriceUnit, ROUTE_PATH, ServiceType, IUrlFromValue, FromMapUrl} from '@common/config';
import './index.less';
import {CommonCard, TipIcon} from '@components/index';
import {api} from '@common/client';
import {ClusterConfigTextMap} from '../create/cluster-config';
import {PriceItem, CouponConfig} from '@pages/cluster/components';
import {formatClusterItem, formatComputePrice, formatEmpty, formatTime, isInvalid} from '@common/utils';
import {
    formatArchFlush,
    formatMode,
    formatPayment,
    formatRegion,
    formatStorageSize,
    formatStorageType,
    formatTotalStorageSize,
    isPrepaid,
    setDisplay
} from '@common/utils';
import {NodeConfigTextMap} from '../create/node-config';
import {PaymentTextMap} from '../create/payment-region';
import {renderSwitch} from '@common/html';
import {DiskTextMap} from '../create/disk-config';
import {NodeType} from './node-type';
import {BrokerNumber} from './broker-number';
import {IClusterDetail, IFeClusterDetail} from '../detail/index.d';
import {BaseScale} from './base-scale';
import {OrderConfirm} from './order-confirm';
import {ScalePriceResult} from './index.d';
import {Payment} from '@common/enums';
import {DiskScale} from './disk';
import BigNumber from 'bignumber.js';

const klass = 'page-cluster-scale';

export enum IScaleType {
    'BrokerNumber' = 'BrokerNumber',
    'NodeType' = 'NodeType',
    'Disk' = 'Disk'
}

@decorators.asPage(ROUTE_PATH.clusterScale)
export class ScalePage extends CreatePage {
    static template = html`
        <template>
            <biz-create-page class="${klass}" pageTitle="变更规格" backTo="{{backTo}}">
                <div class="${klass}__step">
                    <s-steps current="{{current}}" type="normal">
                        <s-step title="变更配置" />
                        <s-step title="确认订单" />
                    </s-steps>
                </div>
                <div class="${klass}__content">
                    <div style="{{current === 1 | setDisplay}}">
                        <common-card title="当前配置" class="${klass}__current-config">
                            <biz-detail-cell datasource="{{configs}}">
                                <div slot="c-clusterId">
                                    {{detail.clusterId}}
                                    <s-clip-board text="{{detail.clusterId}}" />
                                </div>
                            </biz-detail-cell>
                        </common-card>
                        <common-card title="变更规格" class="mt16 ${klass}__scale">
                            <s-form>
                                <s-form-item label="变更类型：">
                                    <s-radio-group
                                        datasource="{{scaleTypes}}"
                                        value="{= scaleType =}"
                                        radioType="button"
                                        enhanced
                                    />
                                    <s-form-item s-if="detail && scaleType" label="" class="${klass}__scale-content">
                                        <broker-number
                                            s-if="scaleType === IScaleType.BrokerNumber"
                                            s-ref="${IScaleType.BrokerNumber}"
                                            detail="{{detail}}"
                                            on-get-price="getPrice"
                                        ></broker-number>
                                        <node-type
                                            s-if="scaleType === IScaleType.NodeType"
                                            s-ref="${IScaleType.NodeType}"
                                            detail="{{detail}}"
                                            on-get-price="getPrice"
                                        ></node-type>
                                        <disk-scale
                                            s-elif="scaleType === IScaleType.Disk"
                                            s-ref="${IScaleType.Disk}"
                                            detail="{{detail}}"
                                            on-get-price="getPrice"
                                        ></disk-scale>
                                    </s-form-item>
                                </s-form-item>
                            </s-form>
                        </common-card>
                    </div>
                    <div style="{{current === 2 | setDisplay}}">
                        <order-confirm orderData="{{orderData}}" />
                        <coupon-config
                            class="${klass}__coupon"
                            s-if="isPrepaid"
                            s-ref="couponConfigRef"
                            decountPrice="{{decountPrice}}"
                            on-coupon-select-change="onCouponSelectChange"
                        />
                    </div>
                </div>
                <template slot="pageFooter">
                    <template s-if="current === 1">
                        <s-button skin="primary" size="large" on-click="onNext">确认订单</s-button>
                        <s-button size="large" class="ml16" on-click="onBack">取消</s-button>
                    </template>
                    <template s-if="current === 2">
                        <s-button size="large" on-click="onPrev">上一步</s-button>
                        <s-button
                            skin="primary"
                            size="large"
                            on-click="onConfirm"
                            class="ml16"
                            loading="{{loading.confirm}}"
                        >
                            {{isPrepaid ? '去支付' : '提交订单'}}
                        </s-button>
                    </template>
                    <s-loading loading="{{loading.price}}">
                        <price-item
                            s-if="current === 1"
                            class="${klass}__price-item"
                            title="变更后集群费用："
                            price="{{computedPrice}}"
                            desc="{{price.updateReferencePrice | filterComputePrice(detail.payment)}}"
                        />
                        <price-item
                            s-else
                            class="${klass}__price-item"
                            title="实付金额："
                            price="{{finalPrice}}"
                            desc="{{price.updateReferencePrice | filterComputePrice(detail.payment)}}"
                        />
                    </s-loading>
                </template>
            </biz-create-page>
        </template>
    `;
    static components = {
        'biz-create-page': AppCreatePage,
        's-steps': Steps,
        's-step': Steps.Step,
        'common-card': CommonCard,
        's-form': Form,
        's-form-item': Form.Item,
        'biz-detail-cell': AppDetailCell,
        's-radio-group': Radio.RadioGroup,
        'broker-number': BrokerNumber,
        'node-type': NodeType,
        'disk-scale': DiskScale,
        's-button': Button,
        'order-confirm': OrderConfirm,
        'price-item': PriceItem,
        'tip-icon': TipIcon,
        'coupon-config': CouponConfig,
        's-loading': Loading
    };

    static computed: SanComputedProps = {
        clusterId(): string {
            return this.data.get('route.query.clusterId');
        },
        isPrepaid() {
            const {payment} = this.data.get('detail') || {};
            return isPrepaid(payment);
        },
        backTo() {
            const {from = IUrlFromValue.List, clusterId} = this.data.get('route.query') as {
                from: IUrlFromValue;
                clusterId: string;
            };
            const urlSuffix = from === IUrlFromValue.Detail ? `?clusterId=${clusterId}` : '';
            return FromMapUrl[from] + urlSuffix;
        },
        computedPrice() {
            const payment = this.data.get('detail.payment') as Payment;
            const {updateReferencePrice} = this.data.get('price');
            const priceNum = isInvalid(updateReferencePrice) ? formatEmpty('') : updateReferencePrice;
            const unit = PriceUnit[payment] || '';

            return `¥${priceNum}/${unit}`;
        },
        decountPrice(): number {
            const couponPrice = this.data.get('currentSelectCoupon.balance') || 0;
            const {margin} = this.data.get('price') as ScalePriceResult;

            return couponPrice > margin ? margin : couponPrice;
        },
        finalPrice() {
            const isPrepaid = this.data.get('isPrepaid');
            if (!isPrepaid) {
                return this.data.get('computedPrice');
            }

            const {margin} = this.data.get('price') as ScalePriceResult;
            if (isInvalid(margin)) {
                return `￥${formatEmpty('')}`;
            }

            let payPrice = 0;
            const couponPrice = this.data.get('currentSelectCoupon.balance') || 0;
            const marginPrice = new BigNumber(margin);
            payPrice = marginPrice.minus(couponPrice).toNumber();
            return `¥${payPrice <= 0 ? 0 : payPrice}`;
        }
    };
    static filters: SanFilterProps = {
        setDisplay,
        filterComputePrice: (price: ScalePriceResult['updateReferencePrice'], payment: Payment) => {
            if (payment === Payment.Prepaid || isInvalid(price)) {
                return '';
            }

            return formatComputePrice(price);
        },
        formatTime
    };

    initData() {
        return {
            detail: null,
            loading: {
                detail: false,
                price: false
            },
            configs: [],
            current: 1,
            scaleTypes: [],
            scaleType: '',
            IScaleType,
            orderData: {},
            currentSelectCoupon: null,
            price: {}
        };
    }

    attached() {
        this.getDetail();
    }

    async getDetail() {
        try {
            this.data.set('loading.detail', true);
            const res = (await api.clusterDetail({clusterId: this.data.get('clusterId')})) as IClusterDetail;
            const detail = formatClusterItem(res) as IFeClusterDetail;
            this.data.set('detail', detail);
            this.handleScaleTypes(detail);
            const {
                payment,
                name,
                version,
                region,
                arch,
                mode,
                deploySetEnabled,
                flushDiskType,
                numberOfNodesPerBroker,
                zoneNames,
                nodeSpec,
                numberOfBrokers,
                numberOfBrokerNodes,
                storageType,
                numberOfDisks,
                storageSize
            } = detail;
            const datasource = [
                {label: '集群ID：', slot: 'clusterId'},
                {label: ClusterConfigTextMap.name, value: name},
                {label: '所在地区：', value: formatRegion(region)},

                {label: PaymentTextMap.payment, value: formatPayment(payment)},
                {label: ClusterConfigTextMap.version, value: version},
                {
                    label: NodeConfigTextMap.arch,
                    value: formatArchFlush({
                        arch,
                        flushDiskType,
                        dledgerNumberOfNodesPerBroker: numberOfNodesPerBroker,
                        masterSlaveNumberOfNodesPerBroker: numberOfNodesPerBroker - 1
                    })
                },

                {label: NodeConfigTextMap.deploySetEnabled, value: renderSwitch(deploySetEnabled)},
                {label: NodeConfigTextMap.mode, value: formatMode({mode, selectedOrderZones: zoneNames})},
                {label: NodeConfigTextMap.nodeType, value: nodeSpec},

                {
                    label: NodeConfigTextMap.numberOfBrokers,
                    value: numberOfBrokers
                },
                {label: NodeConfigTextMap.numberOfBrokerNodes, value: numberOfBrokerNodes},
                {label: DiskTextMap.storageType, value: formatStorageType(storageType)},

                {
                    label: DiskTextMap.storageSize,
                    value: formatStorageSize({storageSize, numberOfDisks, storageType})
                },
                {
                    label: DiskTextMap.totalStorageSize,
                    value: formatTotalStorageSize({storageSize, numberOfDisks, storageType, numberOfBrokerNodes})
                }
            ];
            this.data.set('configs', datasource);
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('loading.detail', false);
        }
    }

    handleScaleTypes(detail: IFeClusterDetail) {
        const {isBrokerNumberMax, isBrokerNodeTypeMax, isBrokerDiskCapacityMax} = detail;

        const scaleArr = [
            {
                label: '节点扩容',
                value: IScaleType.BrokerNumber,
                disabled: isBrokerNumberMax,
                tip: isBrokerNumberMax ? '当前节点数量已达系统上限,不支持进行扩容' : ''
            },
            {
                label: '节点升配',
                value: IScaleType.NodeType,
                disabled: isBrokerNodeTypeMax,
                tip: isBrokerNodeTypeMax ? '当前节点规格已达系统上限，不支持进行升配' : ''
            },
            {
                label: '磁盘扩容',
                value: IScaleType.Disk,
                disabled: isBrokerDiskCapacityMax,
                tip: isBrokerDiskCapacityMax ? '当前磁盘容量已达系统上限，不支持进行扩容' : ''
            }
        ];
        this.data.set('scaleTypes', scaleArr);
        const firstEnableScale = scaleArr.find(item => !item.disabled);
        firstEnableScale && this.data.set('scaleType', firstEnableScale.value);
    }

    async getPrice() {
        const {clusterId, detail, scaleType} = this.data.get('') as {
            clusterId: IClusterDetail['clusterId'];
            detail: IClusterDetail;
            scaleType: IScaleType;
        };
        const {payment} = detail;

        const commonParams = {
            clusterId,
            payment
        };
        try {
            let res: ScalePriceResult;
            this.data.set('loading.price', true);
            if (scaleType === IScaleType.BrokerNumber) {
                const scaleBrokerRef = this.ref(scaleType) as BrokerNumber;
                const scaleBrokerParams = scaleBrokerRef.getPriceParams();
                const brokerCount = scaleBrokerParams.numberOfBrokers;
                res = (await api.scaleBrokerNumberPrice({
                    clusterId,
                    params: {
                        payment,
                        brokerCount
                    }
                })) as ScalePriceResult;
            } else if (scaleType === IScaleType.NodeType) {
                const scaleNodeTypeRef = this.ref(scaleType) as NodeType;
                const scaleNodeTypeParams = scaleNodeTypeRef.getPriceParams();
                res = (await api.scaleNodeTypePrice({
                    clusterId,
                    params: {
                        ...commonParams,
                        ...scaleNodeTypeParams
                    }
                })) as ScalePriceResult;
            } else if (scaleType === IScaleType.Disk) {
                const scaleDiskRef = this.ref(scaleType) as DiskScale;
                const scaleDiskParams = scaleDiskRef.getPriceParams();
                res = (await api.scaleDiskPrice({
                    clusterId,
                    params: {
                        ...commonParams,
                        ...scaleDiskParams
                    }
                })) as ScalePriceResult;
            }
            this.data.set('price', res);
        } catch (err) {
            console.error(`get price error: ${err}`);
        } finally {
            this.data.set('loading.price', false);
        }
    }

    async onNext() {
        const scaleType = this.data.get('scaleType');
        const scaleRef = this.ref(scaleType) as BaseScale;
        try {
            await scaleRef.check();
            const current = this.data.get('current');
            this.data.set('current', current + 1);
            const orderData = scaleRef.getOrderItemData();
            this.data.set('orderData', {
                ...orderData,
                priceInfo: {
                    title: '小计：',
                    price: this.data.get('computedPrice')
                }
            });
        } catch (err) {
            Notification.error('请变更配置', {duration: 5});
        }
    }

    onBack() {
        const backTo = this.data.get('backTo');
        redirect(`#${backTo}`);
    }

    onPrev() {
        const current = this.data.get('current');
        this.data.set('current', current - 1);
    }

    async onConfirm() {
        const scaleType = this.data.get('scaleType');
        this.data.set('loading.confirm', true);
        try {
            let res = null;
            if (scaleType === IScaleType.BrokerNumber) {
                res = await this.handleConfirmBrokerNumber();
            } else if (scaleType === IScaleType.NodeType) {
                res = await this.handleConfirmNodeType();
            } else if (scaleType === IScaleType.Disk) {
                res = await this.handleConfirmDisk();
            }

            const isPrepaid = this.data.get('isPrepaid');
            if (isPrepaid) {
                this.goOrder(res.orderId, 'RESIZE');
                return;
            }
            redirect(`#${ROUTE_PATH.clusterList}`);
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('loading.confirm', false);
        }
    }

    async handleConfirmBrokerNumber() {
        const brokerNumberRef = this.ref(IScaleType.BrokerNumber) as BrokerNumber;
        const formData = brokerNumberRef.getOrderParams();
        const {clusterId} = this.data.get('');
        const commonOrderParams = this.getCommonOrderParams();
        const brokerCount = formData.numberOfBrokers;
        const res = await api.scaleBrokerNumber({
            clusterId,
            params: {
                brokerCount,
                ...commonOrderParams
            }
        });
        return res;
    }

    async handleConfirmNodeType() {
        const nodeTypeRef = this.ref(IScaleType.NodeType) as NodeType;
        const formData = nodeTypeRef.getOrderParams();
        const {clusterId} = this.data.get('');
        const commonOrderParams = this.getCommonOrderParams();
        const res = await api.scaleNodeType({
            clusterId,
            params: {
                ...formData,
                ...commonOrderParams
            }
        });

        return res;
    }

    async handleConfirmDisk() {
        const diskRef = this.ref(IScaleType.Disk) as DiskScale;
        const formData = diskRef.getOrderParams();
        const {clusterId} = this.data.get('');
        const commonOrderParams = this.getCommonOrderParams();
        const res = await api.scaleDisk({
            clusterId,
            params: {
                ...formData,
                ...commonOrderParams
            }
        });

        return res;
    }

    getCommonOrderParams() {
        const isPrepaid = this.data.get('isPrepaid');
        const currentSelectCoupon = this.data.get('currentSelectCoupon');
        if (isPrepaid && currentSelectCoupon?.id) {
            return {
                couponIds: [currentSelectCoupon.id.toString()]
            };
        }
        return {};
    }

    onCouponSelectChange(data: {currentSelectCoupon: any}) {
        this.data.set('currentSelectCoupon', data.currentSelectCoupon);
    }

    // 确认订单页
    goOrder(orderId: string, orderType: string) {
        redirect(`${ClusterCreateSuccUrl.Prepaid}${ServiceType}&orderType=${orderType}&orderId=${orderId}`);
    }
}
