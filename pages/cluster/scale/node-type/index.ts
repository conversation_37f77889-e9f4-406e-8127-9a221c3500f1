/**
 * 扩容
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Table, Select} from '@baidu/sui';
import {NodeConfigTextMap} from '@pages/cluster/create/node-config';
import './index.less';
import {TipIcon} from '@components/tip-icon';
import {formatArchFlush, formatArchHelpTip, formatMode, formatPayment, formatRegion} from '@common/utils';
import {api} from '@common/client';
import {IClusterDetail} from '@pages/cluster/detail/index.d';
import {FlavorResult} from '@pages/cluster/create/node-config/index.d';
import {BaseScale} from '../base-scale';
import {PaymentTextMap} from '@pages/cluster/create/payment-region';
import {NumberOfBrokerNodesHelpTip} from '@common/config';
import {find} from 'lodash';

const klass = 'm-scale-node-type';

const commonTpl = html`
    <div slot="h-numberOfBrokers" class="${klass}__head-tip">
        <span>{{col.label}}</span>
        <tip-icon type="question"> {{detail.arch | formatArchHelpTip(detail.numberOfNodesPerBroker)}} </tip-icon>
    </div>
    <div slot="h-numberOfBrokerNodes" class="${klass}__head-tip">
        <span>{{col.label}}</span>
        <tip-icon type="question">${NumberOfBrokerNodesHelpTip}</tip-icon>
    </div>
`;
export class NodeType extends BaseScale {
    static template = html`
        <template>
            <div class="${klass} m-scale-common">
                <div class="${klass}__item ${klass}__origin">
                    <label class="${klass}__label">变更前：</label>
                    <s-table columns="{{columns}}" datasource="{{originDatasource}}">
                        ${commonTpl}
                        <div slot="c-nodeSpec">{{row.nodeSpec}}</div>
                    </s-table>
                </div>
                <div class="${klass}__item ${klass}__new">
                    <label class="${klass}__label">变更后：</label>
                    <s-table columns="{{columns}}" datasource="{{newDatasource}}">
                        ${commonTpl}
                        <div slot="c-nodeSpec">
                            <s-select
                                datasource="{{nodeList}}"
                                value="{{nodeType}}"
                                on-change="onNodeTypeChange"
                                width="{{180}}"
                            ></s-select>
                        </div>
                    </s-table>
                </div>
            </div>
        </template>
    `;

    static components = {
        's-table': Table,
        'tip-icon': TipIcon,
        's-select': Select
    };

    static computed = {};

    static filters = {
        formatArchHelpTip
    };

    initData() {
        return {
            columns: [
                {
                    name: 'numberOfBrokers',
                    label: NodeConfigTextMap.numberOfBrokers
                },
                {
                    name: 'numberOfBrokerNodes',
                    label: NodeConfigTextMap.numberOfBrokerNodes
                },
                {label: NodeConfigTextMap.nodeType, name: 'nodeSpec'}
            ],
            datasource: [],
            nodeList: [],
            nodeType: ''
        };
    }

    attached() {
        this.handleDatasource();
        this.getFlavors();
    }

    handleDatasource() {
        const detail = this.data.get('detail') as IClusterDetail;
        const {numberOfBrokers, numberOfBrokerNodes, nodeSpec} = detail;
        const datasource = [{numberOfBrokers, numberOfBrokerNodes, nodeSpec}];
        this.data.set('originDatasource', datasource);
        this.data.set('newDatasource', datasource);
    }

    async getFlavors() {
        const detail = this.data.get('detail') as IClusterDetail;
        const {clusterId, zoneNames} = detail;
        const res = (await api.getFlavors({
            clusterId,
            zoneNames
        })) as FlavorResult;

        const nodeList = res?.instanceFlavor?.bccFlavors?.map(item => ({
            ...item,
            disabled: item.status === 'SELLOUT',
            label: item.status === 'SELLOUT' ? `${item.name}（售罄）` : item.name,
            value: item.type,
            noDataText: '已售罄'
        }));
        const firstData = nodeList.find(item => !item.disabled);
        if (firstData) {
            this.data.set('nodeType', firstData.value);
            this.handlePriceChange();
        }
        this.data.set('nodeList', nodeList);
    }
    onNodeTypeChange(e: {value: IClusterDetail['nodeType']}) {
        this.data.set('nodeType', e.value);
        this.handlePriceChange();
    }

    handlePriceChange() {
        this.fire('get-price', {});
    }

    getPriceParams() {
        const nodeType = this.data.get('nodeType');
        return {
            nodeType
        };
    }

    async check() {
        const {nodeType: originNodeId} = this.data.get('detail') as IClusterDetail;
        const nodeId = this.data.get('nodeType');

        if (!nodeId || originNodeId === nodeId) {
            return Promise.reject('');
        }

        return Promise.resolve();
    }

    getOrderItemData() {
        const {
            payment,
            region,
            arch,
            mode,
            flushDiskType,
            numberOfNodesPerBroker,
            zoneNames,
            numberOfBrokers,
            numberOfBrokerNodes
        } = this.data.get('detail') as IClusterDetail;
        const {nodeType, nodeList} = this.data.get('') as {
            nodeType: IClusterDetail['nodeType'];
            nodeList: FlavorResult['instanceFlavor']['bccFlavors'];
        };
        const nodeTypeName = nodeList.find(item => item.value === nodeType)?.name;

        return {
            title: '变更后节点配置',
            class: `${klass}__order`,
            datasource: [
                {label: '所在地区：', value: formatRegion(region)},
                {label: PaymentTextMap.payment, value: formatPayment(payment)},
                {
                    label: NodeConfigTextMap.arch,
                    value: formatArchFlush({
                        arch,
                        flushDiskType,
                        dledgerNumberOfNodesPerBroker: numberOfNodesPerBroker,
                        masterSlaveNumberOfNodesPerBroker: numberOfNodesPerBroker - 1
                    })
                },

                {label: NodeConfigTextMap.mode, value: formatMode({mode, selectedOrderZones: zoneNames})},
                {label: NodeConfigTextMap.nodeType, value: nodeTypeName},
                {
                    label: NodeConfigTextMap.numberOfBrokers,
                    value: numberOfBrokers
                },

                {label: NodeConfigTextMap.numberOfBrokerNodes, value: numberOfBrokerNodes}
            ]
        };
    }

    getOrderParams() {
        return {
            nodeType: this.data.get('nodeType')
        };
    }
}
