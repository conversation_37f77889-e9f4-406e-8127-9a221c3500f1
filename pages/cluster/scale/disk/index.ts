/**
 * 磁盘升配
 * @file index.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Table, InputNumber} from '@baidu/sui';
import {NodeConfigTextMap} from '@pages/cluster/create/node-config';
import './index.less';
import {TipIcon} from '@components/tip-icon';
import {
    formatArchFlush,
    formatMode,
    formatPayment,
    formatRegion,
    formatStorageType,
    formatTotalStorageSize
} from '@common/utils';
import {IClusterDetail} from '@pages/cluster/detail/index.d';
import {BaseScale} from '../base-scale';
import {PaymentTextMap} from '@pages/cluster/create/payment-region';
import {DiskTextMap} from '@pages/cluster/create/disk-config';
import {DiskStorageSizeHelpTip, DiskTotalStorageSizeHelpTip, Units, DiskSizeLimit} from '@common/config';

const klass = 'm-scale-disk';

const commonTpl = html`
    <div slot="h-storageSize" class="${klass}__head-tip">
        <span>{{col.label}}</span>
        <tip-icon type="question">${DiskStorageSizeHelpTip} </tip-icon>
    </div>
    <div slot="h-totalStorageSize" class="${klass}__head-tip">
        <span>{{col.label}}</span>
        <tip-icon type="question">${DiskTotalStorageSizeHelpTip}</tip-icon>
    </div>
    <div slot="c-storageType">{{row.storageType | formatStorageType}}</div>
`;

export class DiskScale extends BaseScale {
    static template = html`
        <template>
            <div class="${klass}">
                <div class="${klass}__item ${klass}__origin">
                    <label class="${klass}__label">变更前：</label>
                    <s-table columns="{{columns}}" datasource="{{originDatasource}}">
                        ${commonTpl}
                        <div slot="c-storageSize">{{row.storageSize}}${Units.GB}</div>
                    </s-table>
                </div>
                <div class="${klass}__item ${klass}__new">
                    <label class="${klass}__label">变更后：</label>
                    <s-table columns="{{columns}}" datasource="{{newDatasource}}">
                        ${commonTpl}
                        <div slot="c-storageSize">
                            <s-inputnumber
                                value="{= storageSize =}"
                                min="{{detail.storageSize}}"
                                max="{{DiskSizeLimit.max}}"
                                step="{{10}}"
                                stepStrictly
                                on-change="onStorageSizeChange"
                                width="{{100}}"
                            />
                            <span>${Units.GB}</span>
                        </div>
                        <div slot="c-totalStorageSize">{{storageSize | filterTotalStorageSize(detail)}}</div>
                    </s-table>
                </div>
            </div>
        </template>
    `;

    static components = {
        's-table': Table,
        'tip-icon': TipIcon,
        's-inputnumber': InputNumber
    };

    static computed = {};

    static filters = {
        formatStorageType,
        filterTotalStorageSize: (storageSize: IClusterDetail['storageSize'], detail: IClusterDetail) => {
            const {numberOfDisks, storageType, numberOfBrokerNodes} = detail;
            return formatTotalStorageSize({
                storageSize,
                numberOfDisks,
                storageType,
                numberOfBrokerNodes
            });
        }
    };

    initData() {
        return {
            columns: [
                {name: 'storageSize', label: DiskTextMap.diskStorage},
                {name: 'totalStorageSize', label: DiskTextMap.totalStorageSize},
                {
                    name: 'storageType',
                    label: DiskTextMap.storageType
                }
            ],
            datasource: [],
            diskStorage: '',
            DiskSizeLimit
        };
    }

    attached() {
        this.handleDatasource();
    }

    handleDatasource() {
        const detail = this.data.get('detail') as IClusterDetail;
        const {storageSize, numberOfDisks, storageType, numberOfBrokerNodes} = detail;
        const datasource = [
            {
                storageSize,
                totalStorageSize: formatTotalStorageSize({
                    storageSize,
                    numberOfDisks,
                    storageType,
                    numberOfBrokerNodes
                }),
                storageType
            }
        ];
        this.data.set('originDatasource', datasource);
        this.data.set('newDatasource', datasource);
        this.data.set('storageSize', storageSize);
        this.handlePriceChange();
    }

    onStorageSizeChange(e: {value: IClusterDetail['storageSize']}) {
        this.data.set('storageSize', e.value);
        this.handlePriceChange();
    }

    handlePriceChange() {
        this.fire('get-price', {});
    }

    getPriceParams() {
        const storageSize = this.data.get('storageSize');
        const {storageType, numberOfDisks} = this.data.get('detail');
        return {
            storageMeta: {
                storageSize,
                storageType,
                numberOfDisks
            }
        };
    }

    async check() {
        const {storageSize: originStorageSize} = this.data.get('detail') as IClusterDetail;
        const storageSize = this.data.get('storageSize');

        if (originStorageSize === storageSize) {
            return Promise.reject('');
        }

        return Promise.resolve();
    }

    getOrderItemData() {
        const {
            payment,
            region,
            arch,
            mode,
            flushDiskType,
            numberOfNodesPerBroker,
            zoneNames,
            numberOfDisks,
            numberOfBrokerNodes,
            storageType
        } = this.data.get('detail') as IClusterDetail;

        const storageSize = this.data.get('storageSize');

        return {
            title: '变更后节点配置',
            class: `${klass}__order`,
            datasource: [
                {label: '所在地区：', value: formatRegion(region)},
                {label: PaymentTextMap.payment, value: formatPayment(payment)},
                {
                    label: NodeConfigTextMap.arch,
                    value: formatArchFlush({
                        arch,
                        flushDiskType,
                        dledgerNumberOfNodesPerBroker: numberOfNodesPerBroker,
                        masterSlaveNumberOfNodesPerBroker: numberOfNodesPerBroker - 1
                    })
                },

                {label: NodeConfigTextMap.mode, value: formatMode({mode, selectedOrderZones: zoneNames})},
                {label: DiskTextMap.storageType, value: formatStorageType(storageType)},
                {label: DiskTextMap.storageSize, value: storageSize + Units.GB},

                {
                    label: DiskTextMap.totalStorageSize,
                    value: formatTotalStorageSize({storageSize, numberOfDisks, storageType, numberOfBrokerNodes})
                }
            ]
        };
    }

    getOrderParams() {
        const storageSize = this.data.get('storageSize');
        return {
            diskSizeGB: storageSize
        };
    }
}
