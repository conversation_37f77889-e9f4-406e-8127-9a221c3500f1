import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import './index.less';
import {OrderConfirmItem} from '@pages/cluster/components/order-item';

const klass = 'page-scale-order-confirm';
export class OrderConfirm extends Component {
    static template = html`
        <div class="${klass}">
            <order-confirm-item item="{{orderData}}" />
        </div>
    `;

    static components = {
        'order-confirm-item': OrderConfirmItem,
    };

    initData() {
        return {
            // 由组件传入
            orderData: {},
        };
    }
}
