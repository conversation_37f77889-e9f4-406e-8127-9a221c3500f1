@create-width: 1180px;

.page-cluster-scale {
    flex-direction: column !important;

    &__step {
        width: 300px;
        margin: 24px auto;
    }

    .s-create-page-content {
        margin: 0 auto;
        padding: 0 24px 96px;
    }

    &__content {
        width: @create-width;
        border-radius: 4px;

        .m-card {
            padding-bottom: 4px;
        }
    }

    &__current-config {
        .detail-cell {
            &:nth-child(3n + 1) label,
            &:nth-child(3n + 2) label {
                width: 74px;
            }
        }
    }

    &__scale {
        .s-form-item-label > label {
            padding-left: 0;
        }
    }

    .s-create-page-footer {
        width: 100%;
        height: 80px;
        margin: 0 auto;
        padding: 0 24px;

        .page-footer-wrapper {
            width: @create-width;

            .s-button {
                width: 88px;
                padding: 0;

                &.s-button-loading .s-loading {
                    margin-left: 0;
                }
            }
        }
    }

    &__scale-content {
        width: 932px;
        background: #f7f7f9;
        border-radius: 6px;
        margin: 16px 0 0;
        padding: 16px 16px 0;

        .s-col {
            position: initial;
        }
    }

    &__price-item {
        margin-left: 16px;
    }

    &__coupon {
        background: #ffff;
        border-top: none;
        padding: 24px;
        margin-top: 16px;
        border-radius: 6px;
    }
}

.m-scale-node-type,
.m-scale-broker-number,
.m-scale-disk {
    &__item {
        display: flex;
    }

    &__label {
        font-size: 12px;
        color: #5c5f66;
        line-height: 20px;
        margin-top: 10px;
        margin-right: 16px;
    }

    .s-table {
        position: initial;

        .s-table-thead {
            background: #edeef0;
        }

        .s-table-body {
            position: initial;
        }
    }

    &__origin {
        margin-bottom: 16px;
    }

    &__head-tip {
        display: flex;
        align-items: center;
    }
}
