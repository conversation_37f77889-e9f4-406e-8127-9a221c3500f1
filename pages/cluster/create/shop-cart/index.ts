/**
 * 购物车：footer价格展示
 * @file index.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import './index.less';
import {DefaultPaymentType, Payment} from '@common/enums';
import {PriceItem} from '@pages/cluster/components';
import {formatComputePrice, formatPrice} from '@common/utils';

const klass = 'm-shop-cart';

export const ShopCartTextMap = {
    // 预付费 | 后付费的第一步
    clusterPrice: '集群费用：',
    networkPrice: '网络费用：',
    // 后付费的第二步
    totalPrice: '总费用：',

    // 预付费的第二步
    totalMoney: '总金额：',
    couponPrice: '抵扣金额：',
    finalPrice: '实际金额：',
};

export class ShopCart extends Component {
    static template = html`
        <div class="${klass}">
            <div class="${klass}__wrap" s-if="current === 1">
                <price-item
                    title="${ShopCartTextMap.clusterPrice}"
                    price="{{priceInfo.cluster}}"
                    desc="{{price.cluster | formatComputePrice(payment)}}"
                />
                <price-item
                    s-if="price.publicAccess"
                    title="${ShopCartTextMap.networkPrice}"
                    price="{{priceInfo.publicAccess}}"
                    desc="{{price.publicAccess | formatComputePrice(payment)}}"
                />
            </div>
            <template s-if="current === 2">
                <div s-if="{{payment === Payment.Postpaid}}" class="${klass}__wrap">
                    <price-item
                        title="${ShopCartTextMap.totalPrice}"
                        price="{{totalPrice | formatPrice(payment, timeLength, timeUnit)}}"
                        desc="{{totalPrice | formatComputePrice(payment)}}"
                    />
                </div>
                <div s-else class="${klass}__wrap">
                    <price-item title="${ShopCartTextMap.totalMoney}" price="{{totalPrice}}" />
                    <price-item title="${ShopCartTextMap.couponPrice}" price="{{decountPrice}}" />
                    <price-item title="${ShopCartTextMap.finalPrice}" price="{{finalPrice}}" />
                </div>
            </template>
        </div>
    `;
    static components = {
        'price-item': PriceItem,
    };

    static filters: SanFilterProps = {
        formatPrice,
        formatComputePrice: (clusterPrice: number, payment: Payment) => {
            if (payment === Payment.Prepaid) {
                return '';
            }

            return formatComputePrice(clusterPrice);
        },
    };

    initData() {
        return {
            /** ---- 由外层组件传入  ---- start */
            price: {
                cluster: 0,
                payment: DefaultPaymentType,
                publicAccess: 0,
            },
            payment: DefaultPaymentType,
            /** ---- 由外层组件传入  ---- end */
            Payment,
        };
    }
}
