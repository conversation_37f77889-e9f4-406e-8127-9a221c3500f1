/**
 * 磁盘配置模块
 *
 * @file disk-config.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Select, Form, InputNumber, Table, Input} from '@baidu/sui';
import {DiskType} from '@baidu/bce-bcc-sdk-enum';
import {DiskSizeLimit, DiskStorageSizeHelpTip, DiskTotalStorageSizeHelpTip, Units} from '@common/config';
import {VALIDATE_ITEMS} from '@common/rules';
import {ClusterRefType, diskNums2, diskNums4, diskNums8} from '@common/enums';
import BaseCmpt from '../base-cmpt';
import './index.less';
import {formatStorageType} from '@common/utils';
import {BlockBox} from '@components/index';
import {InstanceFlavor} from './../node-config/index.d';

const klass = 'm-disk-config';

export const DiskTextMap = {
    title: '存储配置',
    storageType: '磁盘类型：',
    storageSize: '单节点容量：',
    totalStorageSize: '总磁盘容量：',
    diskNum: '个',
    diskStorage: '磁盘大小：'
};
export class DiskConfig extends BaseCmpt {
    static template = html`
        <div class="${klass}">
            <block-box title="${DiskTextMap.title}">
                <s-form s-ref="form" rules="{{rules}}" data="{= formData =}">
                    <s-formitem label="${DiskTextMap.storageType}" prop="storageType">
                        <s-select
                            datasource="{{diskTypes}}"
                            value="{= formData.storageType =}"
                            disabled="{{payload === 'low'}}"
                            on-change="onChangeStorageType"
                            placeholder="{{selectPlaceholder}}"
                            loading="{{flavorloading}}"
                            class="common-width-item"
                        />
                    </s-formitem>
                    <s-formitem
                        s-if="whiteListMap.ROCKETMQ_MultipleDisks"
                        label="${DiskTextMap.storageSize}"
                        class="form-item-disk-size"
                    >
                        <s-table
                            datasource="{{storageMetaSource}}"
                            columns="{{storageMetaColumns}}"
                            class="storage-table"
                            bordered
                        >
                            <div slot="c-diskNum">
                                <s-select
                                    datasource="{{diskNums}}"
                                    value="{= row.diskNum =}"
                                    width="80"
                                    getPopupContainer="{{getPopupContainer}}"
                                    on-change="onDiskChange('diskNum', $event, rowIndex)"
                                />
                                <span>${DiskTextMap.diskNum}</span>
                            </div>
                            <div slot="c-diskStorage">
                                <s-inputnumber
                                    value="{= row.diskStorage =}"
                                    min="{{DiskSizeLimit.min}}"
                                    max="{{DiskSizeLimit.max}}"
                                    step="{{10}}"
                                    stepStrictly
                                    on-change="onDiskChange('diskStorage', $event, rowIndex)"
                                />
                                <span>${Units.GB}</span>
                            </div>
                            <span slot="c-total">
                                {{row.total}}
                                <span>${Units.GB}</span>
                            </span>
                        </s-table>
                    </s-formitem>
                    <s-formitem s-else label="${DiskTextMap.diskStorage}" help="${DiskStorageSizeHelpTip}">
                        <s-inputnumber
                            value="{= storageMetaSource[0].diskStorageSize =}"
                            min="{{DiskSizeLimit.min}}"
                            max="{{DiskSizeLimit.max}}"
                            step="{{10}}"
                            stepStrictly
                            on-change="onDiskChange('diskStorage', $event, 0)"
                            class="common-width-item"
                        />
                        <span>${Units.GB}</span>
                    </s-formitem>
                    <s-formitem label="${DiskTextMap.totalStorageSize}" help="${DiskTotalStorageSizeHelpTip}">
                        <s-inputnumber value="{{totalDiskStorageSize}}" readonly class="common-width-item" />
                        ${Units.GB}
                    </s-formitem>
                </s-form>
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        's-inputnumber': InputNumber,
        's-input': Input,
        's-select': Select,
        's-form': Form,
        's-formitem': Form.Item,
        's-table': Table
    };

    static computed = {
        diskNums(): any[] {
            const {ROCKETMQ_MultipleDisksAnyNumber, ROCKETMQ_MultipleDisksNumber} = this.data.get('whiteListMap');
            const storageType = this.data.get('formData.storageType');
            if (ROCKETMQ_MultipleDisksAnyNumber) {
                // 白名单支持任意数量的磁盘——》可选择1-8个磁盘
                return [1, 2, 3, 4, 5, 6, 7, 8];
            }

            if (storageType === DiskType.SSD && ROCKETMQ_MultipleDisksNumber) {
                // 选择高性能云磁盘 & ROCKETMQ_MultipleDisksNumber白名单 ——》可选择1、2、4、8个磁盘
                return diskNums8.toArray();
            } else if (storageType === DiskType.ENHANCED_SSD_PL1 && !ROCKETMQ_MultipleDisksNumber) {
                // 选择增强型SSD & 未配置ROCKETMQ_MultipleDisksNumber白名单——》可选择1-2个磁盘
                return diskNums2.toArray();
            }

            return diskNums4.toArray();
        },
        totalDiskStorageSize(): number {
            const {ROCKETMQ_MultipleDisks} = this.data.get('whiteListMap');
            const total = this.data.get('storageMetaSource[0].total');
            const diskStorageSize = this.data.get('storageMetaSource[0].diskStorageSize');
            // 监听到总节点数量修改时，会重新设置
            const numberOfBrokerNodes = this.data.get('numberOfBrokerNodes') || 1;
            return (ROCKETMQ_MultipleDisks ? total : diskStorageSize) * numberOfBrokerNodes;
        }
    };

    static filters: SanFilterProps = {
        formatStorageType
    };

    initData() {
        return {
            rules: {
                storageType: [VALIDATE_ITEMS.requiredSelect]
            },
            formData: {
                storageType: ''
            },
            storageMetaColumns: [
                {name: 'diskNum', label: '磁盘个数'},
                {name: 'diskStorageSize', label: '单盘容量'},
                {name: 'total', label: '总容量'}
            ],
            diskTypes: [],
            storageMetaSource: [{diskNum: 1, diskStorageSize: 100, total: 100}],
            getPopupContainer: () => document.body,
            DiskSizeLimit
        };
    }

    async attached() {}

    handleCdsFlavorChange({cdsFlavors = []}: Pick<InstanceFlavor, 'cdsFlavors'>) {
        const diskTypes = _.map(cdsFlavors, i => ({
            value: i.storageType,
            text: DiskType.getTextFromValue(i.storageType)
        }));
        this.data.set('diskTypes', diskTypes);
    }

    handleNumberOfBrokerNodesChange(e: {numberOfBrokerNodes: number}) {
        this.data.set('numberOfBrokerNodes', e.numberOfBrokerNodes);
    }

    // 校验
    verify() {
        const ref = this.ref('form') as any;
        return Promise.all([ref.validateFields(['storageType'])]).catch((err: Error) => {
            throw ClusterRefType.DISK;
        });
    }

    onChangeStorageType(target: {value: string}) {
        this.data.set('formData.storageType', target.value);
        this.onDiskChange('diskNum', {value: 1}, 0);
    }

    // 磁盘大小或者磁盘容量的改变
    onDiskChange(type: 'diskNum' | 'diskStorageSize', target: {value: number}, rowIndex: number) {
        const item = this.data.get(`storageMetaSource[${rowIndex}]`);
        const total = target.value * (type === 'diskNum' ? item.diskStorageSize : item.diskNum);
        this.data.merge(`storageMetaSource[${rowIndex}]`, {
            [type]: target.value,
            total
        });
        this.nextTick(() => {
            this.onPriceConfigChange();
        });
    }

    onPriceConfigChange() {
        this.fire('price-config-change', {});
    }

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    getPriceData() {
        const {formData, storageMetaSource} = this.data.get('');
        const {storageType} = formData;
        return {
            storageSize: storageMetaSource[0].diskStorageSize,
            numberOfDisks: storageMetaSource[0].diskNum,
            storageType
        };
    }

    getCheckData() {
        return {
            ...this.getPriceData()
        };
    }

    getOrderItemData() {
        const {numberOfDisks, storageSize, storageType} = this.getPriceData();
        const totalDiskStorageSize = this.data.get('totalDiskStorageSize');
        const {ROCKETMQ_MultipleDisks} = this.data.get('whiteListMap');
        const nodeStorageSizeText = ROCKETMQ_MultipleDisks
            ? `${numberOfDisks * storageSize}${Units.GB}（${storageSize}${Units.GB} × ${numberOfDisks}）`
            : `${numberOfDisks * storageSize}${Units.GB}`;
        const res = {
            title: DiskTextMap.title,
            datasource: [
                {
                    label: DiskTextMap.storageType,
                    value: formatStorageType(storageType)
                },
                {
                    label: DiskTextMap.storageSize,
                    value: nodeStorageSizeText
                },
                {
                    label: DiskTextMap.totalStorageSize,
                    value: `${totalDiskStorageSize}${Units.GB}`
                }
            ]
        };
        return res;
    }

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    getConfirmData() {
        return {
            ...this.getPriceData()
        };
    }
}
