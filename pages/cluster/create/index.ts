/**
 * @file 创建集群
 * <AUTHOR>
 */

import {html, decorators, redirect, CreatePage} from '@baiducloud/runtime';
import {ClusterCreateSuccUrl, ROUTE_PATH} from '@common/config/route';
import './index.less';
import {PageHeader} from '@components/index';
import {Steps, Button, Loading} from '@baidu/sui';
import {AppCreatePage} from '@baidu/sui-biz';
import {PaymentRegion} from './payment-region';
import {formatPrice, isPrepaid, setDisplay} from '@common/utils';
import {ClusterConfig} from './cluster-config';
import {NodeConfig} from './node-config';
import {DiskConfig} from './disk-config';
import {api} from '@common/client';
import {ClusterRefType, DefaultPaymentType, Payment} from '@common/enums';
import {InstanceFlavor} from './node-config/index.d';
import {NetworkConfig} from './network-config';
import {AccessConfig} from './access-config';
import {TagConfig} from './tag-config';
import {ShopCart} from './shop-cart';
import {PriceResult} from './index.d';
import {OrderConfirm} from './order-confirm';
import {CouponConfig} from '../components/coupon-config';
import BigNumber from 'bignumber.js';
import {ServiceType} from '@common/config';
import {isOneCloudId} from '@common/utils/common';
const LoadingDialog = Loading.LoadingDialog;

const klass = 'page-cluster-create';
@decorators.asPage(ROUTE_PATH.clusterCreate)
export default class ClusterCreate extends CreatePage {
    REGION_CHANGE_LOCATION = `#${ROUTE_PATH.clusterList}`;
    static template = html`<template>
        <biz-create-page class="${klass}" pageTitle="创建集群" backTo="${ROUTE_PATH.clusterList}">
            <div class="${klass}__step">
                <s-steps current="{{current}}" type="normal">
                    <s-step title="信息配置" />
                    <s-step title="确认订单" />
                </s-steps>
            </div>
            <div class="${klass}__content" style="{{current === 1 | setDisplay}}">
                <payment-region s-ref="${ClusterRefType.REGION}" on-price-config-change="onPriceConfigChange" />
                <cluster-config s-ref="${ClusterRefType.CLUSTER}" priceInfo="{{priceInfo}}" />
                <node-config
                    s-ref="${ClusterRefType.NODE}"
                    whiteListMap="{{whiteListMap}}"
                    on-flavor-change="onFlavorChange"
                    on-numberOfBrokerNodes-change="onNumberOfBrokerNodesChange"
                    on-zone-change="onZoneChange"
                    on-price-config-change="onPriceConfigChange"
                />
                <disk-config
                    s-ref="${ClusterRefType.DISK}"
                    whiteListMap="{{whiteListMap}}"
                    on-price-config-change="onPriceConfigChange"
                />
                <network-config
                    s-ref="${ClusterRefType.NETWORK}"
                    on-publicAccessEnabled-change="onPublicAccessEnabledChange"
                    on-price-config-change="onPriceConfigChange"
                    priceInfo="{{priceInfo}}"
                />
                <access-config s-ref="${ClusterRefType.ACCESS}" />
                <tag-config s-ref="${ClusterRefType.TAG}" s-if="{{!isOneCloud}}" />
            </div>
            <div class="${klass}__content" style="{{current === 2 | setDisplay}}">
                <order-confirm orderConfirmList="{{orderConfirmList}}" />
                <coupon-config
                    s-if="isPrepaid"
                    s-ref="${ClusterRefType.COUPON}"
                    decountPrice="{{decountPrice}}"
                    on-coupon-select-change="onCouponSelectChange"
                />
            </div>
            <footer slot="pageFooter" class="${klass}__footer">
                <template s-if="current === 1">
                    <s-button on-click="onCancel" size="large">取消</s-button>
                    <s-button on-click="goNext" size="large" skin="primary" class="ml16" loading="{{loading.next}}">
                        下一步
                    </s-button>
                </template>
                <template s-else>
                    <s-button on-click="goPrev" size="large">上一步</s-button>
                    <s-button
                        on-click="onConfirm"
                        size="large"
                        class="ml16"
                        skin="primary"
                        loading="{{loading.confirm}}"
                    >
                        确认订单
                    </s-button>
                </template>
                <shop-cart
                    s-ref="ShopCart"
                    current="{{current}}"
                    priceInfo="{{priceInfo}}"
                    price="{{price}}"
                    totalPrice="{{totalPrice}}"
                    decountPrice="{{decountPrice}}"
                    finalPrice="{{finalPrice}}"
                    payment="{{paymentRegion.payment}}"
                    timeLength="{{paymentRegion.timeLength}}"
                    timeUnit="{{paymentRegion.timeUnit}}"
                />
            </footer>
        </biz-create-page>
    </template> `;

    static components = {
        'biz-create-page': AppCreatePage,
        'page-header': PageHeader,
        's-steps': Steps,
        's-step': Steps.Step,
        's-button': Button,
        'payment-region': PaymentRegion,
        'cluster-config': ClusterConfig,
        'node-config': NodeConfig,
        'disk-config': DiskConfig,
        'network-config': NetworkConfig,
        'access-config': AccessConfig,
        'tag-config': TagConfig,
        'shop-cart': ShopCart,
        'order-confirm': OrderConfirm,
        'coupon-config': CouponConfig
    };

    static filters: SanFilterProps = {
        setDisplay
    };

    static computed: SanComputedProps = {
        priceInfo() {
            const {payment, timeLength} = this.data.get('paymentRegion');
            const price = this.data.get('price');
            return {
                cluster: formatPrice(price.cluster, payment, timeLength),
                publicAccess: formatPrice(price.publicAccess, payment, timeLength)
            };
        },
        totalPrice(): number {
            const clusterPrice = new BigNumber(this.data.get('price.cluster'));
            const publicAccessPrice = new BigNumber(this.data.get('price.publicAccess'));
            const total = clusterPrice.plus(publicAccessPrice).toNumber();
            return total;
        },
        decountPrice(): number {
            const couponPrice = this.data.get('currentSelectCoupon.balance') || 0;
            const totalPrice = this.data.get('totalPrice');

            return couponPrice > totalPrice ? totalPrice : couponPrice;
        },
        finalPrice() {
            const totalPrice = new BigNumber(this.data.get('totalPrice'));
            const couponPrice = this.data.get('currentSelectCoupon.balance') || 0;
            const res = totalPrice.minus(couponPrice).toNumber();
            return res <= 0 ? 0 : res;
        },
        isPrepaid() {
            const {payment} = this.data.get('paymentRegion');
            return isPrepaid(payment);
        }
    };

    initData() {
        return {
            current: 1,
            /** ------- 多个子组件共享的数据 -----start */
            whiteListMap: {},
            // 付费及地域会修改的数据
            paymentRegion: {
                payment: DefaultPaymentType,
                timeLength: 1
            },
            price: {
                cluster: 0,
                publicAccess: 0,
                payment: DefaultPaymentType
            },
            currentSelectCoupon: null,
            /** ------- 多个子组件共享的数据 -----end */
            orderConfirmList: [],
            loading: {
                next: false,
                confirm: false
            },
            isOneCloud: isOneCloudId()
        };
    }

    async attached() {
        this.getWhiteList();
    }

    // 创建集群需要白名单控制的功能都可以通过这个接口请求
    async getWhiteList() {
        const res = await api.getUserWhiteListAcl({
            featureTypes: [
                'ROCKETMQ_NodesPerBroker',
                'ROCKETMQ_FlushType',
                'ROCKETMQ_MultipleDisks',
                'ROCKETMQ_MultipleDisksNumber',
                'ROCKETMQ_MultipleDisksAnyNumber'
            ]
        });

        this.data.set('whiteListMap', res || {});
    }

    onFlavorChange(e: InstanceFlavor) {
        (this.ref(ClusterRefType.DISK) as DiskConfig).handleCdsFlavorChange({
            cdsFlavors: e.cdsFlavors
        });
    }

    onNumberOfBrokerNodesChange(e: {numberOfBrokerNodes: number}) {
        (this.ref(ClusterRefType.DISK) as DiskConfig).handleNumberOfBrokerNodesChange(e);
    }

    onZoneChange(e) {
        (this.ref(ClusterRefType.NETWORK) as NetworkConfig).handleZoneChange(e);
    }

    onPublicAccessEnabledChange(e: {publicAccessEnabled: boolean}) {
        (this.ref(ClusterRefType.ACCESS) as AccessConfig).handlePublicAccessEnabledChange(e);
    }

    onCancel() {
        redirect(`#${ROUTE_PATH.clusterList}`);
    }

    goPrev() {
        const current = this.data.get('current');
        this.data.set('current', current - 1);
    }

    onPriceConfigChange(data: NormalObject) {
        const {mergeData, name} = data;
        if (name && mergeData) {
            this.data.merge(`${name}`, mergeData);
        }
        this.getPrices();
    }

    async getPrices() {
        const refRegion = this.ref(ClusterRefType.REGION) as PaymentRegion;
        const refNode = this.ref(ClusterRefType.NODE) as NodeConfig;
        const refDisk = this.ref(ClusterRefType.DISK) as DiskConfig;
        const refNetwork = this.ref(ClusterRefType.NETWORK) as NetworkConfig;
        let params = {
            ...refRegion.getPriceData(),
            ...refNode.getPriceData(),
            ...refDisk.getPriceData(),
            ...refNetwork.getPriceData()
        };

        // 检测是否有值有空 除了publicAccessEnabled、publicAccessBandwidth
        const hasEmpty = this.checkHasEmpty(params);
        if (hasEmpty) {
            return Promise.reject();
        }
        const res = (await api.getPrices(params)) as PriceResult;
        this.data.set('price', res);
    }

    checkHasEmpty(priceData: NormalObject): boolean {
        let flag = false;
        for (let key of Object.keys(priceData)) {
            const excludeKeys = ['publicAccessEnabled', 'publicAccessBandwidth'];
            if (!excludeKeys.includes(key) && !priceData[key]) {
                flag = true;
                break;
            }
        }
        return flag;
    }

    async goNext() {
        const refRegion = this.ref(ClusterRefType.REGION) as PaymentRegion;
        const refCluster = this.ref(ClusterRefType.CLUSTER) as ClusterConfig;
        const refNode = this.ref(ClusterRefType.NODE) as NodeConfig;
        const refDisk = this.ref(ClusterRefType.DISK) as DiskConfig;
        const refNetwork = this.ref(ClusterRefType.NETWORK) as NetworkConfig;
        const refAccess = this.ref(ClusterRefType.ACCESS) as AccessConfig;
        const isOneCloud = this.data.get('isOneCloud');
        const refTag = this.ref(ClusterRefType.TAG) as TagConfig;
        this.data.set('loading.next', true);
        Promise.all([
            refRegion.verify(),
            refCluster.verify(),
            refNode.verify(),
            refDisk.verify(),
            refNetwork.verify(),
            isOneCloud ? true : refTag.verify()
        ])
            .then(async () => {
                try {
                    this.showLoading();
                    let checkParams = {
                        ...refRegion.getCheckData(),
                        ...refNode.getCheckData(),
                        ...refDisk.getCheckData(),
                        ...refNetwork.getCheckData(),
                        ...refAccess.getCheckData()
                    };
                    await api.checkResources(checkParams);

                    const current = this.data.get('current');
                    this.data.set('current', current + 1);

                    const orderConfirmList = [
                        refRegion.getOrderItemData(),
                        refCluster.getOrderItemData(),
                        refNode.getOrderItemData(),
                        refDisk.getOrderItemData(),
                        refNetwork.getOrderItemData(),
                        refAccess.getOrderItemData()
                    ];
                    this.data.set('orderConfirmList', orderConfirmList);
                } catch (err) {
                    console.error(err);
                } finally {
                    this.hideLoading();
                }
            })
            .catch(err => {
                console.error(err);
                const errorForm = document.querySelector('.s-form-item-error');
                errorForm && errorForm.scrollIntoView({behavior: 'smooth', block: 'center'});
            })
            .finally(() => {
                this.data.set('loading.next', false);
            });
    }

    showLoading() {
        LoadingDialog.show({
            size: 'large',
            tip: '正在校验资源...'
        });
    }

    hideLoading() {
        LoadingDialog.hide();
    }

    onCouponSelectChange(data: {currentSelectCoupon: any}) {
        this.data.set('currentSelectCoupon', data.currentSelectCoupon);
    }

    async onConfirm() {
        const refRegion = this.ref(ClusterRefType.REGION) as PaymentRegion;
        const refCluster = this.ref(ClusterRefType.CLUSTER) as ClusterConfig;
        const refNode = this.ref(ClusterRefType.NODE) as NodeConfig;
        const refDisk = this.ref(ClusterRefType.DISK) as DiskConfig;
        const refNetwork = this.ref(ClusterRefType.NETWORK) as NetworkConfig;
        const refAccess = this.ref(ClusterRefType.ACCESS) as AccessConfig;
        const refTag = this.ref(ClusterRefType.TAG) as TagConfig;
        const isOneCloud = this.data.get('isOneCloud');
        const tagConfirmData = isOneCloud ? {} : await refTag.getConfirmData();
        const confirmParams: NormalObject = {
            ...refRegion.getConfirmData(),
            ...refCluster.getConfirmData(),
            ...refNode.getConfirmData(),
            ...refDisk.getConfirmData(),
            ...refNetwork.getConfirmData(),
            ...refAccess.getConfirmData(),
            ...tagConfirmData
        };

        if (confirmParams.payment === Payment.Prepaid) {
            const currentSelectCoupon = this.data.get('currentSelectCoupon');
            currentSelectCoupon && (confirmParams.couponIds = [currentSelectCoupon.id.toString()]);
        }

        try {
            this.data.set('loading.confirm', true);
            const res = await api.createCluster(confirmParams);
            const {orderId} = res;
            this.goOrder(confirmParams.payment, orderId, 'NEW');
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('loading.confirm', false);
        }
    }

    // 确认订单页
    goOrder(payment: Payment, orderId: string, orderType: string) {
        redirect(`${ClusterCreateSuccUrl.Prepaid}${ServiceType}&orderType=${orderType}&orderId=${orderId}`);
    }
}
