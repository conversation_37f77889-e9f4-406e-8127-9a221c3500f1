.m-network-config {
    margin-top: 40px;

    .form-item-vpc {
        .s-form-item-control-wrapper {
            flex: 1;
        }
    }

    &__sub-form-wrapper {
        margin-top: 16px;
        padding: 16px 24px;
        background: var(--background-color);
        border-radius: 6px;

        .sub-form-item-last {
            margin-bottom: 0;
        }

        .s-form-item-label > label {
            padding-left: 0;
            width: auto;
            margin-right: 8px;
        }

        .s-select {
            background: #fff;
        }
    }

    .form-item-public-ip-bwidth {
        .s-form-item-help {
            margin-top: 4px;
        }
    }

    &__order {
        .detail-cell {
            &:nth-child(1),
            &:nth-child(2) {
                label {
                    width: 62px;
                }
            }
        }

        .m-block-box > .s-detail-cell {
            > .detail-cell:first-child {
                width: 100% !important;
            }
        }
    }
}
