/**
 * 返回结果，返回一个对象
 */
export interface VpcResult {
    /**
     * vpc列表
     */
    vpcs: Vpc[];
    [property: string]: any;
}

export interface Vpc {
    /**
     * VPC名称
     */
    name: string;
    /**
     * VPC 短 ID
     */
    vpcId: string;
    [property: string]: any;
}

/**
 * 返回结果，返回一个对象
 */
export interface SubnetResult {
    /**
     * 可用区子网信息
     */
    zoneSubnets: Subnetzone[];
    [property: string]: any;
}

export interface Subnetzone {
    /**
     * 子网列表，该可用区下的子网列表
     */
    subnets: Subnet[];
    /**
     * 可用区ID
     */
    zone: string;
    [property: string]: any;
}

export interface Subnet {
    /**
     * 子网名称
     */
    name: string;
    /**
     * 子网短 ID
     */
    subnetId: string;
    vpcId: string;
    /**
     * 可用区 ID
     */
    zone: string;
    [property: string]: any;
}

/**
 * 返回结果，返回一个对象
 */
export interface SecurityGroupResult {
    /**
     * 安全组列表
     */
    securityGroups: SecurityGroup[];
    [property: string]: any;
}

export interface SecurityGroup {
    /**
     * 安全组名称
     */
    name: string;
    /**
     * 安全组短 ID
     */
    securityGroupId: string;
    /**
     * VPC 短 ID
     */
    vpcId: string;
    [property: string]: any;
}
