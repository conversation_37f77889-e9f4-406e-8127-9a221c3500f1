/**
 * @file index.ts
 * <AUTHOR>
 */

import {BlockBox} from '@components/block-box';
import BaseCmpt from '../base-cmpt';
import {html} from '@baiducloud/runtime';
import {Form, Select, Switch, Slider, Alert} from '@baidu/sui';
import {INPUT_WIDTH, PublicIpWidthSliderMarks, SELECT_HEIGHT, Units} from '@common/config';
import {api} from '@common/client';
import _ from 'lodash';
import './index.less';
import {DocLink} from '@components/index';
import {VALIDATE_ITEMS} from '@common/rules';
import {SecurityGroup, SecurityGroupResult, Subnet, SubnetResult, Vpc, VpcResult} from './index.d';
import {formatSelectedOrderZones, renderZoneLabel, isOneCloudId} from '@common/utils';
import {ZoneListItem} from '../node-config/index.d';
import {PublicAccessModeList} from '@common/enums';
import {OrderItem} from '../../components/order-item/index.d';
import {renderSwitch} from '@common/html';

const klass = 'm-network-config';
export const NetworkTextMap = {
    title: '网络配置',
    vpcId: '网络实例：',
    securityGroupIds: '安全组：',
    subnetIds: '子网：',
    publicAccessEnabled: '公网访问：',
    publicAccessBandwidth: '公网带宽：'
};

export class NetworkConfig extends BaseCmpt {
    static template = html`
        <div class="${klass}">
            <block-box title="${NetworkTextMap.title}">
                <s-alert class="mb16">依据访问协议设置安全组出、入站的TCP协议端口以及IP范围。</s-alert>
                <s-form s-ref="form" rules="{{rules}}" data="{= formData =}">
                    <s-form-item label="${NetworkTextMap.vpcId}" prop="vpcId" class="form-item-vpc">
                        <div class="${klass}__vpc">
                            <s-select
                                datasource="{{vpcList}}"
                                value="{= formData.vpcId =}"
                                width="${INPUT_WIDTH}"
                                height="${SELECT_HEIGHT}"
                                noDataText="请前往VCP页面创建当前可用区下的子网"
                                on-change="onVpcChange"
                                on-visible-change="onGetVpcVisibleChange"
                            />
                            <doc-link href="/network/" skin="primary" class="ml8" s-if="{{!isOneCloud}}">
                                创建VPC
                            </doc-link>
                        </div>
                        <div class="${klass}__sub-form-wrapper" s-if="{{formData.vpcId && selectedOrderZones.length}}">
                            <s-form-item
                                class="${klass}__wrap_panel"
                                s-for="item,index in selectedOrderZones"
                                class="${klass}_sub-form {{index === selectedOrderZones.length - 1 ? 'sub-form-item-last' : ''}}"
                                label="{{item | renderZoneLabel}}："
                            >
                                <s-select
                                    datasource="{{zoneSubnetMap[item] || []}}"
                                    value="{= formData.subnetIds[index] =}"
                                    height="${SELECT_HEIGHT}"
                                    width="${INPUT_WIDTH}"
                                    on-change="onSubnetChange"
                                    on-visible-change="onSubnetVisibleChange"
                                />
                            </s-form-item>
                            <div class="s-form-item-error err-tip" s-if="errTip">{{errTip}}</div>
                        </div>
                    </s-form-item>

                    <s-form-item
                        s-if="formData.vpcId"
                        prop="securityGroupIds"
                        label="${NetworkTextMap.securityGroupIds}"
                    >
                        <s-select
                            datasource="{{securityGroupList}}"
                            multiple
                            value="{= formData.securityGroupIds =}"
                            on-change="onSecurityGroupChange"
                            width="${INPUT_WIDTH}"
                            height="${SELECT_HEIGHT}"
                        />
                        <doc-link
                            href="/network/#/vpc/security/create"
                            skin="primary"
                            class="ml8"
                            s-if="{{!isOneCloud}}"
                        >
                            创建安全组
                        </doc-link>
                    </s-form-item>
                    <s-form-item label="${NetworkTextMap.publicAccessEnabled}" prop="publicAccessEnabled">
                        <s-switch checked="{= formData.publicAccessEnabled =}" on-change="onPublicAccessEnabledChange" />
                    </s-form-item>
                    <s-form-item
                        label="${NetworkTextMap.publicAccessBandwidth}"
                        s-if="{{formData.publicAccessEnabled && formData.publicAccessMode === PublicAccessModeList.AUTO_ASSIGN}}"
                        help="公网按您实际选择的带宽计费"
                        prop="publicAccessBandwidth"
                        class="form-item-public-ip-bwidth"
                    >
                        <s-slider
                            showTooltip
                            showInput="{{true}}"
                            step="{{1}}"
                            min="{{1}}"
                            max="{{500}}"
                            marks="{{PublicIpWidthSliderMarks}}"
                            parts="{{4}}"
                            value="{=formData.publicAccessBandwidth=}"
                            on-change="onPublicAccessBandwidthChange"
                        />
                        <span class="ml5">${Units.Mbps}</span>
                    </s-form-item>
                </s-form>
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        's-alert': Alert,
        's-form': Form,
        's-form-item': Form.Item,
        's-select': Select,
        'doc-link': DocLink,
        's-switch': Switch,
        's-slider': Slider
    };

    initData() {
        return {
            rules: {
                vpcId: [VALIDATE_ITEMS.requiredSelect],
                subnetIds: [VALIDATE_ITEMS.requiredSelect],
                securityGroupIds: [VALIDATE_ITEMS.requiredSelect]
            },
            formData: {
                vpcId: '',
                // 可用区选择的子网
                subnetIds: [],
                securityGroupIds: [],
                publicAccessEnabled: false,
                // 目前暂不实现公网方式切换，仅支持自动分配，
                publicAccessMode: PublicAccessModeList.AUTO_ASSIGN,
                publicAccessBandwidth: 0
            },
            PublicIpWidthSliderMarks,
            PublicAccessModeList,
            vpcList: [],
            securityGroupList: [],
            // 可用区对应的子网列表
            zoneSubnetMap: {},

            zoneList: [], // 所有可用区
            logicalZones: [], // 选择的可用区修改后，会通过handleZoneChange更新该值
            // 由外层组件传入
            priceInfo: {},
            isOneCloud: isOneCloudId()
        };
    }

    static filters = {
        renderZoneLabel
    };

    static computed = {
        // 按照展示顺序排序后的可用区
        selectedOrderZones(): string[] {
            const logicalZones = this.data.get('logicalZones') as string[];
            const zoneList = this.data.get('zoneList') as ZoneListItem[];
            return formatSelectedOrderZones({logicalZones, zoneList});
        }
    };

    attached() {}

    handleZoneChange(e: {logicalZones: string[]; zoneList?: {text: string; value: string}[]; isInit?: boolean}) {
        this.data.set('logicalZones', e.logicalZones);
        e.zoneList && this.data.set('zoneList', e.zoneList);
        this.data.set('formData.subnetIds', []);
        this.data.set('errTip', '');
        if (!e.isInit) {
            this.nextTick(() => {
                const {vpcId} = this.data.get('formData');
                vpcId && this.getVpcSubnetList(vpcId);
            });
        }
    }

    // vpc下拉框下拉时，请求刷新下拉vpc列表
    onGetVpcVisibleChange(target: {value: string}) {
        target.value && this.getVpcList();
    }

    // 子网下拉框下拉时，请求刷新数据
    onSubnetVisibleChange(target: {value: boolean}) {
        target.value && this.getVpcSubnetList(this.data.get('formData.vpcId'));
    }

    // 获取vpc
    async getVpcList() {
        const res = (await api.vpcList()) as VpcResult;
        const vpcList = _.map(res?.vpcs, i => ({...i, text: i.name, value: i.vpcId}));
        this.data.set('vpcList', vpcList);
    }

    onVpcChange(e: {value: string}) {
        const vpcId = e.value;
        this.data.set('formData.subnetIds', []);
        this.data.set('formData.securityGroupIds', []);
        this.getVpcSubnetList(vpcId);
        this.getSecurityGroup(vpcId);
    }

    // 获取vpc下面的子网
    async getVpcSubnetList(vpcId: string) {
        const {logicalZones} = this.data.get('');
        const res = (await api.subnetList({
            vpcId,
            params: {
                zoneNames: logicalZones
            }
        })) as SubnetResult;
        const {zoneSubnets} = res || {};
        const zoneSubnetMap: Record<string, Subnet[]> = {};
        _.each(zoneSubnets, i => {
            zoneSubnetMap[i.zone] = i.subnets.map(item => ({...item, text: item.name, value: item.subnetId}));
        });
        this.data.set('zoneSubnetMap', zoneSubnetMap);
    }

    onSubnetChange() {
        this.nextTick(() => {
            this.verifySubnet();
        });
    }

    verifySubnet() {
        return new Promise<void>((resolve, reject) => {
            const logicalZones = this.data.get('logicalZones');
            const {subnetIds} = this.data.get('formData');

            const map = new Map();
            if (subnetIds.length < logicalZones.length) {
                this.data.set('errTip', `请选取${logicalZones.length}个子网`);
                return reject();
            }

            for (let item of subnetIds) {
                if (map.get(item)) {
                    this.data.set('errTip', '子网不能重复');
                    return reject();
                }
                map.set(item, true);
            }
            this.data.set('errTip', '');
            return resolve();
        });
    }

    // 获取安全组
    async getSecurityGroup(vpcId: string) {
        if (!vpcId) {
            return;
        }
        const res = (await api.securityGroupList({vpcId})) as SecurityGroupResult;
        const {securityGroups} = res || {};
        this.data.set(
            'securityGroupList',
            _.map(securityGroups, i => ({...i, text: i.name, value: i.securityGroupId}))
        );
    }

    onSecurityGroupChange(e: {value: string[]}) {
        const {value} = e;
        const isMaxLimit = value.length >= 2;
        const securityGroupList = this.data.get('securityGroupList');
        this.data.set(
            'securityGroupList',
            securityGroupList.map((item: SecurityGroup) => ({
                ...item,
                disabled: isMaxLimit && !value.includes(item.securityGroupId)
            }))
        );
    }

    onPublicAccessEnabledChange(e: {value: boolean}) {
        this.fire('publicAccessEnabled-change', {publicAccessEnabled: e.value});
        this.onPriceConfigChange();
    }

    onPublicAccessBandwidthChange() {
        this.onPriceConfigChange();
    }

    onPriceConfigChange() {
        this.nextTick(() => {
            this.fire('price-config-change', {});
        });
    }

    async verify() {
        const form = this.ref('form') as any;
        await Promise.all([form.validateFields(), this.verifySubnet()]);
    }

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    getPriceData() {
        const {publicAccessEnabled, publicAccessBandwidth, publicAccessMode} = this.data.get('formData');
        let params: NormalObject = {
            publicAccessEnabled,
            publicAccessMode
        };
        if (publicAccessEnabled) {
            params.publicAccessBandwidth = publicAccessBandwidth;
        }
        return params;
    }

    getCheckData() {
        const {publicAccessEnabled, vpcId, subnetIds, securityGroupIds} = this.data.get('formData');
        return {
            publicAccessEnabled,
            vpcId,
            subnetIds,
            securityGroupIds
        };
    }

    // 获取订单信息页面参数(注意，这个是给予父组件使用)，不能随便删除
    getOrderItemData(): OrderItem {
        const {formData, vpcList, zoneSubnetMap, selectedOrderZones, securityGroupList, priceInfo} = this.data.get('');
        const {vpcId, securityGroupIds, subnetIds, publicAccessEnabled, publicAccessBandwidth} = formData;
        const subnetDatasource = selectedOrderZones.map((item: string, index: number) => {
            return {
                label: `${renderZoneLabel(item)}：`,
                value: zoneSubnetMap[item].find((item: Subnet) => item.subnetId === subnetIds[index])?.name
            };
        });
        let priceText: OrderItem['priceInfo'];

        let datasource = [
            {
                label: NetworkTextMap.vpcId,
                value: vpcList.find((item: Vpc) => item.vpcId === vpcId)?.name,
                subDatasource: subnetDatasource,
                slot: 'sub-cell'
            },
            {
                label: NetworkTextMap.securityGroupIds,
                value: securityGroupList
                    .filter((item: SecurityGroup) => securityGroupIds.includes(item.securityGroupId))
                    .map((item: SecurityGroup) => item.name)
                    .join('、')
            },
            {
                label: NetworkTextMap.publicAccessEnabled,
                value: renderSwitch(publicAccessEnabled)
            }
        ];

        if (publicAccessEnabled) {
            priceText = {
                title: '网络费用：',
                price: priceInfo.publicAccess
            };
            datasource.push({
                label: NetworkTextMap.publicAccessBandwidth,
                value: `按带宽付费 ${publicAccessBandwidth}${Units.Mbps}`
            });
        }
        let orderData = {
            title: NetworkTextMap.title,
            class: `${klass}__order`,
            datasource,
            priceInfo: priceText
        };

        return orderData;
    }

    getConfirmData() {
        return {
            ...this.getPriceData(),
            ...this.getCheckData()
        };
    }
}
