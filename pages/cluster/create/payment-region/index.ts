/**
 * 付费及地域模块
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {html, ServiceFactory} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Form, Radio, Loading, Switch, Select, Tag} from '@baidu/sui';
import {BlockBox, DocLink, TipIcon} from '@components/index';
import {
    PaymentType,
    PREPAY_TIME_LIST,
    AUTO_RENEW_MONTH,
    AUTO_RENEW_YEAR,
    Payment,
    PaymentsArr,
    DefaultPaymentType
} from '@common/enums';
import BaseCmpt from '../base-cmpt';
import {DOC_LINK} from '@common/docs';
import {renderSwitch} from '@common/html';

import './index.less';
import {
    getPayLoop,
    getItemInfoInArr,
    formatRegion,
    formatPayment,
    isPrepaid,
    formatTimeLengthUnit
} from '@common/utils';

const klass = 'm-payment-region';

export const PaymentTextMap = {
    title: '付费及地域',
    payment: '付费方式：',
    region: '当前地域：',
    timeChoice: '购买时长：',
    autoSwitch: '自动续费：',
    autoLength: '选择续费周期：'
};

const prepaidTimeList = PREPAY_TIME_LIST.toArray();

export class PaymentRegion extends BaseCmpt {
    static template = html`
        <div class="${klass}">
            <block-box title="${PaymentTextMap.title}">
                <s-form s-ref="form" rules="{{rules}}" data="{{formData}}">
                    <s-form-item class="${klass}__region">
                        <template slot="label">
                            ${PaymentTextMap.region}
                            <tip-icon placement="right" type="question">
                                如需修改购买其他区域产品，请前往主导航进行切换
                            </tip-icon>
                        </template>
                        <s-radio-group
                            class="${klass}__region_radio"
                            value="{= formData.region =}"
                            datasource="{{regions}}"
                            radioType="button"
                            enhanced
                        />
                    </s-form-item>
                    <s-form-item label="${PaymentTextMap.payment}" prop="payment">
                        <div class="payment-card-list">
                            <div
                                s-for="item in payments"
                                on-click="onPaymentChange(item)"
                                class="payment-card-item {{formData.payment === item.value ? 'active' : ''}}"
                            >
                                <i class="payment-card-item__icon {{item.class}}"></i>
                                <div class="payment-card-item__info">
                                    <h4 class="payment-card-item__info-title">{{item.text}}</h4>
                                    <p class="payment-card-item__info-desc">{{item.desc}}</p>
                                </div>
                                <span s-if="item.tag" class="payment-card-item__tag">{{item.tag}}</span>
                            </div>
                        </div>
                    </s-form-item>
                    <s-form-item s-if="isPrepaid" label="${PaymentTextMap.timeChoice}">
                        <s-radio-group
                            value="{= formData.timeLength =}"
                            radioType="button"
                            on-change="onPriceConfigChange"
                            enhanced
                        >
                            <s-radio s-for="item, index in prepaidTimeList" value="{{item.value}}">
                                <s-tag class="sale" s-if="item.value > 9" skin="danger" enhanced> 折扣 </s-tag>
                                {{item.text}}
                            </s-radio>
                        </s-radio-group>
                    </s-form-item>
                    <s-form-item s-if="isPrepaid" label="${PaymentTextMap.autoSwitch}">
                        <s-switch checked="{= formData.autoRenew.renew =}" on-change="onChange" />
                        <doc-link href="${DOC_LINK.autoRenew}" target="blank" skin="primary">
                            什么是自动续费？
                        </doc-link>
                    </s-form-item>
                    <s-form-item s-if="isPrepaid && formData.autoRenew.renew" label="${PaymentTextMap.autoLength}">
                        <s-select
                            datasource="{{ autoUnits }}"
                            value="{= formData.autoRenew.renewTimeUnit =}"
                            on-change="onRenewTimeUnitChange"
                        />
                        <s-select
                            datasource="{{isRenewOfMonth ? autoRenewOfMonth : autoRenewOfYear}}"
                            value="{= formData.autoRenew.renewTimeLength =}"
                            class="ml16"
                        />
                        <span class="desc mt10 inline-desc">
                            系统将于到期7天前进行扣费，扣费时长为 {{ formData.autoRenew.renewTimeLength }}
                            {{isRenewOfMonth ? '月' : '年' }}
                        </span>
                    </s-form-item>
                </s-form>
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        's-append': AppLegend,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        'tip-icon': TipIcon,
        's-loading': Loading,
        's-tag': Tag,
        's-switch': Switch,
        's-select': Select,
        'doc-link': DocLink
    };

    initData() {
        return {
            PaymentType,
            payments: PaymentsArr,
            prepaidTimeList: prepaidTimeList,
            autoUnits: [
                {text: '按月', value: 'month'},
                {text: '按年', value: 'year'}
            ],
            autoRenewOfMonth: AUTO_RENEW_MONTH.toArray(),
            autoRenewOfYear: AUTO_RENEW_YEAR.toArray(),
            formData: {
                payment: DefaultPaymentType,
                timeLength: 1,
                autoRenew: {
                    renew: true,
                    renewTimeUnit: 'month',
                    renewTimeLength: 1
                }
            }
        };
    }

    static filters: SanFilterProps = {
        formatPayment,
        formatRegion,
        renderSwitch
    };

    static computed = {
        isPrepaid(): boolean {
            const payment = this.data.get('formData.payment');
            return isPrepaid(payment);
        },
        isRenewOfMonth(): boolean {
            const {renewTimeUnit} = this.data.get('formData.autoRenew');
            return renewTimeUnit === 'month';
        }
    };

    inited() {
        const region = this.$context.getCurrentRegion();
        this.data.set('regions', [{text: region.label, value: region.id}]);
        this.data.set('formData.region', region.id);
    }

    onPaymentChange(item: EnumItem) {
        this.data.set('formData.payment', item.value);
        this.onPriceConfigChange();
    }

    // 付费类型改变
    onPriceConfigChange() {
        this.nextTick(() => {
            const {payment, timeLength} = this.data.get('formData');
            this.fire('price-config-change', {
                name: 'paymentRegion',
                mergeData: {
                    payment,
                    timeLength
                }
            });
        });
    }

    // 校验
    verify() {
        return Promise.resolve();
    }

    onRenewTimeUnitChange() {
        this.data.set('formData.autoRenew.renewTimeLength', 1);
    }

    // 获取订单信息页面参数(注意，这个是给予父组件使用)，不能随便删除
    getOrderItemData() {
        const {regions, formData} = this.data.get();
        const {payment, region, timeLength, autoRenew} = formData;
        let datasource = [
            {
                label: PaymentTextMap.region,
                value: getItemInfoInArr(regions, region)
            },
            {
                label: PaymentTextMap.payment,
                value: getItemInfoInArr(PaymentsArr, payment)
            }
        ];

        if (payment === Payment.Prepaid) {
            datasource = datasource.concat([
                {
                    label: PaymentTextMap.timeChoice,
                    value: timeLength <= 9 ? timeLength + '个月' : timeLength / 12 + '年'
                },
                {
                    label: PaymentTextMap.autoSwitch,
                    value: renderSwitch(autoRenew.renew)
                }
            ]);

            autoRenew.renew &&
                datasource.push({
                    label: '自动续费周期：',
                    value: getPayLoop(autoRenew.renewTimeLength, autoRenew.renewTimeUnit)
                });
        }

        return {
            title: PaymentTextMap.title,
            datasource
        };
    }

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    getPriceData() {
        const formData = this.data.get('formData');
        const {payment} = formData;
        const isPrepaid = this.data.get('isPrepaid');
        if (isPrepaid) {
            return {
                payment,
                ...formatTimeLengthUnit(formData.timeLength)
            };
        }

        return {
            payment
        };
    }

    /** 获取检查资源参数，目前是跟获取价格的参数一致 */
    getCheckData() {
        return this.getPriceData();
    }

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    getConfirmData() {
        const formData = this.data.get('formData');
        const {payment} = formData;
        const isPrepaid = this.data.get('isPrepaid');
        let params: NormalObject = {
            payment
        };
        if (isPrepaid) {
            const {timeLength, autoRenew} = formData;
            params = {
                ...params,
                ...formatTimeLengthUnit(timeLength),
                renew: autoRenew.renew
            };

            if (autoRenew.renew) {
                const {renewTimeLength, renewTimeUnit} = autoRenew;
                params.renewTimeLength = renewTimeLength;
                params.renewTimeUnit = renewTimeUnit;
            }
        }

        return params;
    }
}
