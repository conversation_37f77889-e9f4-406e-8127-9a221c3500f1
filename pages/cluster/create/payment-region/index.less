@klass: m-payment-region;

.@{klass} {
    &__region {
        .s-form-item-control {
            display: flex;
            align-items: center;
        }

        &_radio {
            display: inline-block;
        }
    }

    .s-radio-text {
        min-width: 42px;
    }

    .sale {
        position: absolute;
        top: -17px;
        right: 0;
        .s-tag {
            margin: 0;
            background-image: linear-gradient(90deg, #f3413f 22%, #f86454 100%);
            border-radius: 1.6px;
            height: 16px;
        }
        .s-tag-content {
            padding: 0 5px;
            font-size: 12px;
            color: #ffffff;
            line-height: 16px;
            font-weight: 400;
            height: 16px;
        }
    }

    .payment-card {
        &-list {
            display: flex;
        }
        &-item {
            position: relative;
            display: inline-flex;
            align-items: center;
            width: 260px;
            padding: 13px 0 13px 20px;
            border: 1px solid rgba(232, 233, 235, 1);
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;

            &:hover {
                border: 1px solid rgba(36, 104, 242, 1);
            }

            &.active {
                background: #eef3fe;
                border: 1px solid rgba(36, 104, 242, 1);

                .payment-card-item__info-title {
                    color: var(--text-primary-color);
                }
            }

            &__icon {
                display: inline-block;
                width: 32px;
                height: 32px;
                margin-right: 20px;

                &.prepaid {
                    background-image: url(~@static/img/prepaid.png);
                }
                &.postpaid {
                    background-image: url(~@static/img/postpaid.png);
                }
            }

            &__info {
                &-title {
                    font-size: 14px;
                    line-height: 22px;
                    font-weight: 500;
                }

                &-desc {
                    font-size: 12px;
                    color: var(--text-sub-color);
                    line-height: 20px;
                    margin-top: 4px;
                }
            }

            &__tag {
                position: absolute;
                right: 0;
                top: 0;
                background-image: linear-gradient(90deg, #f3413f 22%, #f86454 100%);
                border-top-right-radius: 4px;
                line-height: 20px;
                padding: 0 4px;
                color: #fff;
                border-bottom-left-radius: 12px;
                clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0 0);
                text-align: center;
                text-indent: 4px;
            }
        }
    }
}
