@import '~@styles/common.less';

.m-node-config {
    margin-top: 40px;

    .form-item-arch {
        .s-form-item-control-wrapper {
            flex: 1;
        }
    }

    &__arch-sub-wrapper {
        margin-top: 16px;
        padding: 16px 24px;
        background: var(--background-color);
        border-radius: 6px;

        .sub-form-item-last {
            margin-bottom: 0;
        }
    }

    &__arch-sub {
        .s-form-item-label > label {
            padding-left: 0;
            width: auto;
            margin-right: 8px;
        }
    }

    .available-areas {
        padding-top: 5px;

        .s-row {
            .s-radio,
            .s-checkbox {
                position: relative;

                &.soldout::after {
                    .radio-button-soldout();
                }

                .s-radio-text {
                    width: 94px;
                }
            }
        }
    }

    .form-item-node-type {
        .s-form-item-control-wrapper {
            flex: 1;
        }
    }

    // todo: 表格售罄样式
    .s-table-row {
        &.soldout {
            .s-table-cell .s-table-cell-text {
                color: var(--grey-color);
            }

            .cell-name {
                position: relative;

                &::after {
                    .table-soldout();
                }
            }
        }
    }

    &__order {
        .detail-cell {
            &:nth-child(3n + 2) label {
                width: 86px;
            }

            &:nth-child(3n + 3) label {
                width: 74px;
            }
        }
    }
}
