/**
 * 节点配置
 * @file index.ts
 * <AUTHOR>
 */

import BaseCmpt from '../base-cmpt';
import {html} from '@baiducloud/runtime';
import {BlockBox, DocLink, TipIcon} from '@components/index';
import {Form, Radio, InputNumber, Switch, Loading, Checkbox, Table} from '@baidu/sui';
import {ArchList, ArchType, FlushList, ModeList} from '@common/enums';
import './index.less';
import {api} from '@common/client';
import {DOC_LINK} from '@common/docs';
import _ from 'lodash';
import {
    computeNumberOfBrokerNodes,
    computeNumberOfNodesPerBroker,
    formatArchFlush,
    formatEmpty,
    formatMode,
    formatSelectedOrderZones,
    getLogicalZonesByFormData,
    isInvalid,
    renderZoneLabel
} from '@common/utils';
import {BccFlavor, BccFlavorStatus, FlavorResult, ZoneResult} from './index.d';
import {VALIDATE_ITEMS} from '@common/rules';
import {NumberOfBrokerNodesHelpTip, SELECTION_SUI_SINGLE} from '@common/config';
import {renderSwitch} from '@common/html';

const klass = 'm-node-config';

export const NodeConfigTextMap = {
    title: '节点配置',
    arch: '部署架构：',
    dledgerNumberOfNodesPerBroker: '组内节点数：',
    masterSlaveNumberOfNodesPerBroker: '主从比例：',
    flushDiskType: '副本机制：',
    mode: '部署方式：',
    deploySetEnabled: '部署集：',
    logicalZone: '可用区：',
    nodeType: '节点类型：',
    numberOfBrokers: '节点组数量：',
    numberOfBrokerNodes: '总节点数量：'
};

const NumberOfBrokersLimit = {
    min: 1,
    max: 15
};
export class NodeConfig extends BaseCmpt {
    static template = html`
        <div class="${klass}">
            <block-box title="${NodeConfigTextMap.title}">
                <s-form s-ref="form" rules="{{rules}}" data="{= formData =}">
                    <s-form-item label="${NodeConfigTextMap.arch}" prop="arch" class="form-item-arch">
                        <s-radio-group
                            datasource="{{archList}}"
                            value="{= formData.arch =}"
                            radioType="button"
                            class="common-width-item"
                            on-change="onArchChange"
                        />
                        <!-- 白名单控制 -->
                        <template s-if="whiteListMap.ROCKETMQ_NodesPerBroker || whiteListMap.ROCKETMQ_FlushType">
                            <template s-if="formData.arch === ArchList.DLEDGER">
                                <div s-if="whiteListMap.ROCKETMQ_NodesPerBroker" class="${klass}__arch-sub-wrapper">
                                    <s-form-item
                                        s-if="whiteListMap.ROCKETMQ_NodesPerBroker"
                                        label="${NodeConfigTextMap.dledgerNumberOfNodesPerBroker}"
                                        prop="dledgerNumberOfNodesPerBroker"
                                        class="${klass}__arch-sub sub-form-item-last"
                                    >
                                        <s-input-number
                                            step="{{2}}"
                                            min="{{3}}"
                                            max="{{7}}"
                                            value="{= formData.dledgerNumberOfNodesPerBroker =}"
                                            on-change="onNumberOfNodesPerBrokerChange($event, ArchType.DLEDGER)"
                                        />
                                    </s-form-item>
                                </div>
                            </template>
                            <template s-else>
                                <div class="${klass}__arch-sub-wrapper">
                                    <s-form-item
                                        s-if="whiteListMap.ROCKETMQ_NodesPerBroker"
                                        label="${NodeConfigTextMap.masterSlaveNumberOfNodesPerBroker}"
                                        prop="masterSlaveNumberOfNodesPerBroker"
                                        class="${klass}__arch-sub"
                                    >
                                        1：
                                        <s-input-number
                                            min="{{1}} "
                                            max="{{7}}"
                                            value="{= formData.masterSlaveNumberOfNodesPerBroker =}"
                                            on-change="onNumberOfNodesPerBrokerChange($event, ArchType.MASTER_SLAVE)"
                                        />
                                    </s-form-item>
                                    <s-form-item
                                        s-if="whiteListMap.ROCKETMQ_FlushType"
                                        label="${NodeConfigTextMap.flushDiskType}"
                                        prop="flushDiskType"
                                        class="${klass}__arch-sub sub-form-item-last"
                                    >
                                        <s-radio-group
                                            datasource="{{flushList}}"
                                            value="{= formData.flushDiskType =}"
                                            radioType="button"
                                            class="common-width-item"
                                        />
                                    </s-form-item>
                                </div>
                            </template>
                        </template>
                    </s-form-item>

                    <s-form-item label="${NodeConfigTextMap.mode}" prop="mode">
                        <s-radio-group
                            datasource="{{modeList}}"
                            value="{= formData.mode =}"
                            radioType="button"
                            on-change="onModeChange"
                            class="common-width-item"
                        />
                        <p slot="help" s-if="formData.mode === ModeList.HP">单可用区部署不支持跨AZ容灾能力</p>
                        <p slot="help" s-else>多可用区部署，请选择{{numberOfNodesPerBroker}}个可用区</p>
                    </s-form-item>
                    <s-form-item prop="deploySetEnabled">
                        <template slot="label">
                            ${NodeConfigTextMap.deploySetEnabled}
                            <tip-icon placement="right">
                                开启
                                <doc-link href="${DOC_LINK.brokerDeploySet}" skin="primary">部署集</doc-link>
                                后，节点会分布在不同宿主机上以提升可用性，但会提高集群创建、变更的失败概率。
                            </tip-icon>
                        </template>
                        <s-switch checked="{= formData.deploySetEnabled =}" />
                    </s-form-item>
                    <s-form-item label="${NodeConfigTextMap.logicalZone}" prop="logicalZone" class="available-areas">
                        <template s-if="{{!loading.zoneList}}">
                            <s-radio-group
                                s-if="{{formData.mode === ModeList.HP}}"
                                value="{{formData.logicalZone}}"
                                on-change="onSingleZoneChange"
                                radioType="button"
                                enhanced
                            >
                                <s-radio
                                    s-for="item in singleZoneList"
                                    disabled="{{item.soldout}}"
                                    value="{=item.value=}"
                                    class="zone-item {{item.soldout ? 'soldout' : ''}}"
                                >
                                    {{item.text}}
                                </s-radio>
                            </s-radio-group>
                            <s-checkbox-group
                                s-else
                                class="available-areas__checkbox"
                                value="{= formData.logicalZone =}"
                                on-change="onMultiZoneChange"
                                checkboxType="button"
                                enhanced
                            >
                                <s-checkbox
                                    s-for="item in multiZoneList"
                                    disabled="{{item.disabled || item.soldout}}"
                                    value="{{item.value}}"
                                    class="zone-item {{item.soldout ? 'soldout' : ''}}"
                                >
                                    {{item.text}}
                                </s-checkbox>
                            </s-checkbox-group>
                        </template>
                        <s-loading s-else loading />
                    </s-form-item>
                    <s-form-item label="${NodeConfigTextMap.nodeType}" prop="nodeType" class="form-item-node-type">
                        <s-table
                            columns="{{table.columns}}"
                            datasource="{{table.datasource}}"
                            selection="{= table.selection =}"
                            loading="{{loading.zoneList || loading.flavors}}"
                            rowClassName="{{table.rowClassName}}"
                            on-selected-change="onNodeTypeChange"
                            bordered
                        >
                            <span slot="c-name" class="cell-name">{{row.name}}</span>
                            <div slot="c-topics">{{row.topics | formatEmpty}}</div>
                            <div slot="c-groups">{{row.groups | formatEmpty}}</div>
                        </s-table>
                    </s-form-item>
                    <s-form-item label="${NodeConfigTextMap.numberOfBrokers}" prop="numberOfBrokers">
                        <s-input-number
                            value="{= formData.numberOfBrokers =}"
                            min="{{NumberOfBrokersLimit.min}}"
                            max="{{NumberOfBrokersLimit.max}}"
                            class="common-width-item"
                        />
                        <p slot="help" s-if="formData.arch === ArchList.DLEDGER">
                            DLedger 多副本架构，组内节点数为{{numberOfNodesPerBroker}}个
                        </p>
                        <p slot="help" s-else>Master-Slave主从异步架构，组内节点数为{{numberOfNodesPerBroker}}个。</p>
                    </s-form-item>
                    <s-form-item
                        label="${NodeConfigTextMap.numberOfBrokerNodes}"
                        prop="numberOfBrokerNodes"
                        help="${NumberOfBrokerNodesHelpTip}"
                    >
                        <s-input-number value="{{computedNumberOfBrokerNodes}}" disabled class="common-width-item" />
                    </s-form-item>
                </s-form>
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-input-number': InputNumber,
        's-switch': Switch,
        'tip-icon': TipIcon,
        'doc-link': DocLink,
        's-loading': Loading,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-table': Table
    };

    static filters: SanFilterProps = {
        formatEmpty
    };

    static computed: SanComputedProps = {
        computedNumberOfBrokerNodes() {
            const formData = this.data.get('formData');
            return computeNumberOfBrokerNodes(formData);
        },
        numberOfNodesPerBroker() {
            const {arch, dledgerNumberOfNodesPerBroker, masterSlaveNumberOfNodesPerBroker} = this.data.get('formData');
            return computeNumberOfNodesPerBroker({
                arch,
                dledgerNumberOfNodesPerBroker,
                masterSlaveNumberOfNodesPerBroker
            });
        }
    };

    initData() {
        return {
            rules: {
                logicalZone: [
                    VALIDATE_ITEMS.requiredSelect,
                    {
                        validator: (rule: any, value: string | string[], callback: Function) => {
                            const {mode} = this.data.get('formData');
                            const brokerNodesLen = this.data.get('numberOfNodesPerBroker');
                            if (mode === ModeList.HP && !value) {
                                return callback('请选择可用区');
                            } else if (mode === ModeList.HA && value.length < brokerNodesLen) {
                                return callback(`请选择${brokerNodesLen}个可用区`);
                            }

                            callback();
                        }
                    }
                ]
            },
            formData: {
                arch: ArchList.DLEDGER,
                dledgerNumberOfNodesPerBroker: 3,
                masterSlaveNumberOfNodesPerBroker: 1,
                flushDiskType: FlushList.ASYNC_FLUSH,
                mode: ModeList.HP,
                deploySetEnabled: true,
                logicalZone: [],
                numberOfBrokers: NumberOfBrokersLimit.min
            },
            ArchType,
            ArchList,
            archList: ArchList.toArray(),
            FlushList,
            flushList: FlushList.toArray(),
            ModeList,
            modeList: ModeList.toArray(),
            singleZoneList: [],
            multiZoneList: [],
            loading: {
                zoneList: true
            },
            table: {
                columns: [
                    {name: 'name', label: '规格名称', width: 200},
                    {
                        name: 'topics',
                        label: '单节点推荐主题数',
                        width: 150
                    },
                    {
                        name: 'groups',
                        label: '单节点推荐消费组数',
                        width: 150
                    }
                ],
                datasource: [],
                selection: {
                    ...SELECTION_SUI_SINGLE
                },
                rowClassName(item: BccFlavor) {
                    if (item.status === BccFlavorStatus.Sellout) {
                        return 'soldout';
                    }
                }
            },
            NumberOfBrokersLimit
        };
    }

    attached() {
        this.getZoneList();
        this.watch('computedNumberOfBrokerNodes', numberOfBrokerNodes => {
            this.fire('numberOfBrokerNodes-change', {numberOfBrokerNodes});
            this.onPriceConfigChange();
        });
        this.nextTick(() => {
            this.fire('numberOfBrokerNodes-change', {
                numberOfBrokerNodes: this.data.get('computedNumberOfBrokerNodes')
            });
        });
    }

    onArchChange(e: {value: string}) {
        this.nextTick(() => {
            this.handleZoneFromNodesPerBroker();
        });
    }

    onNumberOfNodesPerBrokerChange(e: {value: number}, archType: ArchType) {
        if (e.value % 2 === 0 && archType === ArchType.DLEDGER) {
            this.nextTick(() => {
                this.data.set('formData.dledgerNumberOfNodesPerBroker', e.value - 1);
            });
        }
        this.nextTick(() => {
            this.handleZoneFromNodesPerBroker();
        });
    }

    /**
     * 组内节点数发生变化——》可用区变化
     */
    handleZoneFromNodesPerBroker() {
        const {logicalZone, mode} = this.data.get('formData');
        const brokerNodesLen = this.data.get('numberOfNodesPerBroker');
        if (mode === ModeList.HA) {
            const newZone = logicalZone.slice(0, brokerNodesLen);

            // 确保onMultiZoneChange当中numberOfNodesPerBroker是最新的
            this.onMultiZoneChange({value: newZone});
        }
    }

    onModeChange(e: {value: string}) {
        this.data.set('formData.mode', e.value);
        const {logicalZone} = this.data.get('formData');
        if (e.value === ModeList.HP) {
            const selectZoneValue = logicalZone[0];
            this.onSingleZoneChange({value: selectZoneValue});
        } else {
            this.onMultiZoneChange({value: [logicalZone]});
        }
    }

    async getZoneList() {
        this.data.set('loading.zoneList', true);
        const res = (await api.zoneList({})) as ZoneResult;
        const {all, hp, ha, sellout} = res || {};
        this.data.set('ha', ha);
        // 当可用区数量小于0时，不可选多可用区
        this.data.set('modeList[1].disabled', ha.length <= 0);
        const area = _.map(all, i => ({
            value: i,
            text: renderZoneLabel(i),
            soldout: _.includes(sellout, i)
        }));
        this.data.set('singleZoneList', area);
        this.data.set('multiZoneList', area);
        this.data.set('formData.logicalZone', hp[0] || '');
        this.data.set('loading.zoneList', false);
        this.fire('zone-change', {
            logicalZones: [hp[0]],
            zoneList: area,
            isInit: true
        });
        this.nextTick(() => {
            this.getFlavors();
        });
    }

    // radio 改变
    async onSingleZoneChange(e: {value: string}) {
        this.resetData();
        this.data.set('formData.logicalZone', e.value);
        this.fire('zone-change', {logicalZones: [e.value]});
        this.nextTick(async () => {
            this.verifyZone();
            await this.getFlavors();
        });
    }

    /**
     * 数据类型
     * @param target
     */
    // checkbox 改变，限制最多只能选取三个
    async onMultiZoneChange(e: {value: string[]}) {
        this.fire('zone-change', {logicalZones: e.value});
        e.value = this.handleZones(e.value);
        this.data.set('formData.logicalZone', e.value);
        this.nextTick(() => {
            this.verifyZone();
        });
        const targetValueLen = e.value.length;

        const firstMap = new Map(e.value.map((value: string) => [value, true]));
        // ha: Array<string[]>
        const {ha, multiZoneList} = this.data.get('');
        const remainMap = new Map();
        // 选取的可用区可数小于最大数之前
        // 根据ha的组合结果，判断除了已选可用区，剩下有哪些可用区可以选择，保存到remainMap
        const brokerNodesLen = this.data.get('numberOfNodesPerBroker');
        if (targetValueLen < brokerNodesLen) {
            _.each(ha, haItemArr => {
                // 该组合是否包含了每个已选的可用区
                const hasSelectedZones = _.every(e.value, targetItem => haItemArr.includes(targetItem));
                if (hasSelectedZones) {
                    // 遍历该组合，找到组合当中另外未选中的可用区，保存到remainMap
                    _.each(haItemArr, i => {
                        !firstMap.get(i) && remainMap.set(i, true);
                    });
                }
            });
        }

        // 判断禁用逻辑：
        // 1.当长度为0时，所有都不禁用
        // 2.当长度为最大限制时，如果在firstMap存在的才不禁用，其余禁用
        // 3.当长度少于最大限制时，如果在firstMap或remainMap存在，就不禁用，其余禁用
        _.each(multiZoneList, (item: {value: string; disabled?: boolean}, index: number) => {
            let disabled = false;
            const ha = this.data.get('ha');
            const value = item.value;

            const hasSelectedZones = targetValueLen !== 0;

            // 已选中至少1个可用区
            if (hasSelectedZones) {
                if (targetValueLen >= brokerNodesLen) {
                    // 已选可用区达到限制，如果在firstMap存在的才不禁用，其余禁用
                    disabled = !firstMap.get(value);
                } else {
                    // 未达到限制，如果在firstMap或remainMap存在，就不禁用，其余禁用
                    disabled = !(firstMap.get(value) || remainMap.get(value));
                }
            }
            this.data.set(`multiZoneList[${index}].disabled`, disabled);
        });
        if (targetValueLen) {
            await this.getFlavors();
        } else {
            // 重置下节点、磁盘类型
            this.resetData();
        }
    }

    // 处理下可用区数据使得多选时，数据可以按照显示的多选顺序来显示
    handleZones(zones: string[]) {
        const multiZoneList = this.data.get('multiZoneList');
        const newMap = new Map(zones.map((value: string) => [value, true]));
        return _.map(
            _.filter(multiZoneList, item => newMap.get(item.value)),
            i => i.value
        );
    }

    // 手动校验可用区
    async verifyZone() {
        const form = this.ref('form') as any;
        await form.validateFields(['logicalZone']);
    }

    async getFlavors() {
        const {logicalZone, mode} = this.data.get('formData');
        try {
            this.data.set('loading.flavors', true);
            const res = (await api.getFlavors({
                zoneNames: getLogicalZonesByFormData(mode, logicalZone)
            })) as FlavorResult;

            const {instanceFlavor} = res || {};
            const {bccFlavors, cdsFlavors} = instanceFlavor || {};
            let selectedIndex: number | undefined;
            let disabledIndex: number[] = [];
            const flavors = bccFlavors?.map((item, index) => {
                const soldout = item.status === BccFlavorStatus.Sellout;
                if (soldout) {
                    disabledIndex.push(index);
                }
                if (!soldout && isInvalid(selectedIndex)) {
                    selectedIndex = index;
                }
                return {
                    ...item,
                    soldout
                };
            });
            this.data.set('table.datasource', flavors);
            selectedIndex !== undefined && this.data.set('table.selection.selectedIndex', [selectedIndex]);
            this.data.set('table.selection.disabledIndex', disabledIndex);
            selectedIndex !== undefined && this.data.set('formData.nodeType', flavors[selectedIndex].type);
            this.fire('flavor-change', instanceFlavor);
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('loading.flavors', false);
        }
    }

    onNodeTypeChange(e: {value: {selectedItems: BccFlavor[]; selectedIndex: number[]}}) {
        const nodeType = e.value.selectedItems[0]?.type;
        this.data.set('formData.nodeType', nodeType);
        this.onPriceConfigChange();
    }

    // 数据重置
    resetData() {
        this.data.set('formData.nodeType', '');
        this.data.set('selection', {
            mode: 'single',
            selectedItems: [],
            selectedIndex: []
        });
        // 重置磁盘类型
    }

    // 校验
    async verify() {
        const form = this.ref('form') as any;
        await form.validateFields();
    }

    onPriceConfigChange() {
        this.nextTick(() => {
            this.fire('price-config-change', {});
        });
    }

    getPriceData() {
        const formData = this.data.get('formData');
        const {nodeType} = formData;

        return {
            nodeType,
            numberOfBrokerNodes: computeNumberOfBrokerNodes(formData)
        };
    }

    getCheckData() {
        const formData = this.data.get('formData');
        const {
            arch,
            mode,
            nodeType,
            logicalZone,
            numberOfBrokers,
            dledgerNumberOfNodesPerBroker,
            masterSlaveNumberOfNodesPerBroker
        } = formData;

        const logicalZones = getLogicalZonesByFormData(mode, logicalZone);
        return {
            arch,
            mode,
            nodeType,
            zoneNames: logicalZones,
            numberOfNodesPerBroker: computeNumberOfNodesPerBroker({
                arch,
                dledgerNumberOfNodesPerBroker,
                masterSlaveNumberOfNodesPerBroker
            }),
            numberOfBrokers,
            numberOfBrokerNodes: computeNumberOfBrokerNodes(formData)
        };
    }

    getOrderItemData() {
        const {formData, table, multiZoneList} = this.data.get('');
        const {
            arch,
            mode,
            logicalZone,
            flushDiskType,
            dledgerNumberOfNodesPerBroker,
            masterSlaveNumberOfNodesPerBroker,
            deploySetEnabled,
            numberOfBrokers
        } = formData;

        const {datasource, selection} = table;

        const selectedOrderZones = formatSelectedOrderZones({logicalZones: logicalZone, zoneList: multiZoneList});

        let orderData = {
            title: NodeConfigTextMap.title,
            class: `${klass}__order`,
            datasource: [
                {
                    label: NodeConfigTextMap.arch,
                    value: formatArchFlush({
                        arch,
                        flushDiskType,
                        dledgerNumberOfNodesPerBroker,
                        masterSlaveNumberOfNodesPerBroker
                    })
                },
                {label: NodeConfigTextMap.mode, value: formatMode({mode, selectedOrderZones})},
                {label: NodeConfigTextMap.deploySetEnabled, value: renderSwitch(deploySetEnabled)},
                {label: NodeConfigTextMap.nodeType, value: datasource[selection.selectedIndex[0]].name},
                {label: NodeConfigTextMap.numberOfBrokers, value: numberOfBrokers},
                {label: NodeConfigTextMap.numberOfBrokerNodes, value: computeNumberOfBrokerNodes(formData)}
            ]
        };

        return orderData;
    }

    getConfirmData() {
        const {flushDiskType, deploySetEnabled} = this.data.get('formData');
        let params = {
            ...this.getCheckData(),
            flushDiskType,
            deploySetEnabled
        };
        return params;
    }
}
