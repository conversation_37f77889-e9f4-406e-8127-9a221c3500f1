/**
 * 返回结果，返回一个对象
 */
export interface ZoneResult {
    /**
     * 该 region 所有的可用区列表
     */
    all: string[];
    /**
     * 该 region 支持高可用模式的可用区集合列表
     */
    ha: Array<string[]>;
    /**
     * 该 region 支持高性能模式的可用区列表
     */
    hp: string[];
    /**
     * 该 region 售罄的可用区列表
     */
    sellout: string[];
    [property: string]: any;
}

/**
 * 用于可用区组件渲染
 */
export interface ZoneListItem {
    text: string;
    value: string;
    soldout: boolean;
}
/**
 * 返回结果，返回一个对象
 */
export interface FlavorResult {
    /**
     * 套餐对象
     */
    instanceFlavor: InstanceFlavor;
    [property: string]: any;
}

/**
 * 套餐对象
 */
export interface InstanceFlavor {
    /**
     * BCC 套餐列表
     */
    bccFlavors: BccFlavor[];
    /**
     * CDS 套餐列表
     */
    cdsFlavors: CdsFlavor[];
    [property: string]: any;
}

/**
 * BCC 机型
 */
export interface BccFlavor {
    /**
     * CPU 架构，可选：x86 \ arm \ isomerism 等
     */
    arch: string;
    /**
     * 内网带宽，单位：Gbps
     */
    bandwidth: number;
    /**
     * 机型类别，可选： common(通用型)、calculate(计算型)
     */
    category: string;
    /**
     * CPU核数
     */
    cpu: number;
    /**
     * CPU 类型，可选：INTEL / AMD
     */
    cpuType: CpuType;
    /**
     * 推荐最大消费组数
     */
    groups: number;
    /**
     * 规格族，如 N2、N5、N6、A1、A2
     */
    instanceType: string;
    /**
     * 内存大小，单位：GB
     */
    memory: number;
    /**
     * 机型名称
     */
    name: string;
    /**
     * 机型状态，可选：available /
     */
    status: BccFlavorStatus;
    /**
     * 推荐最大主题数
     */
    topics: number;
    /**
     * 机型 ID
     */
    type: string;
    [property: string]: any;
}

/**
 * CPU 类型，可选：INTEL / AMD
 */
export enum CpuType {
    AMD = 'AMD',
    Intel = 'INTEL',
    Unknown = 'UNKNOWN'
}

/**
 * 机型状态，可选：available /
 */
export enum BccFlavorStatus {
    Available = 'AVAILABLE',
    Sellout = 'SELLOUT'
}

/**
 * CDS 套餐
 */
export interface CdsFlavor {
    /**
     * 类型状态
     */
    status: CdsFlavorStatus;
    /**
     * CDS 类型
     */
    storageType: string;
    [property: string]: any;
}

/**
 * 类型状态
 */
export enum CdsFlavorStatus {
    Avaliable = 'AVALIABLE'
}
