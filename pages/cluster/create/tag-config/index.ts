/**
 * 集群标签
 *
 * @file index.js
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {Form} from '@baidu/sui';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {getSdk} from '@common/utils';
import {BlockBox} from '@components/index';
import './index.less';

const klass = 'm-tag-config';
export class TagConfig extends Component {
    static template = html`
        <div class="${klass}">
            <block-box title="集群标签">
                <s-form label-align="left">
                    <s-form-item label="绑定标签：">
                        <tag-edit-panel s-ref="tagPanel" instances="{{defaultInstances}}" sdk="{{tagSDK}}" />
                    </s-form-item>
                </s-form>
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        's-form': Form,
        's-form-item': Form.Item,
        'tag-edit-panel': TagEditPanel,
    };

    initData() {
        return {
            tagSDK: getSdk('TAG'),
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: 'RocketMQ-Cluster',
                            tagValue: '',
                        },
                    ],
                },
            ],
        };
    }

    // 获取标签,提供外部获取标签实例的能力
    getTagPanel() {
        return this.ref('tagPanel');
    }

    // 校验
    verify() {
        return this.ref('tagPanel').validate(false);
    }

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    getPriceData() {
        return {};
    }

    // 获取订单信息页面参数(注意，这个是给予父组件使用)，不能随便删除
    getOrderItemData(): NormalObject {
        return {};
    }

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    async getConfirmData() {
        const tags = await (this.ref('tagPanel') as any).getTags();
        return {
            tags,
        };
    }
}
