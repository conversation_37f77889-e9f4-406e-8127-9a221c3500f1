/**
 * 返回结果，返回一个对象
 */
export interface VersionsResult {
    /**
     * 集群版本列表
     */
    versions: Version[];
    [property: string]: any;
}

export interface Version {
    /**
     * 版本状态，版本状态，可选：TRIAL / ACTIVE / RECOMMENDED
     */
    status: VersionStatus;
    /**
     * 集群版本
     */
    version: string;
    [property: string]: any;
}

/**
 * 版本状态，版本状态，可选：TRIAL / ACTIVE / RECOMMENDED
 */
export enum VersionStatus {
    Active = 'ACTIVE',
    Recommended = 'RECOMMENDED',
    Trial = 'TRIAL',
}
