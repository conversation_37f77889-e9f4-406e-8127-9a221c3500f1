/**
 * 集群配置模块
 *
 * @file cluster-config.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Select, Input, Form, Radio, Loading} from '@baidu/sui';
import {VALIDATE_ITEMS} from '@common/rules';
import {getItemInfoInArr} from '@common/utils';
import {ClusterDefaultConf, ClusterRefType, VersionStatus} from '@common/enums';
import BaseCmpt from '../base-cmpt';
import {api} from '@common/client';
import {BlockBox} from '@components/index';
import './index.less';
import {VersionsResult} from './index.d';

const klass = 'm-cluster-config';

const Confs = ClusterDefaultConf.toArray();

export const ClusterConfigTextMap = {
    title: '集群配置',
    name: '集群名称：',
    version: '集群版本：',
    config: '集群配置：',
};

export interface IVersionItem {
    /**
     * 版本状态，版本状态，可选：TRIAL / ACTIVE / RECOMMENDED
     */
    status: 'TRIAL' | 'ACTIVE' | 'TRIAL';
    /**
     * 集群版本
     */
    version: string;
    [property: string]: any;
}

const nameRegTip = '必须以字母开头，仅支持数字、字母、特殊符号(_/-.)，不超过65个字符';

export class ClusterConfig extends BaseCmpt {
    static template = html`
        <div class="${klass}">
            <block-box title="${ClusterConfigTextMap.title}">
                <s-form s-ref="form" rules="{{rules}}" data="{= formData =}">
                    <s-form-item label="${ClusterConfigTextMap.name}" prop="name" help="${nameRegTip}">
                        <s-input
                            value="{= formData.name =}"
                            width="248"
                            placeholder="请输入集群名称"
                            on-input="onNameInputChange"
                            on-blur="onNameBlur"
                        />
                    </s-form-item>
                    <s-form-item label="${ClusterConfigTextMap.version}" prop="version">
                        <s-select
                            s-if="{{!versionLoading}}"
                            datasource="{{versions}}"
                            value="{= formData.version =}"
                            class="common-width-item"
                        />
                        <s-loading s-else loading />
                    </s-form-item>
                    <s-form-item label="${ClusterConfigTextMap.config}">
                        <s-radio-group
                            radioType="button"
                            datasource="{{configs}}"
                            value="{{formData.config}}"
                            class="common-width-item"
                        />
                    </s-form-item>
                </s-form>
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        's-append': AppLegend,
        's-input': Input,
        's-select': Select,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-loading': Loading,
        's-option': Select.Option,
    };

    initData() {
        return {
            versions: [],
            configs: Confs,
            formData: {
                version: '',
                name: '',
                config: Confs[0].value,
            },
            rules: {
                name: [
                    VALIDATE_ITEMS.requiredInput,
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (value.length > 65) {
                                return callback('不能超过65个字符');
                            }
                            if (!/^[a-zA-Z][a-zA-Z0-9_.\/-]{0,64}$/.test(value)) {
                                return callback(`输入字符格式有误。${nameRegTip}`);
                            }

                            if (this.data.get('nameErr')) {
                                return callback('与已有集群名称重复，请修改名称');
                            }
                            callback();
                        },
                    },
                ],
                version: [VALIDATE_ITEMS.requiredSelect],
            },
            versionLoading: true,
            configVersionLoading: true,
            configLoading: true,
            // 由外层组件传入
            priceInfo: {},
        };
    }

    static filters: SanFilterProps = {};

    inited() {}

    attached() {
        this.getVersion();
    }

    // 获取版本
    async getVersion() {
        try {
            this.data.set('versionLoading', false);
            const res = (await api.listAvailableVersion({})) as VersionsResult;
            this.data.set(
                'versions',
                _.map(res?.versions, (i: IVersionItem) => ({
                    value: i.version,
                    text: i.version + VersionStatus.getTextFromValue(i.status),
                })),
            );
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('versionLoading', false);
        }
    }

    // 集群名称改变
    onNameInputChange(e: {value: string}) {
        this.data.set('nameErr', false);
    }

    async onNameBlur() {
        await this.checkName();
    }

    // 检测名称是否重复
    async checkName(): Promise<void> {
        return new Promise(async (resolve, reject) => {
            const res = await api.checkClusterName({name: this.data.get('formData.name')});
            this.data.set('nameErr', res);
            this.nextTick(async () => {
                try {
                    await this.ref('form').validateFields(['name']);
                    resolve();
                } catch (err) {
                    reject(err);
                }
            });
        });
    }

    // 校验
    async verify() {
        const node = this.ref('form') as any;
        try {
            await Promise.all([node.validateFields(), this.checkName()]);
        } catch (err) {
            throw ClusterRefType.CLUSTER;
        }
    }

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    getPriceData() {
        return {};
    }

    // 获取订单信息页面参数(注意，这个是给予父组件使用)，不能随便删除
    getOrderItemData() {
        const {formData, versions, configs, priceInfo} = this.data.get('');
        const {name, version, config} = formData;

        const res = {
            title: ClusterConfigTextMap.title,
            priceInfo: {
                title: '集群费用：',
                price: priceInfo.cluster,
            },
            datasource: [
                {
                    label: ClusterConfigTextMap.name,
                    value: name,
                },
                {
                    label: ClusterConfigTextMap.version,
                    value: getItemInfoInArr(versions, version),
                },
                {
                    label: ClusterConfigTextMap.config,
                    value: getItemInfoInArr(configs, config),
                },
            ],
        };

        return res;
    }

    getCheckData() {
        return {};
    }

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    getConfirmData() {
        const {name, version} = this.data.get('formData');
        return {
            name: name,
            version,
        };
    }
}
