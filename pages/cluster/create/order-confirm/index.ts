import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {OrderConfirmItem} from '../../components/order-item';
import './index.less';

const klass = 'm-order-confirm';
export class OrderConfirm extends Component {
    static template = html`
        <div class="${klass}">
            <order-confirm-item s-for="item in orderConfirmList" item="{{item}}" />
        </div>
    `;

    static components = {
        'order-confirm-item': OrderConfirmItem,
    };

    initData() {
        return {
            orderConfirmList: [],
        };
    }
}
