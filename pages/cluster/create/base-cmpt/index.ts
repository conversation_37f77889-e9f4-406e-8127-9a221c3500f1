/**
 * 基础抽象类组件
 *
 * @file base-cmpt.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {OrderItem} from '../../components/order-item/index.d';

export default abstract class BaseCmpt extends Component {
    // 校验
    abstract verify(): Promise<any>;

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    abstract getPriceData(): NormalObject;

    // 获取下单前校验参数
    abstract getCheckData(): NormalObject;

    // 获取订单信息页面参数(注意，这个是给予父组件使用)，不能随便删除
    abstract getOrderItemData(): OrderItem;

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    abstract getConfirmData(): NormalObject;
}
