@input-width: 248px;
@input-half-width: 124px;
@create-width: 1180px;

.page-cluster-create {
    flex-direction: column !important;

    .s-create-page-footer {
        width: 100%;
        height: 80px;
    }

    &__step {
        width: 300px;
        margin: 24px auto;
    }

    .s-create-page-content {
        margin: 0 auto;
        padding: 0 24px 96px;
    }

    &__content {
        width: @create-width;
        padding: 24px;
        border-radius: 4px;
        background: #fff;

        .s-form-item-label > label {
            display: inline-flex;
            align-items: center;
            width: 92px;
            text-align: left;
        }

        // 创建集群统一组件宽度
        .common-width-item {
            &.s-select {
                width: @input-width !important;
            }

            &.s-radio-button-group {
                .s-radio-text {
                    width: @input-half-width;
                    box-sizing: border-box;
                }
            }

            &.s-inputnumber {
                width: @input-half-width !important;
            }
        }
    }

    &__footer {
        display: flex;

        .s-button {
            width: 106px;
            box-sizing: border-box;
        }
    }

    .s-create-page-footer {
        margin: 0 auto;
        padding: 0 24px;

        .page-footer-wrapper {
            width: @create-width;
        }
    }
}
