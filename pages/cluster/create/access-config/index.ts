/**
 * 访问配置
 * @file index.ts
 * <AUTHOR>
 */

import {BlockBox} from '@components/block-box';
import BaseCmpt from '../base-cmpt';
import {html} from '@baiducloud/runtime';
import './index.less';
import {Form, Radio} from '@baidu/sui';
import {AuthenticationModeList, EncryptionInTransitList} from '@common/enums';
import {VALIDATE_ITEMS} from '@common/rules';
import {ProtocolTable} from '@pages/cluster/components';
import {ProtocolColumns} from '@common/config';
import {getAuthenticationModeByFormData, renderAuthentictionMode} from '@common/utils';

const klass = 'm-access-config';
export const AccessTextMap = {
    title: '访问配置',
    authenticationMode: '认证方式：',
    encryptionInTransit: '传输加密：',
};

export class AccessConfig extends BaseCmpt {
    static template = html`
        <div class="${klass}">
            <block-box title="${AccessTextMap.title}">
                <s-form s-ref="form" rules="{{rules}}" data="{= formData =}">
                    <s-form-item
                        label="${AccessTextMap.authenticationMode}"
                        prop="authenticationMode"
                        class="form-item-auth"
                    >
                        <s-radio-group value="{= formData.authenticationMode =}" class="auth-list">
                            <div s-for="item in authenticList" class="auth-item">
                                <s-radio value="{{item.value}}" label="{{item.text}}" />
                                <p class="desc">{{item.help}}</p>
                            </div>
                        </s-radio-group>
                    </s-form-item>
                    <s-form-item
                        label="${AccessTextMap.encryptionInTransit}"
                        prop="encryptionInTransit"
                        help="{{formData.encryptionInTransit | formatHelp}}"
                    >
                        <s-radio-group
                            datasource="{{computedEncryptionList}}"
                            value="{= formData.encryptionInTransit =}"
                            radioType="button"
                        />
                    </s-form-item>
                    <s-form-item label=" " class="form-item-protocol">
                        <protocol-table columns="{{table.columns}}" datasource="{{computedDatasource}}" />
                    </s-form-item>
                </s-form>
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        'protocol-table': ProtocolTable,
    };

    static filters: SanFilterProps = {
        formatHelp(value) {
            return EncryptionInTransitList.fromValue(value)?.help || '';
        },
    };

    static computed: SanComputedProps = {
        computedDatasource() {
            const {authenticationMode, encryptionInTransit} = this.data.get('formData');
            const encryptEnable = [EncryptionInTransitList.SSL, EncryptionInTransitList.PERMISSIVE].includes(
                encryptionInTransit,
            );

            return [
                {
                    authenticationMode: renderAuthentictionMode(authenticationMode),
                    aclEnable: authenticationMode !== AuthenticationModeList.NONE,
                    encryptEnable,
                    port: 9876,
                },
            ];
        },

        computedEncryptionList() {
            const encryptionList = this.data.get('encryptionList');
            const publicAccessEnabled = this.data.get('publicAccessEnabled');

            return encryptionList.map((item: EnumItem) => {
                return {
                    ...item,
                    // 开启公网访问后，PLAINTEXT禁用
                    disabled: item.value === EncryptionInTransitList.PLAINTEXT ? publicAccessEnabled : false,
                };
            });
        },
    };

    initData() {
        return {
            rules: {
                authenticationMode: [VALIDATE_ITEMS.requiredSelect],
                encryptionInTransit: [VALIDATE_ITEMS.requiredSelect],
            },
            formData: {
                authenticationMode: AuthenticationModeList.NONE,
                encryptionInTransit: EncryptionInTransitList.SSL,
            },
            AuthenticationModeList,
            authenticList: AuthenticationModeList.toArray(),
            EncryptionInTransitList,
            encryptionList: EncryptionInTransitList.toArray(),

            table: {
                columns: ProtocolColumns.concat({name: 'port', label: '访问端口', width: 100}),
            },
            // 当公网方式修改时，该值会通过handlePublicAccessEnabledChange修改
            publicAccessEnabled: false,
        };
    }

    handlePublicAccessEnabledChange(e: {publicAccessEnabled: boolean}) {
        this.data.set('publicAccessEnabled', e.publicAccessEnabled);
        const {encryptionInTransit} = this.data.get('formData');
        if (e.publicAccessEnabled && encryptionInTransit === EncryptionInTransitList.PLAINTEXT) {
            const encryptionList = this.data.get('encryptionList');
            this.data.set('formData.encryptionInTransit', encryptionList[0].value);
        }
    }

    async verify() {
        await this.ref('form').validateFields();
    }

    getPriceData(): NormalObject {
        return {};
    }

    getCheckData() {
        const {authenticationMode} = this.data.get('formData');
        return {
            authenticationModes: getAuthenticationModeByFormData(authenticationMode),
        };
    }

    getOrderItemData() {
        const {authenticationMode, encryptionInTransit} = this.data.get('formData');
        return {
            title: AccessTextMap.title,
            datasource: [
                {
                    label: AccessTextMap.authenticationMode,
                    value: renderAuthentictionMode(authenticationMode),
                },
                {
                    label: AccessTextMap.encryptionInTransit,
                    value: encryptionInTransit,
                },
            ],
        };
    }

    getConfirmData() {
        const {authenticationMode, encryptionInTransit} = this.data.get('formData');
        return {
            authenticationModes: getAuthenticationModeByFormData(authenticationMode),
            encryptionInTransit: [encryptionInTransit],
            aclEnabled: authenticationMode === AuthenticationModeList.SASL_PLAIN ? true : false,
        };
    }
}
