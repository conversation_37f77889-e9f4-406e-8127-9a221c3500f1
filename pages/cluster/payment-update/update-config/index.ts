/**
 * 付费及地域模块
 *
 * @file index.ts
 * <AUTHOR>
 */
import {Component} from 'san';

import {html} from '@baiducloud/runtime';
import {} from '@baidu/sui-biz';
import {Form, Radio, Switch, Select, Tag, Alert} from '@baidu/sui';
import {DocLink} from '@components/index';
import {PREPAY_TIME_LIST, AUTO_RENEW_MONTH, AUTO_RENEW_YEAR} from '@common/enums';
import {DOC_LINK} from '@common/docs';
import {renderSwitch} from '@common/html';

import './index.less';
import {formatPayment, formatTimeLengthUnit} from '@common/utils';
import {PaymentTextMap} from '@pages/cluster/create/payment-region';

const klass = 'payment-update-config';

const prepaidTimeList = PREPAY_TIME_LIST.toArray();

export class UpdateConfig extends Component {
    static template = html`
        <div class="${klass}">
            <s-alert skin="info" class="mb16">
                目前产品支持由后付费变更为预付费模式，您需要确定预付费的购买时长和续费方式
            </s-alert>
            <s-form s-ref="form" rules="{{rules}}" data="{{formData}}">
                <s-form-item label="${PaymentTextMap.timeChoice}">
                    <s-radio-group
                        value="{= formData.timeLength =}"
                        radioType="button"
                        on-change="onTimeLengthChange"
                        enhanced
                    >
                        <s-radio s-for="item, index in prepaidTimeList" value="{{item.value}}">
                            <s-tag class="sale" s-if="item.value > 9" skin="danger" enhanced> 折扣 </s-tag>
                            {{item.text}}
                        </s-radio>
                    </s-radio-group>
                </s-form-item>
                <s-form-item label="${PaymentTextMap.autoSwitch}">
                    <s-switch checked="{= formData.autoRenew.renew =}" on-change="onChange" />
                    <doc-link href="${DOC_LINK.autoRenew}" target="blank" skin="primary"> 什么是自动续费？ </doc-link>
                </s-form-item>
                <s-form-item s-if="formData.autoRenew.renew" label="${PaymentTextMap.autoLength}">
                    <s-select
                        datasource="{{ autoUnits }}"
                        value="{= formData.autoRenew.renewTimeUnit =}"
                        on-change="onRenewTimeUnitChange"
                    />
                    <s-select
                        datasource="{{isRenewOfMonth ? autoRenewOfMonth : autoRenewOfYear}}"
                        value="{= formData.autoRenew.renewTimeLength =}"
                        class="ml16"
                        on-change="onRenewTimeLengthChange"
                    />
                    <span slot="help" class="desc mt10 inline-desc">
                        系统将于到期7天前进行扣费，扣费时长为
                        <span>{{formData.autoRenew.renewTimeLength}} {{isRenewOfMonth? '月' : '年' }}</span>
                    </span>
                </s-form-item>
            </s-form>
        </div>
    `;

    static components = {
        's-alert': Alert,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-tag': Tag,
        's-switch': Switch,
        's-select': Select,
        'doc-link': DocLink
    };

    initData() {
        return {
            prepaidTimeList: prepaidTimeList,
            autoUnits: [
                {text: '按月', value: 'month'},
                {text: '按年', value: 'year'}
            ],
            autoRenewOfMonth: AUTO_RENEW_MONTH.toArray(),
            autoRenewOfYear: AUTO_RENEW_YEAR.toArray(),
            formData: {
                timeLength: 1,
                autoRenew: {
                    renew: true,
                    renewTimeUnit: 'month',
                    renewTimeLength: 1
                }
            }
        };
    }

    static filters: SanFilterProps = {
        formatPayment,
        renderSwitch
    };

    static computed = {
        isRenewOfMonth(): boolean {
            const {renewTimeUnit} = this.data.get('formData.autoRenew');
            return renewTimeUnit === 'month';
        }
    };

    attached() {
        this.fire('form-data-init', this.data.get('formData'));
    }

    onTimeLengthChange() {
        this.nextTick(() => {
            this.fire('form-data-change', this.data.get('formData'));
        });
        this.nextTick(() => {
            this.fire('price-config-change', this.data.get('formData'));
        });
    }

    onRenewTimeUnitChange() {
        this.data.set('formData.autoRenew.renewTimeLength', 1);
        this.nextTick(() => {
            this.fire('form-data-change', this.data.get('formData'));
        });
    }

    onRenewTimeLengthChange() {
        this.nextTick(() => {
            this.fire('form-data-change', this.data.get('formData'));
        });
    }

    getConfirmData() {
        const formData = this.data.get('formData');
        const {timeLength, autoRenew} = formData;
        let params: NormalObject = {
            ...formatTimeLengthUnit(timeLength),
            autoRenew: {
                renew: autoRenew.renew
            }
        };
        if (autoRenew.renew) {
            const {renewTimeLength, renewTimeUnit} = autoRenew;
            params.autoRenew.renewTimeLength = renewTimeLength;
            params.autoRenew.renewTimeUnit = renewTimeUnit;
        }

        return params;
    }
}
