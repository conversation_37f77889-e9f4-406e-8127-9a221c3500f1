/**
 * 集群原配置
 * <AUTHOR>
 */

import {html, CreatePage} from '@baiducloud/runtime';
import {ROUTE_PATH} from '@common/config';
import {Table, Link} from '@baidu/sui';
import {PaymentType} from '@common/enums';
import {IClusterDetail} from '@pages/cluster/detail/index.d';
import {PriceResult} from '@pages/cluster/create/index.d';
import './index.less';
import {formatEmpty} from '@common/utils';
import BigNumber from 'bignumber.js';

const klass = 'payment-update-prev-config';

export class PrevConfig extends CreatePage {
    static template = html`
        <template>
            <s-table columns="{{columns}}" datasource="{{datasource}}" loading="{{loading.detail}}" class="${klass}">
                <div slot="c-name">
                    <div>
                        <s-link href="#${ROUTE_PATH.clusterDetail}?&clusterId={{row.clusterId}}" skin="primary">
                            {{row.name}}
                        </s-link>
                    </div>
                    <div class="cluster-id-wrap">{{row.clusterId}}</div>
                </div>
                <div slot="c-payment">
                    <div>{{row.payment | filterPayment}}</div>
                </div>
                <div slot="c-price"><span class="price-text">￥{{row.price}}</span>/分钟</div>
            </s-table>
        </template>
    `;
    static components = {
        's-table': Table,
        's-link': Link
    };
    initData() {
        return {
            columns: [
                {
                    label: '集群名称/ID',
                    name: 'name',
                    width: 500
                },
                {
                    label: '付费类型',
                    name: 'payment',
                    width: 300
                },
                {
                    label: '付费单价',
                    name: 'price'
                }
            ]
        };
    }

    static filters: SanFilterProps = {
        filterPayment(payment: string) {
            return PaymentType.getTextFromValue(payment);
        }
    };

    static computed: SanComputedProps = {
        datasource() {
            const detail = this.data.get('detail') as IClusterDetail;
            if (!detail) {
                return [];
            }
            const {name, clusterId, payment} = detail;
            const oldPrice = this.data.get('oldPrice') as PriceResult;
            const clusterPrice = new BigNumber(this.data.get('oldPrice.cluster'));
            const publicAccessPrice = new BigNumber(this.data.get('oldPrice.publicAccess'));
            const total = clusterPrice.plus(publicAccessPrice).toNumber();
            return [
                {
                    name,
                    clusterId,
                    payment,
                    price: oldPrice ? total : formatEmpty('')
                }
            ];
        }
    };
}
