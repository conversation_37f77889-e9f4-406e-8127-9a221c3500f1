/**
 * 计费变更
 * <AUTHOR>
 */

import {html, decorators, CreatePage, redirect} from '@baiducloud/runtime';
import {AppCreatePage} from '@baidu/sui-biz';
import {ClusterCreateSuccUrl, FromMapUrl, IUrlFromValue, ROUTE_PATH, ServiceType, Units} from '@common/config';
import './index.less';
import {Steps, Button, Loading, Checkbox, Link, Tooltip} from '@baidu/sui';
import {CommonCard} from '@components/common-card';
import {PaymentType, Payment} from '@common/enums';
import {api} from '@common/client';
import {IClusterDetail} from '../detail/index.d';
import {UpdateConfig} from './update-config';
import {PrevConfig} from './prev-config';
import {
    formatEmpty,
    formatPrice,
    formatStorageSize,
    formatStorageType,
    formatTimeLengthUnit,
    formatTotalStorageSize,
    getPayLoop,
    setDisplay
} from '@common/utils';
import {OrderConfirmItem} from '../components/order-item';
import {PaymentTextMap} from '../create/payment-region';
import {renderSwitch} from '@common/html';
import {NetworkTextMap} from '../create/network-config';
import {NodeConfigTextMap} from '../create/node-config';
import {DiskTextMap} from '../create/disk-config';
import {PriceResult} from '../create/index.d';
import {CouponConfig, PriceItem} from '../components';
import BigNumber from 'bignumber.js';
import {AgreementLink} from '@common/docs';

const klass = 'page-cluster-payment-update';

@decorators.asPage(ROUTE_PATH.clusterPaymentUpdate)
export class PageClusterPaymentUpdate extends CreatePage {
    static template = html`
        <biz-create-page pageTitle="集群计费变更" backTo="{{backTo}}" class="${klass}">
            <div class="${klass}__step">
                <s-steps current="{{current}}" type="normal">
                    <s-step title="计费变更" />
                    <s-step title="确认订单" />
                </s-steps>
            </div>
            <div class="${klass}__content" style="{{current === 1 | setDisplay}}">
                <common-card title="原套餐配置">
                    <prev-config loading="{{loading}}" detail="{{detail}}" oldPrice="{{oldPrice}}" />
                </common-card>
                <common-card title="按量转为包年包月" class="mt16">
                    <update-config
                        s-ref="updateConfig"
                        on-form-data-init="onFormDataInit"
                        on-form-data-change="onFormDataChange"
                        on-price-config-change="onPriceConfigChange"
                    />
                </common-card>
            </div>

            <div
                class="${klass}__content {{current === 2 ? 'step2-content' : ''}}"
                style="{{current === 2 | setDisplay}}"
            >
                <common-card class="${klass}__order-confirm">
                    <order-confirm-item item="{{orderConfirmData}}"></order-confirm-item>
                </common-card>
                <common-card class="mt16">
                    <coupon-config
                        class="${klass}__coupon"
                        s-ref="couponConfigRef"
                        decountPrice="{{decountPrice}}"
                        on-coupon-select-change="onCouponSelectChange"
                    />
                </common-card>
            </div>

            <div slot="pageFooter">
                <template s-if="current === 1">
                    <s-button skin="primary" size="large" on-click="onNext">确认订单</s-button>
                    <s-button size="large" class="ml16" on-click="onCancel">取消</s-button>
                </template>
                <template s-if="current === 2">
                    <div class="${klass}__protocol">
                        <span s-if="!isProtocolChecked" class="${klass}__protocol-agreement-tip">
                            请先阅读并同意服务协议信息
                        </span>
                        <span class="${klass}__protocol-checkbox">
                            <s-checkbox checked="{= isProtocolChecked =}">我已阅读并同意</s-checkbox>
                        </span>
                        <s-link href="${AgreementLink}" target="_blank" skin="primary">
                            《百度智能云线上订购协议》
                        </s-link>
                    </div>
                    <s-button size="large" on-click="onPrev" disabled="{{loading.confirm}}">上一步</s-button>
                    <s-button
                        skin="primary"
                        size="large"
                        class="ml16"
                        on-click="onConfirm"
                        loading="{{loading.confirm}}"
                    >
                        提交订单
                    </s-button>
                </template>
                <s-loading loading="{{loading.newPrice}}">
                    <div class="${klass}__price-wrap">
                        <template s-if="{{current === 1}}">
                            <price-item class="${klass}__price-item" title="集群费用：" price="{{clusterPrice}}" />
                            <price-item
                                s-if="{{detail.publicAccessEnabled}}"
                                class="${klass}__price-item"
                                title="网络费用："
                                price="{{publicAccessPrice}}"
                            />
                        </template>
                        <template s-else>
                            <price-item class="${klass}__price-item" title="总金额：" price="{{totalPrice}}" />
                            <price-item class="${klass}__price-item" title="抵扣金额：" price="{{decountPrice}}" />
                            <price-item class="${klass}__price-item" title="实付金额：" price="{{finalPrice}}" />
                        </template>
                    </div>
                </s-loading>
            </div>
        </biz-create-page>
    `;
    static components = {
        'biz-create-page': AppCreatePage,
        's-steps': Steps,
        's-step': Steps.Step,
        'common-card': CommonCard,
        'prev-config': PrevConfig,
        'update-config': UpdateConfig,
        's-button': Button,
        'order-confirm-item': OrderConfirmItem,
        'coupon-config': CouponConfig,
        's-loading': Loading,
        'price-item': PriceItem,
        's-checkbox': Checkbox,
        's-link': Link,
        's-tooltip': Tooltip
    };

    static filters: SanFilterProps = {
        filterPayment(payment: string) {
            return PaymentType.getTextFromValue(payment);
        },
        setDisplay
    };

    static computed: SanComputedProps = {
        backTo() {
            const {from = IUrlFromValue.List, clusterId} = this.data.get('route.query') as {
                from: IUrlFromValue;
                clusterId: string;
            };
            const urlSuffix = from === IUrlFromValue.Detail ? `?clusterId=${clusterId}` : '';
            return FromMapUrl[from] + urlSuffix;
        },
        clusterPrice() {
            const newPrice = this.data.get('newPrice') as PriceResult;
            if (!newPrice) {
                return formatEmpty('');
            }
            return formatPrice(newPrice.cluster, Payment.Prepaid, this.data.get('updateFormData.timeLength'));
        },
        publicAccessPrice() {
            const newPrice = this.data.get('newPrice') as PriceResult;
            if (!newPrice) {
                return formatEmpty('');
            }
            return formatPrice(newPrice.publicAccess, Payment.Prepaid, this.data.get('updateFormData.timeLength'));
        },
        totalPrice() {
            const newPrice = this.data.get('newPrice');
            if (!newPrice) {
                return 0;
            }
            const clusterPrice = new BigNumber(this.data.get('newPrice.cluster'));
            const publicAccessPrice = new BigNumber(this.data.get('newPrice.publicAccess'));
            const total = clusterPrice.plus(publicAccessPrice).toNumber();
            return total;
        },

        decountPrice(): number {
            const couponPrice = this.data.get('currentSelectCoupon.balance') || 0;
            const totalPrice = this.data.get('totalPrice');
            return couponPrice > totalPrice ? totalPrice : couponPrice;
        },
        finalPrice() {
            const totalPrice = new BigNumber(this.data.get('totalPrice'));
            const couponPrice = this.data.get('currentSelectCoupon.balance') || 0;
            const payPrice = totalPrice.minus(couponPrice).toNumber();
            return `¥${payPrice <= 0 ? 0 : payPrice}`;
        }
    };

    initData() {
        return {
            current: 1,
            detail: null,
            loading: {
                detail: false,
                newPrice: false,
                confirm: false
            },
            orderConfirmData: null,
            oldPrice: null,
            newPrice: null,
            updateFormData: {},
            isProtocolChecked: false,
            currentSelectCoupon: null
        };
    }

    attached() {
        this.getDetail();
    }

    async getDetail() {
        try {
            const {clusterId} = this.data.get('route.query');
            this.data.set('loading.detail', true);
            const res = await api.clusterDetail({clusterId});
            this.data.set('detail', res);
            this.getOldPrices();
            this.getNewPrices();
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('loading.detail', false);
        }
    }

    onFormDataInit(updateFormData: NormalObject) {
        this.data.merge('updateFormData', updateFormData);
    }
    onFormDataChange(updateFormData: NormalObject) {
        this.data.merge('updateFormData', updateFormData);
    }

    onPriceConfigChange() {
        this.getNewPrices();
    }

    onNext() {
        const orderConfirmData = this.getOrderItemData();
        this.data.set('orderConfirmData', orderConfirmData);
        const current = this.data.get('current');
        this.data.set('current', current + 1);
    }

    onCancel() {
        const backTo = this.data.get('backTo');
        redirect(`#${backTo}`);
    }

    async getOldPrices() {
        const oldPrice = await this.getPrices({
            payment: Payment.Postpaid
        });
        this.data.set('oldPrice', oldPrice);
    }

    async getNewPrices() {
        const {timeLength} = this.data.get('updateFormData');
        this.data.set('loading.newPrice', true);
        const newPrice = await this.getPrices({
            payment: Payment.Prepaid,
            ...formatTimeLengthUnit(timeLength)
        });
        this.data.set('newPrice', newPrice);
        this.data.set('loading.newPrice', false);
    }

    async getPrices(paymentParams: NormalObject) {
        const detail = this.data.get('detail') as IClusterDetail;
        const {
            nodeType,
            numberOfBrokerNodes,
            storageSize,
            storageType,
            numberOfDisks,
            publicAccessEnabled,
            publicAccessMode,
            publicAccessBandwidth
        } = detail;
        let params: NormalObject = {
            ...paymentParams,
            nodeType,
            numberOfBrokerNodes,
            storageSize,
            storageType,
            numberOfDisks,
            publicAccessEnabled,
            publicAccessMode
        };
        if (publicAccessEnabled) {
            params.publicAccessBandwidth = publicAccessBandwidth;
        }
        const res = (await api.getPrices(params)) as PriceResult;
        return res;
    }

    getOrderItemData() {
        const {timeLength, autoRenew} = this.data.get('updateFormData');
        const detail = this.data.get('detail') as IClusterDetail;
        const {
            name,
            publicAccessEnabled,
            publicAccessBandwidth,
            nodeSpec,
            numberOfBrokers,
            numberOfBrokerNodes,
            storageType,
            storageSize,
            numberOfDisks
        } = detail;
        let datasource: Array<{label: string; value: any}> = [
            {
                label: PaymentTextMap.timeChoice,
                value: timeLength <= 9 ? timeLength + '个月' : timeLength / 12 + '年'
            },
            {
                label: PaymentTextMap.autoSwitch,
                value: renderSwitch(autoRenew.renew)
            }
        ];

        let priceInfoList = [
            {
                title: '集群费用：',
                price: this.data.get('clusterPrice')
            }
        ];

        if (autoRenew.renew) {
            datasource.push({
                label: '自动续费周期：',
                value: getPayLoop(autoRenew.renewTimeLength, autoRenew.renewTimeUnit)
            });
        }

        datasource = datasource.concat([
            {
                label: '集群名称：',
                value: name
            },
            {
                label: PaymentTextMap.payment,
                value: PaymentType.getTextFromValue(Payment.Prepaid)
            },
            {
                label: NetworkTextMap.publicAccessEnabled,
                value: renderSwitch(publicAccessEnabled)
            }
        ]);

        if (publicAccessEnabled) {
            datasource.push({
                label: NetworkTextMap.publicAccessBandwidth,
                value: `按带宽付费 ${publicAccessBandwidth}${Units.Mbps}`
            });
            priceInfoList.push({
                title: '网络费用：',
                price: this.data.get('publicAccessPrice')
            });
        }

        datasource = datasource.concat([
            {
                label: NodeConfigTextMap.nodeType,
                value: nodeSpec
            },
            {
                label: NodeConfigTextMap.numberOfBrokers,
                value: numberOfBrokers
            },
            {
                label: NodeConfigTextMap.numberOfBrokerNodes,
                value: numberOfBrokerNodes
            },

            {label: DiskTextMap.storageType, value: formatStorageType(storageType)},
            {
                label: DiskTextMap.storageSize,
                value: formatStorageSize({storageSize, numberOfDisks, storageType})
            },
            {
                label: DiskTextMap.totalStorageSize,
                value: formatTotalStorageSize({storageSize, numberOfDisks, storageType, numberOfBrokerNodes})
            }
        ]);

        return {
            title: '消息服务（RocketMQ）',
            priceInfo: priceInfoList,
            datasource
        };
    }

    onPrev() {
        const current = this.data.get('current');
        this.data.set('current', current - 1);
    }

    onCouponSelectChange(data: {currentSelectCoupon: any}) {
        this.data.set('currentSelectCoupon', data.currentSelectCoupon);
    }

    async onConfirm() {
        const isProtocolChecked = this.data.get('isProtocolChecked');
        if (!isProtocolChecked) {
            return;
        }

        try {
            const detail = this.data.get('detail') as IClusterDetail;
            const updateFormData = (this.ref('updateConfig') as UpdateConfig).getConfirmData();
            const {clusterId} = detail;
            const currentSelectCoupon = this.data.get('currentSelectCoupon');

            let params: NormalObject = {
                clusterId,
                ...updateFormData
            };
            if (currentSelectCoupon?.id) {
                params.couponIds = [currentSelectCoupon.id.toString()];
            }
            this.data.set('loading.confirm', true);
            const res = await api.switchToPrepaid({
                clusterId,
                params
            });
            this.goOrder(res.orderId, 'TO_PREPAY');
        } catch (e) {
            console.error(e);
        } finally {
            this.data.set('loading.confirm', false);
        }
    }

    // 确认订单页
    goOrder(orderId: string, orderType: string) {
        redirect(`${ClusterCreateSuccUrl.Prepaid}${ServiceType}&orderType=${orderType}&orderId=${orderId}`);
    }
}
