.page-cluster-payment-update {
    .s-create-page-content {
        padding-bottom: 98px;
    }

    .s-create-page-content:has(.step2-content) {
        padding-bottom: 134px;
    }

    &__step {
        width: 600px;
        margin: 24px auto;
    }

    &__content {
        margin: 0 16px;

        .m-order-confirm-item {
            background-color: #fff;
        }

        .m-coupon {
            padding-top: 0;
            border: none;
        }

        .s-legend {
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 16px;
        }
    }

    &__price-wrap {
        display: flex;
    }

    &__order-confirm {
        .s-detail-cell .detail-cell {
            &:nth-child(3n + 1) label {
                width: 74px;
            }

            &:nth-child(3n + 3) label {
                width: 86px;
            }
        }
    }

    .s-create-page-footer {
        width: 100%;
        min-height: 80px;
        height: initial;

        .s-button {
            min-width: 90px;
            box-sizing: border-box;
        }

        .protocol {
            display: flex;
            align-items: center;
            padding-bottom: 16px;
        }

        .page-footer-wrapper {
            padding-top: 16px;
        }
    }

    &__protocol {
        position: relative;
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        &-agreement-tip {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 8px 0 rgba(7, 12, 20, 0.12);
            font-size: 12px;
            font-weight: 400;
            left: -8px;
            line-height: 20px;
            padding: 8px;
            position: absolute;
            text-align: center;
            top: -46px;
            color: var(--red-color);

            &::after {
                border: 6px solid transparent;
                border-top-color: #fff;
                content: '';
                height: 0;
                left: 8px;
                position: absolute;
                right: -12px;
                top: 36px;
                width: 0;
            }
        }

        &-checkbox {
            font-size: 0;
        }
    }
}
