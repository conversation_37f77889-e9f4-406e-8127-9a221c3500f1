/**
 * 集群详情
 * @file index.ts
 * <AUTHOR>
 */

import {html, decorators, DetailPage, redirect} from '@baiducloud/runtime';
import {AppDetailPage} from '@baidu/sui-biz';
import {Button, Loading, Alert} from '@baidu/sui';
import {ROUTE_PATH} from '@common/config';
import {DetailSidebar} from './sidebar';
import {DetailInfo} from './info';
import './index.less';
import {api} from '@common/client';
import {PageHeader} from '@components/page-header';
import {ClusterStatus} from '@common/enums';
import {debounce, throttle} from '@common/decorators';
import {DetailTopic} from './topic';
import {DetailMonitor} from './monitor';
import {ActionList} from './actions';
import {EndPoint} from '@pages/cluster/components';
import {AccessEndpointResult} from '../list/index.d';
import {ClusterOperations} from '../components/cluster-operations';
import {renderStatus} from '@common/html';
import UserAclList from './user-acl/list';
import {formatClusterItem} from '@common/utils';
import {ClusterStatusType} from '@common/enums';
import {DetailConsumer} from './consumer';
import DetailMessage from './message';
import _ from 'lodash';
import {Log} from './log';
const klass = 'page-cluster-detail';

const routes = [
    ROUTE_PATH.clusterDetail,
    ROUTE_PATH.clusterTopic,
    ROUTE_PATH.clusterMonitor,
    ROUTE_PATH.clusterActionList,
    ROUTE_PATH.clusterConsumer,
    ROUTE_PATH.clusterMonitor,
    ROUTE_PATH.clusterActionList,
    ROUTE_PATH.clusterUserAcl,
    ROUTE_PATH.clusterLog,
    ROUTE_PATH.clusterMessageList
];
@decorators.asPage(...routes)
export default class ClusterDetail extends DetailPage {
    REGION_CHANGE_LOCATION = `#${ROUTE_PATH.clusterList}`;
    static template = html`<div class="${klass}">
        <s-page>
            <page-header slot="pageTitle" back="#${ROUTE_PATH.clusterList}" title="{{detail.name}}">
                <!--bca-disable-next-line-->
                <template slot="title-right">{{detail | renderStatus | raw}}</template>
                <div slot="right" class="operations">
                    <s-button on-click="onRefresh">刷新</s-button>
                    <s-button on-click="onIpGet" class="ml8">接入点</s-button>
                    <cluster-operations
                        on-refresh="onRefresh"
                        row="{{detail}}"
                        moreBtnSkin="normal"
                        class="ml8"
                        on-delete-success="handleDeleteSuccess"
                        from="detail"
                    />
            </page-header>

            <s-alert
                skin="warning"
                class="${klass}__alert"
                s-if="{{showAlert}}"
            >
                {{alertText}}
            </s-alert>
            <div class="${klass}__content">
            <detail-sidebar
                clusterId="{{clusterId}}"
                active="{= active =}"
                on-change="onSidebarChange"
                detail="{{detail}}"
                whiteListMap="{{whiteListMap}}"
            />
            <s-loading loading="{{loading.content}}" size="large">
                <div class="right-content">
                    <detail-info
                        s-if="active === ROUTE_PATH.clusterDetail"
                        s-ref="detailContent"
                        detail="{{detail}}"
                        whiteListMap="{{whiteListMap}}"
                        route="{{route}}"
                        on-getDetail="getDetail"
                    />
                    <detail-topics
                        s-elif="active === ROUTE_PATH.clusterTopic"
                        s-ref="detailContent"
                        detail="{{detail}}"
                        route="{{route}}"
                        on-getDetail="getDetail"
                    />
                    <detail-consumer
                        s-elif="active === ROUTE_PATH.clusterConsumer"
                        detail="{{detail}}"
                        route="{{route}}"
                    />
                    <detail-user-acl
                        s-elif="active === ROUTE_PATH.clusterUserAcl"
                        detail="{{detail}}"
                        route="{{route}}"
                    />
                    <detail-monitor
                        s-elif="active === ROUTE_PATH.clusterMonitor"
                        s-ref="detailContent"
                        detail="{{detail}}"
                        route="{{route}}"
                    />
                    <detail-actions
                        s-elif="active === ROUTE_PATH.clusterActionList"
                        s-ref="detailContent"
                        detail="{{detail}}"
                        route="{{route}}"
                    />
                    <detail-message
                        s-elif="active === ROUTE_PATH.clusterMessageList"
                        s-ref="detailContent"
                        detail="{{detail}}"
                        route="{{route}}"
                    />
                    <detail-log
                        s-elif="active === ROUTE_PATH.clusterLog"
                        s-ref="detailContent"
                        detail="{{detail}}"
                        route="{{route}}"
                    />
                </div>
            </s-loading>
        </div>
        </s-page>
    </div> `;

    static components = {
        's-page': AppDetailPage,
        'page-header': PageHeader,
        's-button': Button,
        's-loading': Loading,
        'detail-sidebar': DetailSidebar,
        'detail-info': DetailInfo,
        'detail-topics': DetailTopic,
        'detail-user-acl': UserAclList,
        'detail-monitor': DetailMonitor,
        'detail-actions': ActionList,
        'cluster-operations': ClusterOperations,
        'detail-consumer': DetailConsumer,
        's-alert': Alert,
        'detail-log': Log,
        'detail-message': DetailMessage
    };

    static computed: SanComputedProps = {
        clusterId(): string {
            return this.data.get('route.query.clusterId');
        },
        type() {
            return this.data.get('route.path').replace('/cluster/', '');
        },
        routePath() {
            let path = this.data.get('route.path');
            return path;
        },
        showAlert() {
            const status = this.data.get('detail.status');
            return _.includes(
                [
                    ClusterStatusType.DEPLOYING,
                    ClusterStatusType.UPDATING,
                    ClusterStatusType.UPDATE_ROLLBACKING,
                    ClusterStatusType.REBOOTING,
                    ClusterStatusType.PRE_REBOOTING,
                    ClusterStatusType.PRE_UPDATING
                ],
                status
            );
        },
        alertText() {
            const status = this.data.get('detail.status');
            let alertTextMap = {
                [ClusterStatusType.DEPLOYING]: '部署中，预计需要10~30分钟。请在部署成功后创建主题、用户及权限。',
                [ClusterStatusType.UPDATING]:
                    '变更中，预计需要10~30分钟。变更进行期间仅支持各类查看操作，请在变更完成后恢复正常使用。',
                [ClusterStatusType.PRE_UPDATING]:
                    '待变更，预计需要10~30分钟。变更进行期间仅支持各类查看操作，请在变更完成后恢复正常使用。',
                [ClusterStatusType.UPDATE_ROLLBACKING]:
                    '变更回滚中，预计需要10~30分钟。回滚进行期间仅支持各类查看操作，请在回滚完成后恢复正常使用。',
                [ClusterStatusType.REBOOTING]: '重启中，预计需要10~30分钟。请在重启成功后创建主题、用户及权限。',
                [ClusterStatusType.PRE_REBOOTING]: '待重启，预计需要10~30分钟。请在重启成功后创建主题、用户及权限。'
            };
            return alertTextMap[status] || '';
        }
    };

    static filters: SanFilterProps = {
        renderStatus() {
            const detail = this.data.get('detail');

            return renderStatus(ClusterStatus.fromValue(detail?.status));
        }
    };
    initData() {
        return {
            ROUTE_PATH,
            detail: null,
            whiteListMap: {},
            loading: {
                content: false
            }
        };
    }

    inited() {
        this.getWhiteList();
        this.getDetail();
    }

    attached() {
        const active = this.data.get('routePath');
        this.data.set('active', active);
        if (active !== ROUTE_PATH.clusterDetail) {
            // 非集群详情tab，在此请求detail接口；集群详情tab会在info组件的attached当中请求
            this.getDetail();
        }
    }

    async getDetail(options?: {needLoading: boolean}) {
        const {needLoading} = options || {};
        try {
            needLoading && this.data.set('loading.content', true);
            const res = await api.clusterDetail({clusterId: this.data.get('clusterId')});
            this.data.set('detail', formatClusterItem(res));
        } catch (err) {
            console.error(err);
        } finally {
            needLoading && this.data.set('loading.content', false);
        }
    }

    // 集群详情需要白名单控制的功能都可以通过这个接口请求
    async getWhiteList() {
        const res = await api.getUserWhiteListAcl({
            featureTypes: ['ROCKETMQ_MultipleDisks', 'ROCKETMQ_ClusterLogViewer']
        });

        this.data.set('whiteListMap', res || {});
    }

    @throttle(300)
    onRefresh() {
        const refDetailContent = this.ref('detailContent') as any;
        const active = this.data.get('active');
        if (active === ROUTE_PATH.clusterDetail) {
            // tab为集群详情时刷新，展示loading态
            refDetailContent?.refresh();
        } else {
            this.getDetail({
                needLoading: false
            });
        }
    }

    @debounce(500)
    async onIpGet() {
        const detail = this.data.get('detail');
        const {clusterId, name: clusterName, encryptionInTransit} = detail;
        const res = (await api.getAccessPoints({clusterId, params: {}})) as AccessEndpointResult;
        const dialog = new EndPoint({
            data: {
                ...res,
                clusterId,
                clusterName,
                encryptionInTransit
            }
        });
        dialog.attach(document.body);
    }

    onSidebarChange(path: string) {
        this.data.set('active', path);
    }
    handleDeleteSuccess() {
        redirect(`#${ROUTE_PATH.clusterList}`);
    }
}
