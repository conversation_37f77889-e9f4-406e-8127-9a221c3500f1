@klass: page-cluster-log;

.app-view:has(.@{klass}) {
    width: 100%;
}
.page-cluster-detail__content {
    .s-loading-nested {
        overflow: hidden;
    }
    .right-content:has(.@{klass}) {
        overflow: hidden;
    }
}

.@{klass} {
    height: 100%;

    .m-block-box {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    &__operation-area {
        margin-left: auto;
        display: flex;
        align-items: center;

        .refresh-icon {
            position: relative;
            top: -1px;
        }
    }

    &__content-wrap {
        border: 1px solid rgba(232, 233, 235, 1);
        border-radius: 4px;
        display: flex;
        flex: 1;
        overflow: hidden;
    }

    &__left-wrap {
        display: flex;
        flex-direction: column;
        width: 200px;
        border-right: 1px solid rgba(232, 233, 235, 1);

        &-header {
            flex-direction: column;
            display: flex;
            padding: 0 16px;

            .s-select {
                width: 168px;
            }

            .title {
                font-size: 14px;
                font-weight: 500;
                line-height: 26px;
                margin-top: 12px;
            }
        }

        .tree-area {
            .s-loading {
                display: flex;
                vertical-align: middle;
                width: 100%;
                height: 100%;
                justify-content: center;
                align-items: center;
            }

            flex: 1;
            overflow-y: auto;
            padding: 16px 0;

            .s-tree2 li span.s-tree2-switcher.s-tree2-switcher_noop {
                display: none;
            }

            .s-tree2 li .s-tree2-node-content-wrapper {
                width: 100%;
                height: 28px;
                line-height: 28px;
                padding: 0 16px;
            }
        }
    }

    &__right-wrap {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .s-empty {
            height: 100%;
            margin-top: -30px;
        }

        .log-tabs {
            padding: 16px;
        }

        .logInfo {
            padding: 4px 0;
            border-bottom: 1px solid var(--border-color);

            .label {
                color: #5c5f66;
                margin-left: 16px;
            }
        }

        .tab-item-content-wrapper {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;

            .loading-area {
                overflow: hidden;
            }

            .s-loading-slot-wrap {
                display: flex;
                flex-direction: column;
            }
            .tab-item-content {
                flex: 1;
                overflow: auto;

                .log-detail {
                    white-space: pre-wrap;
                    padding: 0 16px;

                    .grey-text {
                        color: #84868c;
                    }

                    .line-number {
                        padding-right: 16px;
                    }
                }
            }
        }

        .tab-item-footer {
            height: 56px;
            display: flex;
            justify-content: space-between;
            padding: 12px 16px;

            .offset-area {
                display: flex;
                align-items: center;
            }

            .extra-load-desc {
                flex: 1;
                margin-left: 16px;
                color: #f39000;
            }

            .pagination {
                flex: 1;
                display: flex;
                flex-direction: row-reverse;
            }
        }
    }
}
