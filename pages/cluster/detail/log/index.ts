/**
 * 集群日志
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html, Enum} from '@baiducloud/runtime';
import {Empty} from '@baidu/sui-biz';
import {Tree2, Dialog, Pagination, Select, Button, Tooltip, Loading, Tabs} from '@baidu/sui';
import {OutlinedUp, OutlinedDown, OutlinedRefresh} from '@baidu/sui-icon';
import {api} from '@common/client';
import {formatTime, pickEmpty, setDisplay} from '@common/utils';
import './index.less';
import {BlockBox, CustomTabs} from '@components/index';
import _ from 'lodash';
import {ILogContent, ILogListItem} from '@common/client/types/log';
import {NodeClientStatusType} from '@common/enums';
const klass = 'page-cluster-log';

const pageSizeEnum = new Enum(
    {value: 256 * 1024, text: '256KB/页', alias: '256KB/页'},
    {value: 512 * 1024, text: '512KB/页', alias: '512KB/页'},
    {value: 1024 * 1024, text: '1M/页', alias: '1M/页'},
    {value: 2 * 1024 * 1024, text: '2M/页', alias: '2M/页'}
);

const extraUnit = 64 * 1024;

interface IFeLogListItem extends ILogListItem {
    /** 展示的日志文件名称 */
    label: string;
    /** 日志文件key：serviceId + '-' + filename */
    key: string;
    /** 所属节点 */
    serviceId: string;
}
type ITabItem = IFeLogListItem &
    ILogContent & {
        /** 额外加载次数 */
        totalExtraTimes: number;
        /** 往后多加载的数据量 */
        currentEndOffset: number;
        /** 往前多加载的数据量 */
        currentStartOffset: number;
    };

const extraLoadDisabled = (row: ITabItem) => {
    return row.totalExtraTimes * extraUnit >= row.pageSize;
};

export class Log extends Component {
    static template = html`<div class="${klass}">
        <block-box title="集群日志">
            <s-button on-click="onRefresh" skin="normal-stringfy" slot="extra" class="${klass}__operation-area">
                <s-outlined-refresh width="14" class="mr5 refresh-icon" />
                刷新日志
            </s-button>
            <div class="${klass}__content-wrap">
                <div class="${klass}__left-wrap">
                    <div class="${klass}__left-wrap-header">
                        <div class="title">节点ID</div>
                        <s-select
                            datasource="{{brokerList}}"
                            value="{= broker =}"
                            on-change="onSelectChange"
                            width="100%"
                        />
                    </div>
                    <div class="tree-area">
                        <s-loading s-if="{{brokerLoading}}" loading />
                        <s-tree
                            s-else
                            s-ref="log-tree"
                            empty-text=""
                            node-key="key"
                            treeData="{{logList}}"
                            lazy="{{false}}"
                            selectedKeys="{{[activeKey]}}"
                            on-select="onNodeClick"
                        />
                    </div>
                </div>
                <div class="${klass}__right-wrap">
                    <template s-if="logList.length">
                        <custom-tabs
                            s-ref="tab"
                            tabs="{= tabs =}"
                            activeKey="{= activeKey =}"
                            on-change="onTabChange"
                            on-close="onTabClose"
                        ></custom-tabs>
                        <div
                            s-for="i, index in tabs"
                            class="tab-item-content-wrapper"
                            style="{{i.key === activeKey | setDisplay}}"
                        >
                            <s-loading loading="{{detailLoading}}" size="large" class="loading-area">
                                <div class="tab-item-content" s-ref="scrollContainer{{index}}">
                                    <div class="logInfo">
                                        <span class="label">最近更新时间：</span>
                                        <span class="ml8">{{i.modifyTime | getTime}}</span>
                                        <span class="ml24 label">文件大小:</span>
                                        <span class="ml8">{{i.totalSize | getTotalSize}}</span>
                                    </div>
                                    <!--bca-disable-next-line-->
                                    <div class="log-detail">{{i.content | raw}}</div>
                                </div>
                            </s-loading>
                            <div class="tab-item-footer">
                                <div class="offset-area">
                                    <s-tooltip content="当前页面内额外加载较旧的64KB日志">
                                        <s-button
                                            on-click="downloadOlder(i, index)"
                                            disabled="{{detailLoading || olderDisabled || brokerLoading}}"
                                        >
                                            旧数据
                                            <outlined-up />
                                        </s-button>
                                    </s-tooltip>
                                    <s-tooltip content="当前页面内额外加载较新的64KB日志" class="ml8">
                                        <s-button
                                            on-click="downloadNewer(i, index)"
                                            disabled="{{detailLoading || newerDisabled || brokerLoading}}"
                                        >
                                            新数据
                                            <outlined-down />
                                        </s-button>
                                    </s-tooltip>

                                    <span class="extra-load-desc" s-if="i.totalExtraTimes">
                                        <span s-if="extraDisabled"> 当前页面已达到额外加载上限 </span>
                                        <span s-else> 已额外加载{{i | formatExtra}}KB数据 </span>
                                    </span>
                                </div>
                                <div class="pagination">
                                    <s-pagination
                                        total="{{i.totalSize}}"
                                        pageSize="{{i.pageSize}}"
                                        page="{{i.pageNo}}"
                                        layout="pageSize, pager, go"
                                        on-pagerChange="onPageChange($event, i, index)"
                                    >
                                        <span slot="size" class="page-size-select">
                                            <s-select
                                                value="{= i.pageSize =}"
                                                datasource="{{pageSizeArray}}"
                                                on-change="onPageSizeChange($event, i, index)"
                                            >
                                            </s-select>
                                        </span>
                                    </s-pagination>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template s-else>
                        <s-empty emptyText="节点下还没有任何日志文件，">
                            <s-button slot="action" skin="stringfy">请选择其他节点。</s-button>
                        </s-empty>
                    </template>
                </div>
            </div>
        </block-box>
    </div> `;
    static components = {
        'block-box': BlockBox,
        'custom-tabs': CustomTabs,
        's-tree': Tree2,
        's-pagination': Pagination,
        's-select': Select,
        's-empty': Empty,
        's-button': Button,
        'outlined-up': OutlinedUp,
        'outlined-down': OutlinedDown,
        's-tooltip': Tooltip,
        's-outlined-refresh': OutlinedRefresh,
        's-loading': Loading,
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane
    };
    initData() {
        return {
            brokerList: [],
            logList: [],
            broker: '',
            tabs: [],
            activeKey: '',
            pageSizeArray: pageSizeEnum.toArray()
        };
    }

    static computed: SanComputedProps = {
        extraDisabled() {
            const activeKey = this.data.get('activeKey');
            const tabs = this.data.get('tabs') as ITabItem[];
            const currentTab = tabs.find(item => item.key === activeKey);
            if (!currentTab) {
                return true;
            }
            return extraLoadDisabled(currentTab);
        },
        olderDisabled() {
            const activeKey = this.data.get('activeKey');
            const tabs = this.data.get('tabs') as ITabItem[];
            const currentTab = tabs.find(item => item.key === activeKey);
            if (!currentTab) {
                return true;
            }
            return currentTab.pageNo === 1 || extraLoadDisabled(currentTab);
        },
        newerDisabled() {
            const activeKey = this.data.get('activeKey');
            const tabs = this.data.get('tabs') as ITabItem[];
            const currentTab = tabs.find(item => item.key === activeKey);
            if (!currentTab) {
                return true;
            }

            return currentTab.currentEndOffset === currentTab.totalSize || extraLoadDisabled(currentTab);
        }
    };

    static filters: SanFilterProps = {
        getTabLabel(filename) {
            const broker = this.data.get('broker');
            return broker + '-' + filename;
        },
        getTotalSize(totalSize: number): string {
            if (totalSize >= 1024 * 1024) {
                return Math.floor(totalSize / (1024 * 1024)) + 'MB';
            } else if (totalSize >= 1024) {
                return Math.floor(totalSize / 1024) + 'KB';
            }
            return totalSize + 'Byte';
        },
        getTime: formatTime,
        setDisplay,
        formatExtra(row: ITabItem): number {
            return row.totalExtraTimes * 64;
        }
    };

    async attached() {
        await this.getBrokers();
    }

    // 获取节点
    async getBrokers() {
        const {clusterId} = this.data.get('route.query');
        const params = pickEmpty({
            pageNo: 1,
            pageSize: 10000
        });
        try {
            const {result} = await api.clusterNodes({clusterId, params});
            const brokerList = result.filter(item => item.status === NodeClientStatusType.ALIVE);
            this.data.set(
                'brokerList',
                brokerList.map(item => ({
                    text: item.nodeId,
                    value: item.nodeId,
                    alias: item.nodeId
                }))
            );
            this.data.set('broker', brokerList[0]?.nodeId);
            this.getLogList();
        } catch (err) {
            console.error(err);
        }
    }

    // 获取日志列表
    async getLogList() {
        this.data.set('brokerLoading', true);
        const {clusterId} = this.data.get('route.query');
        const broker = this.data.get('broker');
        if (!broker) {
            return;
        }
        const res = await api.getLogFiles({clusterId, serviceId: broker});
        const logList = res.map(item => ({
            ...item,
            label: item.filename,
            key: broker + '-' + item.filename,
            serviceId: broker
        }));

        this.data.set('logList', logList);
        const activeKey = this.data.get('activeKey');
        !activeKey && this.onNodeClick({info: {key: logList[0]?.key}});
        this.data.set('brokerLoading', false);
    }

    onSelectChange(target: {value: string}) {
        this.data.set('broker', target.value);
        if (target.value) {
            this.getLogList();
        }
    }

    onRefresh() {
        const activeKey = this.data.get('activeKey');
        const tabs = this.data.get('tabs') as ITabItem[];
        const currentTabIndex = tabs.findIndex(item => item.key === activeKey);
        currentTabIndex > -1 && this.getLogDetail(tabs[currentTabIndex], currentTabIndex);
    }

    async getLogDetail(row: ITabItem, index: number) {
        const {clusterId} = this.data.get('detail');
        const params = {
            pageNo: row.pageNo,
            pageSize: row.pageSize
        };
        try {
            this.data.set('detailLoading', true);
            const {result, totalSize} = await api.getLogFileContent({
                clusterId,
                serviceId: row.serviceId,
                filename: row.filename,
                params
            });
            const formattedLog = result.content.replace(
                /(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/g,
                '<span class="grey-text">$1</span>'
            );
            const logDetail = {
                ...result,
                content: formattedLog,
                totalSize,
                serviceId: row.serviceId,
                currentStartOffset: result.startOffset,
                currentEndOffset: result.endOffset,
                totalExtraTimes: 0
            };
            this.data.merge(`tabs[${index}]`, logDetail);
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('detailLoading', false);
        }
    }

    onTabChange(e: ITabItem) {
        this.data.set('broker', e.serviceId);
        this.getLogList();
    }

    onNodeClick(target: {info: {key: string}}) {
        const key = target.info.key;
        const tabs = this.data.get('tabs') as ITabItem[];
        const index = _.findIndex(this.data.get('tabs'), (item: ITabItem) => item.key === key);
        if (index !== -1) {
            // 如果选中的日志已经被打开
            this.data.set('activeKey', key);
            const currentTab = tabs[index];
            (this.ref('tab') as CustomTabs)?.scrollTo(currentTab);
        } else if (tabs.length >= 20) {
            // 如果当前tab个数大于等于20
            Dialog.confirm({
                title: '提示',
                content: '打开窗口数已达20上限，请关闭已打开窗口。',
                okText: '确认',
                cancelText: '取消'
            });
        } else {
            this.data.set('activeKey', key);
            const logList = this.data.get('logList');
            const currentLogItem = logList.find((item: any) => item.key === key);
            const currentRow = {
                ...currentLogItem,
                name: currentLogItem.key,
                pageNo: 1,
                pageSize: 256 * 1024
            };
            this.data.push('tabs', currentRow);
            // tab个数小于20且选中日志尚未被打开
            this.getLogDetail(currentRow, tabs.length);
            this.nextTick(() => {
                (this.ref('tab') as CustomTabs)?.scrollTo(currentRow);
            });
        }
    }

    onPageChange(e: {value: {page: number; pageSize: number}}, row: ITabItem, index: number) {
        const newRow = {
            ...row,
            pageNo: e.value.page
        };
        this.data.set(`tabs[${index}]`, newRow);
        this.scrollTo(index, 0);
        this.getLogDetail(newRow, index);
    }

    onPageSizeChange(target: {value: number}, row: ITabItem, index: number) {
        const newRow = {
            ...row,
            pageSize: target.value,
            pageNo: 1
        };
        this.data.set(`tabs[${index}]`, newRow);
        this.scrollTo(index, 0);
        this.getLogDetail(newRow, index);
    }

    scrollTo(index: number, top: number) {
        (this.ref(`scrollContainer${index}`) as HTMLElement)?.scrollTo({
            top,
            behavior: 'smooth'
        });
    }

    async downloadOlder(row: any, index: number) {
        this.data.set('detailLoading', true);
        const params = {
            currentStartOffset: row.currentStartOffset,
            backwardOffset: extraUnit
        };
        const {clusterId} = this.data.get('detail');
        const {result} = await api.getLogFileContent({
            clusterId,
            serviceId: row.serviceId,
            filename: row.filename,
            params
        });
        this.data.merge(`tabs[${index}]`, {
            totalExtraTimes: row.totalExtraTimes + 1,
            content: result.content + row.content,
            currentStartOffset: result.startOffset
        });
        this.data.set('detailLoading', false);
        this.scrollTo(index, 0);
    }

    async downloadNewer(row: any, index: number) {
        this.data.set('detailLoading', true);
        const params = {
            currentEndOffset: row.currentEndOffset,
            forwardOffset: extraUnit
        };
        const {clusterId} = this.data.get('detail');
        const {result} = await api.getLogFileContent({
            clusterId,
            serviceId: row.serviceId,
            filename: row.filename,
            params
        });
        const scrollHeight = (this.ref(`scrollContainer${index}`) as HTMLElement)?.scrollHeight;
        this.data.merge(`tabs[${index}]`, {
            totalExtraTimes: row.totalExtraTimes + 1,
            content: row.content + result.content,
            currentEndOffset: result.endOffset
        });
        this.data.set('detailLoading', false);

        this.scrollTo(index, scrollHeight);
    }

    onTabClose(e: any) {
        if (e.type === 'all') {
            Dialog.confirm({
                title: '关闭全部',
                content: '关闭全部标签当前页面标签全部关闭，确认是否继续关闭。',
                okText: '确定关闭',
                cancelText: '取消',
                onOk: () => {
                    // 关闭
                    e.callback();
                }
            });
        }
    }
}
