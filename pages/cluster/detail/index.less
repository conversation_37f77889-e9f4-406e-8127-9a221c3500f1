.page-cluster-detail {
    min-width: 1280px;

    &__alert {
        margin: 0 0 15px;
    }

    &__content {
        flex: 1 1 50%;
        display: flex;
        flex-direction: row;
        overflow-y: auto;
    }

    .operations {
        display: flex;
    }

    .s-detail-page {
        height: 100%;
        overflow-y: hidden;

        .page-header__title {
            .status {
                margin-left: 8px;
            }
        }
    }

    .s-detail-page-content {
        overflow-y: hidden;

        .s-loading-nested {
            flex: 1;
        }

        .s-loading-slot-wrap {
            height: 100%;
        }

        .right-content {
            height: 100%;
            padding: 16px;
            overflow-y: auto;
        }
    }
}
