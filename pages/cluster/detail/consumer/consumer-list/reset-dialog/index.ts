import _ from 'lodash';
import {Component} from 'san';
import {Dialog, Form, Notification, Select, Radio, DatePicker, Tooltip, Alert} from '@baidu/sui';
import {api} from '@common/client';
import {VALIDATE_ITEMS} from '@common/rules';
import {PAGER_MAX, PAGER_SUI} from '@common/config';
import {TimeType, TimeTypeArr} from '@common/enums';
import {Base64} from 'js-base64';
import './index.less';
import {html} from '@baiducloud/runtime';
const klass = 'reset-site';
const template = html`<template>
    <s-dialog
        title="重置消费进度"
        okText="确定"
        class="${klass}"
        open="{= open =}"
        on-confirm="onConfirm"
        on-close="onClose"
        confirming="{{confirming}}"
        loadingAfterConfirm="{{false}}"
    >
        <s-alert skin="warning"> 重置消费进度会改变消费位点，可能引起重复消费 </s-alert>
        <s-form s-ref="form" data="{= formData =}" rules="{{rules}}">
            <s-form-item prop="topicName" label="主题名称：">
                <s-select width="400" value="{= formData.topicName =}" filterable width="320">
                    <s-tooltip s-for="item in topics" content="{{item.tip}}" placement="right">
                        <s-option value="{{item.value}}" label="{{item.label}}" />
                    </s-tooltip>
                </s-select>
            </s-form-item>
            <s-form-item prop="timestamp" label="时间：">
                <s-radio-group
                    value="{= formData.timestamp =}"
                    radioType="button"
                    on-change="onPreCheckResetStrategy"
                    datasource="{{TimeTypeArr}}}"
                    enhanced
                >
                </s-radio-group>
            </s-form-item>
            <s-form-item prop="custom" class="${klass}--reset-datepicker">
                <s-date-picker
                    s-if="{{formData.timestamp === TimeType.Custom}}"
                    class="${klass}--reset-strategy"
                    value="{= formData.custom =}"
                    mode="second"
                    getPopupContainer="{{getPopupContainer}}"
                />
            </s-form-item>
        </s-form>
    </s-dialog>
</template>`;

export default class extends Component {
    static template = template;

    static components = {
        's-dialog': Dialog,
        's-select': Select,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-date-picker': DatePicker,
        's-option': Select.Option,
        's-tooltip': Tooltip,
        's-alert': Alert
    };

    initData() {
        return {
            open: true,
            topics: [],
            formData: {
                timestamp: TimeType.Custom,
                custom: new Date()
            },
            TimeTypeArr,
            TimeType,
            rules: {
                topicName: [VALIDATE_ITEMS.requiredSelect],
                timestamp: [VALIDATE_ITEMS.requiredSelect],
                custom: [
                    {required: true, message: '请选择时间'},
                    {
                        validator: (rule, value, callback) => {
                            if (!value) {
                                return callback('请选择时间');
                            }
                            callback();
                        }
                    }
                ]
            },
            getPopupContainer: () => document.body,
            pager: {...PAGER_SUI}
        };
    }

    attached() {
        this.getTopicList();
    }

    async onConfirm() {
        try {
            await (this.ref('form') as unknown as Form).validateFields();
            this.data.set('confirming', true);
            const {consumerGroupName, clusterId, formData} = this.data.get('');
            let {topicName, timestamp, custom} = this.data.get('formData');
            const groupName = consumerGroupName;
            timestamp = timestamp === TimeType.Custom ? new Date(custom).getTime() : timestamp;
            let params = {
                topicName: Base64.encodeURI(topicName),
                timestamp
            };
            await api.resetClusterOffset({clusterId, params, groupName});
            Notification.success('重置消费进度成功');
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        } catch (err) {
            console.error(err);
            this.data.set('confirming', false);
        }
    }

    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }

    // 获取主题信息
    async getTopicList() {
        const {clusterId, consumerGroupName} = this.data.get('');
        const params = {
            ...PAGER_MAX
        };
        const groupName = consumerGroupName;
        const data = await api.getConsumerGroupTopics({clusterId, groupName, params}, {});
        const {result} = data;
        this.data.set(
            'topics',
            result?.map(item => ({text: item.topicName, value: item.topicName, tip: item.topicName}))
        );
        this.data.set('formData.topicName', result[0]?.topicName);
    }
}
