import {html} from '@baiducloud/runtime';
import {BlockBox, CommonTable, CreateBtn, RefreshBtn, EllipsisTip} from '@components/index';
import {Search, Button, Table, Link, Pagination} from '@baidu/sui';
import {AppListPage, Empty} from '@baidu/sui-biz';
import {TABLE_SUI, SELECTION_SUI_MULTI, PAGER_SUI, PAGINATION_LAYOUT} from '@common/config';
import {isClusterStatusDisabled, pickEmpty, updateUrlQuery} from '@common/utils';
import {api} from '@common/client';
import './index.less';
import {ClusterConsumerMessageModel} from '@common/enums';
import {DetailConsumerDeleteDialog} from './delete-dialog';
import {DetailConsumerCreateDialog} from './create-comsumer-dialog';
import {IClusterDetail} from '../../index.d';
import ResetSite from './reset-dialog';
export interface ConsumerListProps {
    groupName: string;
    brokerNames: string[];
    consumeBroadcastEnable: boolean;
    retryMaxTimes: number;
    messageModel: string;
}

const klass = 'm-detail-consumer-list';

export const columns = [
    {
        name: 'groupName',
        label: '消费组名称',
        width: 120
    },
    {
        name: 'brokerNames',
        label: '关联节点组',
        width: 230
    },
    {
        name: 'retryMaxTimes',
        label: '最大重试次数',
        width: 100
    },
    {
        name: 'consumeBroadcastEnable',
        label: '广播模式消费',
        width: 130,
        render: (item: ConsumerListProps) => (item.consumeBroadcastEnable ? '允许' : '禁止')
    },
    {
        name: 'messageModel',
        label: '消费模式',
        width: 90,
        render: (item: ConsumerListProps) => ClusterConsumerMessageModel.getTextFromValue(item.messageModel)
    },
    {
        name: 'operation',
        label: '操作',
        width: 200
    }
];

const template = html`
    <template>
        <block-box title="消费组管理" class="${klass}">
            <app-list-page class="${klass}-content">
                <div slot="bulk">
                    <create-btn on-click="onCreate" disabled="{{operationDisabled}}">创建消费组</create-btn>
                    <s-button
                        class="ml8"
                        disabled="{{operationDisabled || !selection.selectedIndex.length}}"
                        on-click="onDelete()"
                        width="{{46}}"
                    >
                        删除
                    </s-button>
                </div>
                <div slot="filter">
                    <s-search
                        value="{= keyword =}"
                        placeholder="请输入消费组名称进行搜索"
                        on-search="onSearch"
                        clearable
                    />
                    <refresh-btn on-click="onRefresh" />
                </div>
                <s-table
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    datasource="{{table.datasource}}"
                    selection="{{selection}}"
                    on-selected-change="onSelectChange"
                    on-sort="onSort"
                    on-filter="onFilter"
                >
                    <div class="table-ellipsis" slot="c-groupName">
                        <s-button skin="stringfy" class="table-btn-slim name-btn" on-click="onGroupNameClick(row)">
                            <ellipsis-tip text="{{row.groupName}}" placement="top"> {{row.groupName}} </ellipsis-tip>
                        </s-button>
                    </div>
                    <div class="table-ellipsis" slot="c-brokerNames">
                        <ellipsis-tip text="{{row.brokerNames}}" placement="top"> {{row.brokerNames}} </ellipsis-tip>
                    </div>
                    <div slot="c-operation">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="onReset(row)"
                            disabled="{{operationDisabled}}"
                        >
                            重置消费进度
                        </s-button>
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="onEdit(row)"
                            disabled="{{operationDisabled}}"
                        >
                            编辑
                        </s-button>
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="onDelete(row)"
                            disabled="{{operationDisabled}}"
                        >
                            删除
                        </s-button>
                    </div>
                    <div slot="empty">
                        <s-empty
                            vertical
                            emptyTitle="暂无数据"
                            emptyText="{{keyword ? '暂无符合条件的消费组，您可以尝试更换筛选条件。 ' : '您还没有创建任何消费组。 '}}"
                        >
                            <create-btn
                                slot="action"
                                on-click="onCreate"
                                skin="stringfy"
                                disabled="{{operationDisabled}}"
                            >
                                创建消费组
                            </create-btn>
                        </s-empty>
                    </div>
                </s-table>
                <s-pagination
                    slot="pager"
                    s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                    layout="${PAGINATION_LAYOUT}"
                    total="{{pager.count}}"
                    pageSize="{{pager.pageSize}}"
                    page="{{pager.page}}"
                    pageSizes="{{pager.pageSizes}}"
                    max-item="{{7}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange"
                />
            </app-list-page>
        </block-box>
    </template>
`;

export class DetailConsumerList extends CommonTable {
    static template = template;

    static components = {
        'block-box': BlockBox,
        'create-btn': CreateBtn,
        'app-list-page': AppListPage,
        'ellipsis-tip': EllipsisTip,
        's-empty': Empty,
        's-search': Search,
        's-button': Button,
        'refresh-btn': RefreshBtn,
        's-table': Table,
        's-link': Link,
        's-pagination': Pagination
    };

    static computed = {
        operationDisabled() {
            const detail = this.data.get('detail') as IClusterDetail;
            return isClusterStatusDisabled(detail);
        }
    };

    initData() {
        return {
            keyword: '',
            table: {
                ...TABLE_SUI,
                columns
            },
            selection: {
                ...SELECTION_SUI_MULTI
            },
            pager: {...PAGER_SUI}
        };
    }

    async attached() {
        this.getComList();
    }

    async getTableList() {
        const {keyword, pager, route} = this.data.get('');
        const {clusterId} = route.query;
        let param = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            groupName: keyword
        });
        const {totalCount, result} = await api.consumerGroupsList({params: param, clusterId});
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', result);
    }

    onSelectChange(event: {value: {selectedIndex: number[]; selectedItems: Array<Object>}}) {
        this.data.set('selection.selectedIndex', event.value.selectedIndex);
    }
    onReset(row: ConsumerListProps) {
        const {
            route: {
                query: {clusterId}
            }
        } = this.data.get('');
        const dialog = new ResetSite({
            data: {
                clusterId,
                consumerGroupName: row.groupName
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onOperateRefresh(true);
        });
    }

    onCreate() {
        const {
            route: {
                query: {clusterId}
            }
        } = this.data.get('');
        const dialog = new DetailConsumerCreateDialog({
            data: {
                clusterId
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onOperateRefresh(true);
        });
    }

    onEdit(row: ConsumerListProps) {
        const {
            route: {
                query: {clusterId}
            }
        } = this.data.get('');
        const dialog = new DetailConsumerCreateDialog({
            data: {
                clusterId,
                formData: {
                    groupName: row.groupName,
                    brokerNames: [...row.brokerNames],
                    consumeBroadcastEnable: row.consumeBroadcastEnable,
                    retryMaxTimes: row.retryMaxTimes
                },
                isEdit: true
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onOperateRefresh();
        });
    }

    async onDelete(row?: ConsumerListProps) {
        const {
            selection: {selectedIndex},
            table: {datasource},
            route: {
                query: {clusterId}
            }
        } = this.data.get('');
        const dialogDatasource = row
            ? [row]
            : datasource.filter((item: ConsumerListProps, index: number) => selectedIndex.includes(index));

        const dialog = new DetailConsumerDeleteDialog({
            data: {
                datasource: dialogDatasource,
                columns: columns.slice(0, -1),
                clusterId
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onOperateRefresh(true);
        });
    }

    onGroupNameClick(row: ConsumerListProps) {
        updateUrlQuery({groupName: row.groupName});
        this.data.set('groupName', row.groupName);
    }

    onOperateRefresh(isInitRefresh: boolean = false) {
        if (isInitRefresh) {
            // TODO 创建 & 删除 是否需要完全刷新 清空搜索关键字并跳到第一页
            this.data.set('keyword', '');
            this.onPageChange({value: {page: 1, pageSize: this.data.get('pager.pageSize')}});
        } else {
            this.onRefresh();
        }
    }
}
