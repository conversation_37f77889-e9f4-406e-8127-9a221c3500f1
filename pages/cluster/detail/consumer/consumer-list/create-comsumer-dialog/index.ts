import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Form, Switch, InputNumber, Select, Input, Notification, Button} from '@baidu/sui';
import './index.less';
import {api} from '@common/client';

const klass = 'detail-consumer-create-dialog';

const nameRegTip = '支持字母a~z或A~Z、数字0~9以及下划线（_）、短划线（-）和百分号（%）';

export class DetailConsumerCreateDialog extends Component {
    static template = html`
        <template>
            <s-dialog
                class="${klass}"
                title="{{isEdit ? '编辑' : '创建'}}消费组"
                width="{{600}}"
                open="{{open}}"
                confirming="{{confirming}}"
                on-confirm="onConfirm"
                on-close="onClose"
            >
                <s-form s-ref="form" data="{=formData=}" label-align="left" isRequired="{{true}}" rules="{{rules}}">
                    <s-form-item prop="groupName" label="消费组名称：" help="${nameRegTip}">
                        <template s-if="isEdit">{{formData.groupName}}</template>
                        <s-input s-else value="{=formData.groupName=}" width="{{400}}" />
                    </s-form-item>
                    <s-form-item prop="brokerNames" label="关联节点组：">
                        <s-select
                            datasource="{{brokerList}}"
                            multiple
                            value="{=formData.brokerNames=}"
                            taggable
                            width="{{400}}"
                        />
                    </s-form-item>
                    <s-form-item
                        prop="retryMaxTimes"
                        label="最大重试次数："
                        help="消费失败后重试次数上限，取值范围 1-16"
                        required
                    >
                        <s-input-number
                            value="{=formData.retryMaxTimes=}"
                            min="{{1}}"
                            max="{{16}}"
                            stepStrictly
                            width="{{80}}"
                        />
                    </s-form-item>
                    <s-form-item prop="consumeBroadcastEnable" label="广播模式消费：" required>
                        <s-switch checked="{=formData.consumeBroadcastEnable=}" />
                    </s-form-item>
                </s-form>
                <div slot="footer">
                    <s-button on-click="onClose">取消</s-button>
                    <s-button skin="primary" on-click="onConfirm">确定</s-button>
                </div>
            </s-dialog>
        </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-switch': Switch,
        's-input-number': InputNumber,
        's-select': Select,
        's-input': Input,
        's-button': Button
    };

    static filters = {
        formatBrokerNames(brokerNames: string[]) {
            return brokerNames.join('，');
        }
    };

    initData() {
        return {
            formData: {
                groupName: '',
                brokerNames: [],
                retryMaxTimes: 1,
                consumeBroadcastEnable: false
            },
            rules: {
                groupName: [
                    {
                        required: true,
                        message: `请输入消费组名称，${nameRegTip}`
                    },
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (value.length > 64) {
                                return callback('不能超过64个字符');
                            }
                            if (!/^[0-9a-zA-Z_\w-%]{1,64}$/.test(value)) {
                                return callback(`输入格式有误。${nameRegTip}`);
                            }
                            callback();
                        }
                    }
                ],
                brokerNames: [
                    {
                        required: true,
                        message: '请选择关联节点组'
                    }
                ]
            },
            isEdit: false,
            open: true,
            brokerList: [],
            confirming: false
        };
    }

    inited() {
        this.initBrokerList();
    }

    async initBrokerList() {
        const {result} = await api.clusterNodes({
            clusterId: this.data.get('clusterId'),
            params: {
                pageNo: 1,
                pageSize: 10000
            }
        });

        const brokerMap = result.reduce(
            (res, item) => {
                if (!res[item.brokerId]) {
                    res[item.brokerId] = true;
                }
                return res;
            },
            {} as {[key in string]: boolean}
        );

        this.data.set(
            'brokerList',
            Object.keys(brokerMap).map(item => ({label: item, value: item}))
        );
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }

    async onConfirm() {
        try {
            this.data.set('confirming', true);
            await this.ref('form').validateFields();
            const {clusterId, formData, isEdit} = this.data.get('');
            await api[isEdit ? 'updateConsumerGroup' : 'createConsumerGroup']({
                clusterId,
                params: formData,
                ...(isEdit ? {groupName: formData.groupName} : {})
            });
            Notification.success(`${isEdit ? '编辑' : '创建'}消费组成功`);
            this.onClose();
            this.fire('success', {});
        } catch (error) {
        } finally {
            this.data.set('confirming', false);
        }
    }
}
