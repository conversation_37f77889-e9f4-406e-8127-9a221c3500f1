import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Alert, Table, Notification, Button} from '@baidu/sui';
import './index.less';
import {api} from '@common/client';
import {ConsumerListProps} from '..';
import {EllipsisTip} from '@components/index';
const klass = 'detail-consumer-delete-dialog';

export class DetailConsumerDeleteDialog extends Component {
    static template = html`
        <template>
            <s-dialog
                class="${klass}"
                title="删除消费组"
                width="{{800}}"
                open="{{true}}"
                on-confirm="onConfirm"
                on-close="onClose"
            >
                <s-alert skin="warning">
                    删除后数据将无法恢复，请谨慎操作，确认删除以下 {{datasource.length}} 个消费组吗？
                </s-alert>
                <s-table columns="{{columns}}" datasource="{{datasource}}" max-height="328">
                    <div class="table-ellipsis" slot="c-groupName">
                        <ellipsis-tip text="{{row.groupName}}" placement="top"> {{row.groupName}} </ellipsis-tip>
                    </div>
                    <div class="table-ellipsis" slot="c-brokerNames">
                        <ellipsis-tip text="{{row.brokerNames}}" placement="top"> {{row.brokerNames}} </ellipsis-tip>
                    </div>
                </s-table>
                <div slot="footer">
                    <s-button on-click="onClose">取消</s-button>
                    <s-button skin="primary" on-click="onConfirm">确定</s-button>
                </div>
            </s-dialog>
        </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-alert': Alert,
        's-table': Table,
        's-button': Button,
        'ellipsis-tip': EllipsisTip
    };

    initData() {
        return {
            datasource: []
        };
    }

    async onConfirm() {
        try {
            const {clusterId, datasource}: {clusterId: string; datasource: ConsumerListProps[]} = this.data.get('');
            await api.deleteConsumerGroups({clusterId, groupNames: datasource.map((item) => item.groupName)});
            Notification.success('删除消费组成功');
        } catch (error) {
        } finally {
            // ! 暂时先在单个删除和批量删除后，均直接跳到列表页面并刷新
            this.fire('success', {});
            this.onClose();
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
