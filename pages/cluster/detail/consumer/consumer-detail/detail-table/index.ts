import {html} from '@baiducloud/runtime';
import {BlockBox, CommonTable, EllipsisTip} from '@components/index';
import {Tabs, Table, Pagination} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz';
import {TABLE_SUI, PAGER_SUI} from '@common/config';
import {
    formatTime,
    formatTopicPermission,
    pickEmpty,
    updateUrlQuery,
    formatTopicPerm,
    isAuthenticationModeNone
} from '@common/utils';
import {api} from '@common/client';
import {ConsumerDetailExpTable} from './exp-table';

import {TopicListItem, QueueListItem, AclUserListView} from './index.d';
import {ClusterConsumerMessageModelType} from '@common/enums';
import {ConsumerListProps} from '../../consumer-list';
import './index.less';
import {IClusterDetail} from '@pages/cluster/detail/index.d';
enum TableTab {
    Client = 'client',
    Topic = 'topic',
    Perm = 'permission',
    Subscription = 'subscription'
}

const Table_Tab_List = [
    {label: '消费者列表', value: TableTab.Client, apiName: 'getConsumerGroupClients'},
    {label: '订阅主题', value: TableTab.Topic, apiName: 'getConsumerGroupTopics'},
    {label: '订阅关系', value: TableTab.Subscription, apiName: 'getConsumerGroupSubscriptions'}
];

const columnsMap = {
    [TableTab.Client]: [
        {name: 'clientId', label: '客户端ID'},
        {name: 'clientAddr', label: '客户端地址'},
        {name: 'language', label: '客户端语言'},
        {name: 'version', label: '客户端版本'}
    ],
    [TableTab.Topic]: [
        {name: 'topicName', label: '主题名称', width: '25%'},
        {name: 'brokerNames', label: '关联节点组', width: '30%'},
        {
            name: 'permission',
            label: '权限',
            width: '15%',
            render: (item: TopicListItem) => formatTopicPermission(item.permission)
        },
        {name: 'readQueueSum', label: '读队列总数', width: '10%'},
        {name: 'writeQueueSum', label: '写队列总数', width: '10%'}
    ],
    [TableTab.Perm]: [
        {name: 'accessKey', label: '用户名'},
        {
            name: 'whiteRemoteAddress',
            label: 'IP白名单'
        },
        {name: 'admin', label: '是否为管理员', render: (item: AclUserListView) => (item.admin ? '是' : '否')},
        {
            name: 'finalPerm',
            label: '权限',
            render: (item: AclUserListView) => formatTopicPerm(item.finalPerm)
        }
    ],
    [TableTab.Subscription]: [
        {name: 'topicName', label: '订阅主题', width: '30%'},
        {name: 'subExpression', label: '订阅规则', width: '70%'}
    ],
    expColumns: [
        {name: 'brokerName', label: '关联节点组', filter: {options: [], value: ''}},
        {name: 'queueId', label: '读队列ID'},
        {name: 'brokerOffset', label: '队列消息总数'},
        {name: 'consumerOffset', label: '已消费消息数'},
        {name: 'diffTotal', label: '未消费消息数'},
        {
            name: 'lastConsumeTime',
            label: '最后消费时间',
            render: (item: QueueListItem) => formatTime(item.lastConsumeTime)
        }
    ]
};
const klass = 'consumer-detail-table';
export class ConsumerDetailTable extends CommonTable {
    static template = html`
        <template>
            <s-tabs active="{= activeTab =}" on-change="onTabChange" class="${klass}">
                <s-tabpane
                    s-for="i in tabList"
                    label="{{i.label}}"
                    key="{{i.value}}"
                    disabled="{{baseDetail.messageModel === ClusterConsumerMessageModelType.BROADCASTING && i.value === TableTab.Topic}}"
                />
            </s-tabs>
            <s-table
                class="mt16"
                columns="{{table.columnsMap[activeTab]}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                has-Expand-Row="{{activeTab === TableTab.Topic}}"
                datasource="{{table.datasource}}"
                on-selected-change="onSelectChange"
                on-exprow-expand="onRowExpand"
                class="${klass}"
            >
                <div slot="empty">
                    <s-empty emptyTitle="暂无数据" emptyText="" actionText="" />
                </div>
                <exp-table
                    slot="sub-table"
                    topicName="{{row.topicName}}"
                    route="{{route}}"
                    groupName="{{groupName}}"
                    brokerList="{{brokerList}}"
                    columns="{{table.columnsMap.expColumns}}"
                    brokerNames="{{brokerNames}}"
                />
                <div class="table-ellipsis" slot="c-brokerNames">
                    <ellipsis-tip text="{{row.brokerNames}}" placement="top"> {{row.brokerNames}} </ellipsis-tip>
                </div>
                <div class="table-ellipsis" slot="c-whiteRemoteAddress">
                    <ellipsis-tip
                        s-if="row.whiteRemoteAddress.length"
                        text="{{row.whiteRemoteAddress | filterWhiteRemoteAddress}}"
                        placement="top"
                    />
                    <div s-else>-</div>
                </div>
                <div class="table-ellipsis" slot="c-subExpression">
                    <ellipsis-tip text="{{row.subExpression}}" placement="top"> {{row.subExpression}} </ellipsis-tip>
                </div>
            </s-table>
            <s-pagination
                class="mt16"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="total, pageSize, pager"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </template>
    `;

    static components = {
        'block-box': BlockBox,
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        's-table': Table,
        's-pagination': Pagination,
        's-empty': Empty,
        'ellipsis-tip': EllipsisTip,
        'exp-table': ConsumerDetailExpTable
    };

    static computed = {
        tabList() {
            let list = [...Table_Tab_List];
            const detail = this.data.get('detail') as IClusterDetail;
            if (detail && !isAuthenticationModeNone(detail.authenticationModes)) {
                list.splice(2, 0, {
                    label: '授权信息',
                    value: TableTab.Perm,
                    apiName: 'getConsumerUsers'
                });
            }

            return list;
        }
    };

    initData() {
        return {
            activeTab: TableTab.Client,
            table: {
                ...TABLE_SUI,
                columnsMap,
                exprowLoading: true
            },
            pager: {...PAGER_SUI},
            TableTab,
            ClusterConsumerMessageModelType
        };
    }

    inited() {
        // 初始化activeTab
        const {route} = this.data.get('');
        const {activeTab} = route.query;
        activeTab && this.data.set('activeTab', activeTab);
        activeTab === TableTab.Topic && this.initBrokerList();
    }

    attached(): void {
        this.getComList();
        this.watch('baseDetail', (value: ConsumerListProps) => {
            value.messageModel === ClusterConsumerMessageModelType.BROADCASTING &&
                this.data.get('activeTab') === TableTab.Topic &&
                this.onTabChange({value: {key: TableTab.Client}});
        });
    }

    async getTableList() {
        const {pager, route, activeTab, groupName} = this.data.get('');
        const {clusterId} = route.query;
        let param = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize
        });
        const tabList = this.data.get('tabList') as typeof Table_Tab_List;
        const currentApiName = tabList.find(item => item.value === activeTab)!.apiName;
        const {totalCount, result} = await api[currentApiName]({
            params: param,
            groupName,
            clusterId
        });
        this.data.set('pager.count', totalCount);
        this.data.set(
            'table.datasource',
            activeTab === TableTab.Topic ? result.map((item: any) => ({...item, subSlot: 'sub-table'})) : result
        );
    }

    onTabChange({value: {key: activeTab}}: {value: {key: TableTab}}) {
        // 更新当前选中tab到url
        updateUrlQuery({activeTab});

        this.data.set('table.datasource', []);
        this.data.set('activeTab', activeTab);
        this.onPageChange({value: {page: 1, pageSize: 10}});
        activeTab === TableTab.Topic && this.initBrokerList();
    }

    async initBrokerList() {
        const {route} = this.data.get('');
        const {clusterId} = route.query;
        const {result} = await api.clusterNodes({
            clusterId,
            params: {
                pageNo: 1,
                pageSize: 10000
            }
        });

        const brokerMap = result.reduce(
            (res, item) => {
                if (!res[item.brokerId]) {
                    res[item.brokerId] = true;
                }
                return res;
            },
            {} as {[key in string]: boolean}
        );

        this.data.set(
            'brokerList',
            Object.keys(brokerMap).map(item => ({text: item, value: item}))
        );
        this.data.set('brokerNames', Object.keys(brokerMap));
    }
}
