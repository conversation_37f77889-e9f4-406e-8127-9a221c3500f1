import {html} from '@baiducloud/runtime';
import {BlockBox, CommonTable} from '@components/index';
import {Table, Pagination} from '@baidu/sui';
import {TABLE_SUI, PAGER_SUI, PAGINATION_LAYOUT} from '@common/config';
import {pickEmpty} from '@common/utils';
import {api} from '@common/client';

import './index.less';
import {MultiFilter} from '@components/multi-filter';

const klass = 'consumer-detail-exp-table';

export class ConsumerDetailExpTable extends CommonTable {
    static template = html`
        <div class="${klass}">
            <s-table
                columns="{{columns || table.columns}}"
                loading="{{table.loading}}"
                datasource="{{table.datasource}}"
                error="{{table.error}}"
            >
                <span slot="h-brokerName-filter">
                    <multi-filter options="{{brokerList}}" value="{= brokerNames =}" on-change="onStatusChange" />
                </span>
                <div slot="empty">
                    <s-empty emptyTitle="暂无数据" emptyText="" actionText="" />
                </div>
            </s-table>
            <s-pagination
                class="mt16"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="total, pager"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        's-table': Table,
        's-pagination': Pagination,
        'multi-filter': MultiFilter,
    };

    initData() {
        return {
            table: {
                ...TABLE_SUI,
            },
            pager: {...PAGER_SUI},
            brokerNames: [],
        };
    }

    inited() {
        this.getComList();
    }

    async getTableList() {
        const {pager, route, groupName, topicName, brokerNames} = this.data.get('');
        const {clusterId} = route.query;
        let param = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            brokerNames,
        });
        const {totalCount, result} = await api.getConsumerGroupQueues({
            clusterId,
            groupName,
            topicName,
            params: param,
        });
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', result);
    }

    onStatusChange({value}: {value: string[]}) {
        this.data.set('brokerNames', value);
        this.onPageChange({value: {page: 1, pageSize: this.data.get('pager.pageSize')}});
    }
}
