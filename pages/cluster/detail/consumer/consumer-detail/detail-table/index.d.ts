import {IPermissionType} from '@common/enums';

export interface TopicListItem {
    topicName: string;
    brokerNames: string[];
    permission: string;
    readQueueSum: string;
    writeQueueSum: string;
}

export interface QueueListItem {
    /**
     * 关联节点组
     */
    brokerName: string;
    /**
     * 队列消息总数
     */
    brokerOffset: number;
    /**
     * 客户端ID
     */
    clientId: string;
    /**
     * 已消费消息数
     */
    consumerOffset: number;
    /**
     * 未消费消息数
     */
    diffTotal: number;
    /**
     * 消费组名称
     */
    groupName: string;
    /**
     * 最后消费时间
     */
    lastConsumeTime: string;
    /**
     * 读队列ID
     */
    queueId: number;
}

/**
 * AclUserListView
 */
export interface AclUserListView {
    /**
     * 用户名，用户名
     */
    accessKey: string;
    /**
     * 是否为管理员，是否为管理员
     */
    admin: boolean;
    /**
     * 默认消费组权限，默认消费组权限
     */
    defaultGroupPerm: IPermissionType;
    /**
     * 默认主题权限，默认主题权限
     */
    defaultTopicPerm: IPermissionType;
    /**
     * 当前主题消费组权限
     */
    finalPerm: IPermissionType;
    /**
     * IP白名单列表，IP白名单列表
     */
    whiteRemoteAddress: string[];
    [property: string]: any;
}
