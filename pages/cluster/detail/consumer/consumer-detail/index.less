.m-detail-consumer-detail {
    .detail-loading {
        width: 100%;
    }

    &-title {
        display: flex;
        justify-content: space-between;

        &-operation {
            .op-btn {
                width: 48px;
            }
        }
    }

    .detail-cell {
        padding-right: 16px;

        .c-ellipsis-tip {
            height: 20px;

            .text-ellipsis-tip {
                display: inline-flex;
            }
        }

        label {
            width: 88px;
            flex-shrink: 0;
        }
    }

    .s-pagination-wrapper {
        justify-content: flex-end;
    }

    .table-ellipsis {
        height: 20px;
    }

    .text-ellipsis-tip {
        display: inline-flex;
    }
}
