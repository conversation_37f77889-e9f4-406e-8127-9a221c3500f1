import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Breadcrumb, Loading, Button} from '@baidu/sui';
import {AppDetailCell} from '@baidu/sui-biz';
import {BlockBox, EllipsisTip} from '@components/index';
import {api} from '@common/client';
import {ConsumerListProps} from '../consumer-list';
import {ClusterConsumerMessageModel} from '@common/enums';
import {ConsumerDetailTable} from './detail-table';
import {DetailConsumerCreateDialog} from '../consumer-list/create-comsumer-dialog';
import {DetailConsumerDeleteDialog} from '../consumer-list/delete-dialog';
import {columns as listColumns} from '../consumer-list';
import './index.less';
import {updateUrlQuery, isClusterStatusDisabled} from '@common/utils';

const klass = 'm-detail-consumer-detail';

export class DetailConsumerDetail extends Component {
    static template = html`
        <div class="${klass}">
            <div class="${klass}-title">
                <s-breadcrumb class="mb16">
                    <s-breadcrumb-item>
                        <a class="label" on-click="onExitClick">消费组管理</a>
                        <span slot="separator" class="s-breadcrumbitem-separator">></span>
                    </s-breadcrumb-item>
                    <s-breadcrumb-item>
                        <span class="label s-breadcrumbitem-current">{{groupName}}</span>
                        <span slot="separator" class="s-breadcrumbitem-separator"></span>
                    </s-breadcrumb-item>
                </s-breadcrumb>
                <div class="${klass}-title-operation">
                    <s-button class="op-btn mr8" on-click="onEdit" disabled="{{operationDisabled}}">编辑</s-button>
                    <s-button class="op-btn mr8" on-click="onDelete" disabled="{{operationDisabled}}">删除</s-button>
                    <s-button class="op-btn" on-click="onRefresh">刷新</s-button>
                </div>
            </div>
            <block-box title="基本信息">
                <s-loading loading="{{loading}}" class="detail-loading">
                    <biz-detail-cell datasource="{{datasource}}">
                        <div slot="c-groupName" class="c-ellipsis-tip">
                            <ellipsis-tip text="{{baseDetail.groupName}}" placement="top">
                                {{baseDetail.groupName}}
                            </ellipsis-tip>
                        </div>
                        <div slot="c-brokerNames" class="c-ellipsis-tip">
                            <ellipsis-tip text="{{baseDetail.brokerNames}}" placement="top">
                                {{baseDetail.brokerNames}}
                            </ellipsis-tip>
                        </div>
                    </biz-detail-cell>
                </s-loading>
                <consumer-detail-table
                    s-ref="consumer-detail-table"
                    detail="{{detail}}"
                    route="{{route}}"
                    groupName="{{groupName}}"
                    baseDetail="{{baseDetail}}"
                />
            </block-box>
        </div>
    `;

    static components = {
        's-breadcrumb': Breadcrumb,
        's-breadcrumb-item': Breadcrumb.BreadcrumbItem,
        'block-box': BlockBox,
        'ellipsis-tip': EllipsisTip,
        'biz-detail-cell': AppDetailCell,
        's-loading': Loading,
        'consumer-detail-table': ConsumerDetailTable,
        's-button': Button
    };

    static computed: SanComputedProps = {
        datasource() {
            const detail: ConsumerListProps = this.data.get('baseDetail');
            return [
                {label: '消费组名称：', slot: 'groupName'},
                {label: '关联节点组：', slot: 'brokerNames'},
                {label: '最大重试次数：', value: detail.retryMaxTimes},
                {label: '广播模式消费：', value: detail.consumeBroadcastEnable ? '允许' : '禁止'},
                {label: '消费模式：', value: ClusterConsumerMessageModel.getTextFromValue(detail.messageModel)}
            ];
        },
        operationDisabled() {
            const detail = this.data.get('detail');
            return isClusterStatusDisabled(detail);
        }
    };

    initData() {
        return {
            breadcrumbRoutes: [],
            baseDetail: {
                groupName: '',
                brokerNames: [],
                retryMaxTimes: 0,
                consumeBroadcastEnable: false,
                messageModel: ClusterConsumerMessageModel.UNKNOW
            },
            loading: true
        };
    }

    inited() {
        this.initDetailData();
    }

    async initDetailData() {
        const {
            route: {
                query: {clusterId}
            },
            groupName
        } = this.data.get('');
        this.data.set('loading', true);
        const result: ConsumerListProps = await api.getConsumerGroupDetail({
            clusterId,
            groupName
        });

        this.data.set('baseDetail', result);
        this.data.set('loading', false);
    }

    onExitClick() {
        const {
            route: {
                query: {clusterId}
            }
        } = this.data.get('');
        this.data.set('groupName', '');
        updateUrlQuery({clusterId}, true);
    }

    onEdit() {
        const {
            route: {
                query: {clusterId}
            },
            baseDetail: row
        } = this.data.get('');

        const dialog = new DetailConsumerCreateDialog({
            data: {
                clusterId,
                formData: {
                    groupName: row.groupName,
                    brokerNames: [...row.brokerNames],
                    consumeBroadcastEnable: row.consumeBroadcastEnable,
                    retryMaxTimes: row.retryMaxTimes
                },
                isEdit: true
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onRefresh();
        });
    }

    onDelete() {
        const {
            route: {
                query: {clusterId}
            },
            baseDetail: row
        } = this.data.get('');

        const dialog = new DetailConsumerDeleteDialog({
            data: {
                datasource: [row],
                columns: listColumns.slice(0, -1),
                clusterId
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onExitClick();
        });
    }

    onRefresh() {
        this.initDetailData();
        this.ref('consumer-detail-table') && this.ref('consumer-detail-table').getComList();
    }
}
