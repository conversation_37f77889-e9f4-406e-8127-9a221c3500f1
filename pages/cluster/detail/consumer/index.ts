import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {DetailConsumerList} from './consumer-list';
import {DetailConsumerDetail} from './consumer-detail';

const klass = 'm-detail-consumer';

const template = html`
    <template>
        <consumer-list s-if="!groupName" detail="{{detail}}" route="{{route}}" groupName="{=groupName=}" />
        <consumer-detail s-else groupName="{=groupName=}" detail="{{detail}}" route="{{route}}" />
    </template>
`;

export class DetailConsumer extends Component {
    static template = template;

    static components = {
        'consumer-list': DetailConsumerList,
        'consumer-detail': DetailConsumerDetail,
    };

    inited() {
        const {
            query: {groupName},
        } = this.data.get('route');

        this.data.set('groupName', groupName);
    }
}
