/**
 * 集群详情侧边栏
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html, redirect} from '@baiducloud/runtime';
import {AppSidebar} from '@baidu/sui-biz';
import {ROUTE_PATH} from '@common/config';
import './index.less';
import {IClusterDetail} from '../index.d';
import {isAuthenticationModeNone} from '@common/utils';
const klass = 'page-cluster-detail-sidebar';

export class DetailSidebar extends Component {
    static template = html`
        <div class="${klass}">
            <app-sidebar activeName="{{active}}">
                <app-sidebar-item
                    title="集群详情"
                    name="${ROUTE_PATH.clusterDetail}"
                    on-click="onClick(ROUTE_PATH.clusterDetail)"
                />
                <app-sidebar-item
                    title="主题管理"
                    name="${ROUTE_PATH.clusterTopic}"
                    on-click="onClick(ROUTE_PATH.clusterTopic)"
                />
                <app-sidebar-item
                    title="消费组管理"
                    name="${ROUTE_PATH.clusterConsumer}"
                    on-click="onClick(ROUTE_PATH.clusterConsumer)"
                />
                <app-sidebar-item
                    visible="{{userAclVisible}}"
                    title="用户管理"
                    name="${ROUTE_PATH.clusterUserAcl}"
                    on-click="onClick(ROUTE_PATH.clusterUserAcl)"
                />
                <app-sidebar-item
                    title="任务管理"
                    name="${ROUTE_PATH.clusterActionList}"
                    on-click="onClick(ROUTE_PATH.clusterActionList)"
                />
                <app-sidebar-item
                    title="消息查询"
                    name="${ROUTE_PATH.clusterMessageList}"
                    on-click="onClick(ROUTE_PATH.clusterMessageList)"
                />
                <app-sidebar-item
                    title="集群监控"
                    name="${ROUTE_PATH.clusterMonitor}"
                    on-click="onClick(ROUTE_PATH.clusterMonitor)"
                />
                <app-sidebar-item
                    s-if="whiteListMap.ROCKETMQ_ClusterLogViewer"
                    title="集群日志"
                    name="${ROUTE_PATH.clusterLog}"
                    on-click="onClick(ROUTE_PATH.clusterLog)"
                />
            </app-sidebar>
        </div>
    `;

    static components = {
        'app-sidebar': AppSidebar,
        'app-sidebar-item': AppSidebar.Item
    };

    static computed: SanComputedProps = {
        prefix(): string {
            return 'clusterId=' + this.data.get('clusterId');
        },
        userAclVisible() {
            const detail = this.data.get('detail') as IClusterDetail;
            if (!detail) {
                return false;
            }
            return !isAuthenticationModeNone(detail.authenticationModes);
        }
    };

    initData() {
        return {
            ROUTE_PATH
        };
    }

    inited() {
        this.watch('active', name => this.fire('active', {name}));
    }

    onClick(path: string) {
        const prefix = this.data.get('prefix');
        redirect(`#${path}?${prefix}`, {silent: true});
        this.fire('change', path);
    }
}
