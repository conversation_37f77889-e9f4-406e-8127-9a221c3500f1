import {Component, defineComponent} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Input, Form, Notification} from '@baidu/sui';
import {api} from '@common/client';
import {pickEmpty} from '@common/utils';
import {ClipBoard} from '@baidu/sui-biz';
const klass = 'message-send';
class MessageSend extends Component {
    static template = html` <template>
        <s-dialog
            open="{{open}}"
            class="${klass}"
            title="消息发送"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}"
            on-confirm="onConfirm"
        >
            <s-form
                s-ref="form"
                label-align="right"
                class="form-format"
                rules="{{rules}}"
                data="{= formData =}"
                isRequired="{{require}}"
            >
                <s-form-item label="消息内容：" prop="body">
                    <s-textarea width="300" height="100" value="{=formData.body=}" maxLength="2000" />
                </s-form-item>
                <s-form-item label="消息key：" prop="key">
                    <s-input width="300" value="{=formData.key=}" />
                </s-form-item>
                <s-form-item label="消息tag：" prop="tag">
                    <s-input width="300" value="{=formData.tag=}" />
                </s-form-item>
            </s-form>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-textarea': Input.TextArea
    };

    initData() {
        return {
            formData: {
                key: '',
                body: '',
                id: ''
            },
            rules: {
                body: [{required: true, message: '请输入消息内容'}]
            },
            open: true,
            confirming: false,
            require: true
        };
    }

    async onConfirm() {
        try {
            const {topicName, clusterId, formData} = this.data.get('');
            await this.ref('form')?.validateFields();
            let params = pickEmpty({
                ...formData
            });
            params = {
                ...params,
                topicName
            };
            this.data.set('confirming', true);
            const result = await api.messageSend({clusterId, topicName, params});
            const {msgId} = result;
            const tip = defineComponent({
                template: `
                <template>
                    <span>消息已发送成功，消息ID：${msgId}<s-clip-board text="${msgId}"/></span>
                </template>
                `,
                components: {
                    's-clip-board': ClipBoard
                }
            });
            Notification.success(tip, {
                duration: 120
            });
            this.data.set('confirming', false);
            this.onClose();
        } catch (error) {
            Notification.error('消息发送失败');
            this.data.set('confirming', false);
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}

export default MessageSend;
