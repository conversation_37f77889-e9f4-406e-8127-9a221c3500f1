/**
 * 创建主题弹窗
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {OutlinedQuestionCircle} from '@baidu/sui-icon';
import {Dialog, Form, Table, Radio, Select, Input, InputNumber, Button, Notification, Tooltip} from '@baidu/sui';
import {api} from '@common/client';
import './index.less';
import {VALIDATE_ITEMS} from '@common/rules';
import {CreateBtn} from '@components/index';
import {Broker, BrokerList, FromDataType, AssociatedNodeGroupType} from '../../index.d';
import {InfoCircle2} from '@baidu/xicon-san';

const topicNameRegTip = '支持字母a~z或A~Z、数字0~9以及下划线（_）、短划线（-）和百分号（%）';
const klass = 'detail-topic-create-dialog';

const baseColumns = [
    {
        name: 'brokerName',
        label: '节点组',
        width: 140
    },
    {
        name: 'readQueueNum',
        label: '读队列数量',
        width: 70
    },
    {
        name: 'writeQueueNum',
        label: '写队列数量',
        width: 70
    }
];
const operationColumn = {
    name: 'operation',
    label: '操作',
    width: 60,
    fixed: 'right'
};
export class DetailTopicCreateDialog extends Component {
    static template = html`
        <template>
            <s-dialog
                title="{{isEdit ? '编辑' : '创建'}}主题"
                open="{{open}}"
                confirming="{{confirming}}"
                class="${klass}"
                width="{{700}}"
                on-confirm="onConfirm"
                on-close="onClose"
            >
                <s-form s-ref="form" data="{= formData =}" rules="{{rules}}" label-align="left" width="{{800}}">
                    <!-- Topic Name -->
                    <s-form-item label="主题名称：" prop="topicName" help="{{isEdit?'':topicNameRegTip}}">
                        <span s-if="isEdit">{{formData.topicName}}</span>
                        <s-input s-else value="{=formData.topicName=}" width="400" />
                    </s-form-item>

                    <!-- Permission -->
                    <s-form-item label="权限：" prop="permission">
                        <s-radio-group
                            radioType="button"
                            datasource="{{radioGroup.permission}}"
                            value="{=formData.permission=}"
                            enhanced
                        />
                    </s-form-item>

                    <!-- Associated Node Group -->
                    <s-form-item label="关联节点组：" prop="associatedNodeGroupType">
                        <s-radio-group
                            radioType="button"
                            datasource="{{radioGroup.associatedNodeGroup}}"
                            value="{=formData.associatedNodeGroupType=}"
                            enhanced
                            class="associated-node-group-type"
                        />
                        <s-table columns="{{dynamicColumns}}" datasource="{{currentAssociatedNodeGroup.brokersData}}">
                            <div slot="h-brokerName">
                                <span>节点组</span>
                                <s-tooltip content="节点组不⽀持重复添加，且上限为集群节点组数量上限">
                                    <s-icon-info class="pick-icon ml8" />
                                </s-tooltip>
                            </div>
                            <div slot="c-brokerName">
                                <span s-if="{{row.disabled}}">{{row.brokerName}}</span>
                                <s-select
                                    s-else
                                    slot="c-brokerName"
                                    taggable="{{isGlobal}}"
                                    multiple="{{isGlobal}}"
                                    value="{=row.brokerName=}"
                                    datasource="{=currentAssociatedNodeGroup.options=}"
                                    width="{{200}}"
                                    on-change="onBrokerNameChange($event,rowIndex)"
                                    getPopupContainer="{{getPopupContainer}}"
                                >
                                </s-select>
                            </div>
                            <s-input-number
                                slot="c-readQueueNum"
                                width="{{50}}"
                                min="{{1}}"
                                max="{{50}}"
                                value="{=row.readQueueNum=}"
                                on-change="onQueueNumChange($event, rowIndex, 'readQueueNum')"
                            />
                            <s-input-number
                                slot="c-writeQueueNum"
                                width="{{50}}"
                                min="{{1}}"
                                max="{{50}}"
                                value="{=row.writeQueueNum=}"
                                on-change="onQueueNumChange($event, rowIndex, 'writeQueueNum')"
                            />
                            <s-tooltip slot="c-operation" content="{{customBrokersNumLimit?'⾄少关联⼀个节点组':''}}">
                                <s-button
                                    skin="stringfy"
                                    on-click="onDelete(row)"
                                    disabled="{{customBrokersNumLimit || row.disabled}}"
                                >
                                    删除
                                </s-button>
                            </s-tooltip>
                        </s-table>
                        <span s-if="{{noBrokerName && isGlobal}}" class="no-broker-name">请选择节点组</span>

                        <!-- Add Node Group -->
                        <create-btn
                            s-if="{{isCustom}}"
                            skin="stringfy"
                            on-click="onCreate"
                            disabled="{{customBrokersMaxedOut}}"
                        >
                            关联节点组
                        </create-btn>
                    </s-form-item>
                </s-form>
                <div slot="footer">
                    <s-button on-click="onClose">取消</s-button>
                    <s-button skin="primary" on-click="onConfirm">确定</s-button>
                </div>
            </s-dialog>
        </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-question': OutlinedQuestionCircle,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-table': Table,
        's-select': Select,
        's-option': Select.Option,
        's-input': Input,
        's-input-number': InputNumber,
        's-button': Button,
        's-tooltip': Tooltip,
        's-icon-info': InfoCircle2,
        'create-btn': CreateBtn
    };

    static computed = {
        dynamicColumns(): any {
            return this.data.get('isCustom') ? [...baseColumns, operationColumn] : baseColumns;
        },
        currentAssociatedNodeGroup(): Broker[] {
            const type = this.data.get('formData.associatedNodeGroupType');
            const currentAssociatedNodeGroup = this.data.get(`formData.${type}`);
            return currentAssociatedNodeGroup;
        },
        // 判断是否为 global 类型
        isGlobal(): boolean {
            return this.data.get('formData.associatedNodeGroupType') === AssociatedNodeGroupType.GLOBAL;
        },

        // 判断是否为 custom 类型
        isCustom(): boolean {
            return this.data.get('formData.associatedNodeGroupType') === AssociatedNodeGroupType.CUSTOM;
        },
        // 判断 custom.brokersData 长度是否小于 2
        customBrokersNumLimit(): boolean {
            const brokersData = this.data.get('formData.custom.brokersData');
            return brokersData && brokersData.length < 2;
        },

        // 判断 custom.brokersData 是否达到 options 的最大长度
        customBrokersMaxedOut(): boolean {
            const brokersData = this.data.get('formData.custom.brokersData');
            const options = this.data.get('formData.custom.options');
            return brokersData && options && brokersData.length >= options.length;
        }
    };

    initData() {
        return {
            getPopupContainer: () => document.body,
            isEdit: false,
            open: true,
            confirming: false,
            topicNameRegTip,
            formData: {
                topicName: '',
                permission: 6,
                associatedNodeGroupType: AssociatedNodeGroupType.GLOBAL
            },
            rules: {
                topicName: [
                    {
                        required: true,
                        message: `请输入主题名称，${topicNameRegTip}`
                    },
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (value.length > 64) {
                                return callback('不能超过64个字符');
                            }
                            if (!/^[0-9a-zA-Z_\w-%]{1,64}$/.test(value)) {
                                return callback(`输入格式有误。${topicNameRegTip}`);
                            }
                            callback();
                        }
                    }
                ],
                permission: [VALIDATE_ITEMS.requiredSelect],
                associatedNodeGroupType: [VALIDATE_ITEMS.requiredSelect]
            },
            radioGroup: {
                permission: [
                    {label: '发布+订阅', value: 6},
                    {label: '发布', value: 2},
                    {label: '订阅', value: 4}
                ],
                associatedNodeGroup: [
                    {label: '全局设置', value: AssociatedNodeGroupType.GLOBAL},
                    {label: '自定义', value: AssociatedNodeGroupType.CUSTOM}
                ]
            },
            noBrokerName: false
        };
    }

    inited() {
        this.initFormData();
    }
    initFormData() {
        const {isEdit, brokerList} = this.data.get('');

        if (isEdit) {
            const {brokers, topicName, permission} = this.data.get('datasource');
            const brokerName = brokers.map((item: Broker) => item.brokerName);
            const options = brokerList?.map((item: BrokerList) => ({
                ...item,
                disabled: brokerName?.includes(item.label)
            }));

            const {readQueueNum, writeQueueNum} = brokers[0];
            const associatedNodeGroupType = brokers.some(
                (item: Broker) => item.readQueueNum !== readQueueNum || item.writeQueueNum !== writeQueueNum
            )
                ? AssociatedNodeGroupType.CUSTOM
                : AssociatedNodeGroupType.GLOBAL;

            this.data.set('formData', {
                topicName,
                permission,
                associatedNodeGroupType,
                global: {
                    options: [...options],
                    brokersData: [
                        {
                            brokerName,
                            readQueueNum: associatedNodeGroupType === AssociatedNodeGroupType.GLOBAL ? readQueueNum : 1,
                            writeQueueNum:
                                associatedNodeGroupType === AssociatedNodeGroupType.GLOBAL ? writeQueueNum : 1
                        }
                    ]
                },
                custom: {
                    options: [...options],
                    brokersData: brokers.map((item: Broker) => ({...item, disabled: true}))
                }
            });
        } else {
            this.data.set('formData', {
                topicName: '',
                permission: 6,
                associatedNodeGroupType: AssociatedNodeGroupType.GLOBAL,
                ...([AssociatedNodeGroupType.GLOBAL, AssociatedNodeGroupType.CUSTOM] as const).reduce(
                    (acc, type) => {
                        acc[type] = {
                            brokersData: [
                                {
                                    brokerName:
                                        type === AssociatedNodeGroupType.GLOBAL
                                            ? [brokerList[0].value]
                                            : brokerList[0].value,
                                    readQueueNum: 1,
                                    writeQueueNum: 1
                                }
                            ],
                            options:
                                type === AssociatedNodeGroupType.GLOBAL
                                    ? [...brokerList]
                                    : brokerList.map((item: BrokerList, index: number) =>
                                          index === 0 ? {...item, disabled: true} : {...item}
                                      )
                        };
                        return acc;
                    },
                    {} as Record<AssociatedNodeGroupType.GLOBAL | AssociatedNodeGroupType.CUSTOM, FromDataType>
                )
            });
        }
    }

    onBrokerNameChange(e: any, rowIndex: number) {
        const {
            isCustom,
            formData: {associatedNodeGroupType: type}
        } = this.data.get('');
        this.data.set(`formData.${type}.brokersData[${rowIndex}].brokerName`, e.value);
        if (isCustom) {
            const brokerNames = new Set(
                this.data.get('formData.custom.brokersData').map((item: Broker) => item.brokerName)
            );

            const updatedOptions = this.data.get('formData.custom.options').map((item: BrokerList) => ({
                ...item,
                disabled: brokerNames.has(item.value)
            }));

            this.data.set('formData.custom.options', updatedOptions);
        } else {
            this.data.set('noBrokerName', !e.value.length);
        }
    }

    onQueueNumChange(e: any, rowIndex: number, queueType: 'readQueueNum' | 'writeQueueNum') {
        const type = this.data.get('formData.associatedNodeGroupType');

        this.data.set(`formData.${type}.brokersData[${rowIndex}].${queueType}`, e.value);

        // 创建时，读写队列数需保持一致
        if (!this.data.get('isEdit')) {
            const oppositeType = queueType === 'readQueueNum' ? 'writeQueueNum' : 'readQueueNum';
            this.data.set(`formData.${type}.brokersData[${rowIndex}].${oppositeType}`, e.value);
        }
    }

    onDelete(row: any) {
        this.data.remove('formData.custom.brokersData', row);
        const updatedOptions = this.data.get('formData.custom.options').map((item: BrokerList) => {
            return {
                ...item,
                disabled: row.brokerName === item.value ? false : item.disabled
            };
        });
        this.data.set('formData.custom.options', updatedOptions);
    }

    onCreate() {
        let brokerName = '';
        let found = false;
        const updatedOptions = this.data.get('formData.custom.options').map((item: BrokerList) => {
            if (!found && !item.disabled) {
                brokerName = item.value;
                found = true;
                return {...item, disabled: true};
            }
            return {...item};
        });

        this.data.set('formData.custom.options', updatedOptions);
        this.data.push('formData.custom.brokersData', {
            brokerName,
            readQueueNum: 1,
            writeQueueNum: 1
        });
    }

    async onConfirm() {
        try {
            this.data.set('confirming', true);
            const form = this.ref('form');
            await form.validateFields();

            const {
                clusterId,
                isEdit,
                formData: {topicName, permission, global, custom},
                noBrokerName,
                isGlobal
            } = this.data.get('');
            if (noBrokerName && isGlobal) {
                return;
            }
            const params = {
                topicName,
                permission,
                brokers: this.getBrokers(isGlobal, global, custom)
            };

            await api[isEdit ? 'topicUpdate' : 'topicCreate']({clusterId, params, ...(isEdit ? {topicName} : {})});
            Notification.success(isEdit ? '编辑主题成功' : '创建主题成功');
            this.onClose();
            this.fire('success', {});
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('confirming', false);
        }
    }
    getBrokers(isGlobal: boolean, global: any, custom: any) {
        if (isGlobal) {
            // 扁平化 global.brokersData 的 brokerName 数据
            return global.brokersData.flatMap(({brokerName, readQueueNum, writeQueueNum}: Broker) =>
                (brokerName as string[]).map(name => ({
                    brokerName: name,
                    readQueueNum,
                    writeQueueNum
                }))
            );
        }
        custom.brokersData.forEach(item => {
            delete item.disabled;
        });
        return custom.brokersData;
    }

    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
