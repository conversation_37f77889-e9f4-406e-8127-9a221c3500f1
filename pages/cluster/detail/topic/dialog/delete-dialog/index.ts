/**
 * 删除主题弹窗
 * @file index.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Alert, Table, Notification, Button} from '@baidu/sui';
import {api} from '@common/client';
import {EllipsisTip} from '@components/index';
import {TopicListProps} from '../../index.d';
import './index.less';
const klass = 'detail-topic-delete-dialog';
const Columns = [
    {
        name: 'topicName',
        label: '主题名称',
        width: 200,
        fixed: 'left'
    },
    {
        name: 'brokerNames',
        label: '关联节点组',
        width: 140
    },
    {
        name: 'readQueueSum',
        label: '读队列总数',
        width: 100
    },
    {
        name: 'writeQueueSum',
        label: '写队列总数',
        width: 90
    }
];
export class DetailTopicDeleteDialog extends Component {
    static template = html`
        <template>
            <s-dialog
                class="${klass}"
                title="删除主题"
                width="{{800}}"
                open="{{true}}"
                on-confirm="onConfirm"
                on-close="onClose"
            >
                <s-alert skin="warning">
                    删除后数据将无法恢复，请谨慎操作，确认删除以下 {{datasource.length}} 个主题吗？
                </s-alert>
                <s-table columns="{{Columns}}" datasource="{{datasource}}" max-height="328">
                    <div slot="c-topicName" class="brokerNames">
                        <ellipsis-tip text="{{row.topicName}}" placement="top"> {{row.topicName}} </ellipsis-tip>
                    </div>
                    <div slot="c-brokerNames" class="brokerNames">
                        <ellipsis-tip text="{{row.brokerNames}}" placement="top"> {{row.brokerNames}} </ellipsis-tip>
                    </div>
                </s-table>
                <div slot="footer">
                    <s-button on-click="onClose">取消</s-button>
                    <s-button skin="primary" on-click="onConfirm">确定</s-button>
                </div>
            </s-dialog>
        </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-alert': Alert,
        's-table': Table,
        's-button': Button,
        'ellipsis-tip': EllipsisTip
    };

    initData() {
        return {
            datasource: [],
            Columns
        };
    }

    async onConfirm() {
        try {
            const {clusterId, datasource} = this.data.get('');
            const topicNames = datasource.map((item: TopicListProps) => item.topicName);
            await api.topicDelete({clusterId, topicNames});
            Notification.success('删除主题成功');
        } catch (error) {
        } finally {
            // ! 暂时先在单个删除和批量删除后，均直接跳到列表页面并刷新
            this.fire('success', {});
            this.onClose();
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
