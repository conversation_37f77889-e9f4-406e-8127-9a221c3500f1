/**
 * 主题管理
 * @file index.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {DetailTopicList} from './topic-list';
import {DetailTopicDetail} from './topic-detail';

const klass = 'm-detail-topic';

const template = html`
    <template>
        <topic-list s-if="!topicName" detail="{{detail}}" route="{{route}}" topicName="{=topicName=}" />
        <topic-detail s-else topicName="{=topicName=}" detail="{{detail}}" route="{{route}}" />
    </template>
`;

export class DetailTopic extends Component {
    static template = template;

    static components = {
        'topic-list': DetailTopicList,
        'topic-detail': DetailTopicDetail,
    };

    inited() {
        const {
            query: {topicName},
        } = this.data.get('route');

        this.data.set('topicName', topicName);
    }
}
