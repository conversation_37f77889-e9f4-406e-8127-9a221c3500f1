/**
 * 主题管理列表
 * @file index.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Pagination, Table, Button, Search} from '@baidu/sui';
import {AppListPage, Empty} from '@baidu/sui-biz';
import {api} from '@common/client';
import {TABLE_SUI, PAGER_SUI, PAGINATION_LAYOUT, SELECTION_SUI_MULTI} from '@common/config';
import {isClusterStatusDisabled, pickEmpty, updateUrlQuery} from '@common/utils';
import {ClusterTopicPermissionTypeList} from '@common/enums';
import {debounce} from '@common/decorators';
import {BlockBox, CommonTable, CreateBtn, EllipsisTip, RefreshBtn} from '@components/index';
import './index.less';
import {DetailTopicCreateDialog} from '../dialog/create-dialog';
import {DetailTopicDeleteDialog} from '../dialog/delete-dialog';
import {TopicListProps} from '../index.d';
import {IClusterDetail} from '../../index.d';
import MessageSend from '../dialog/message-dialog';
const klass = 'm-detail-topic-list';
const Columns = [
    {
        name: 'topicName',
        label: '主题名称',
        width: 140,
        fixed: 'left'
    },
    {
        name: 'brokerNames',
        label: '关联节点组',
        width: 140
    },
    {
        name: 'permission',
        label: '权限',
        width: 110,
        render: (item: TopicListProps) => ClusterTopicPermissionTypeList.getTextFromValue(item.permission)
    },
    {
        name: 'readQueueSum',
        label: '读队列总数',
        width: 100
    },
    {
        name: 'writeQueueSum',
        label: '写队列总数',
        width: 90
    },
    {
        name: 'operation',
        label: '操作',
        width: 140,
        fixed: 'right'
    }
];
const permissionArr = [2, 3, 6, 7];
export class DetailTopicList extends CommonTable {
    static template = html`
        <template>
            <block-box title="主题管理" class="${klass}">
                <app-list-page class="${klass}-content">
                    <div slot="bulk">
                        <create-btn on-click="onCreate" disabled="{{operationDisabled}}">创建主题</create-btn>
                        <s-button
                            s-if="{{!isOneCloud}}"
                            class="ml8"
                            disabled="{{operationDisabled || !selection.selectedIndex.length}}"
                            on-click="onDelete"
                        >
                            删除
                        </s-button>
                    </div>
                    <div slot="filter" class="table-filter">
                        <s-search
                            class="cluster-search-box"
                            placeholder="{{searchbox.placeholder}}"
                            value="{= searchbox.keyword =}"
                            on-search="onSearch"
                            clearable
                            width="170"
                        />
                        <refresh-btn on-click="onRefresh" class="ml4" />
                    </div>
                    <s-table
                        columns="{{table.Columns}}"
                        loading="{{table.loading}}"
                        error="{{table.error}}"
                        datasource="{{table.datasource}}"
                        selection="{{selection}}"
                        on-selected-change="onSelectChange"
                    >
                        <div slot="c-topicName" class="brokerNames">
                            <s-button skin="stringfy" class="table-btn-slim name-btn" on-click="onTopicNameClick(row)">
                                <ellipsis-tip text="{{row.topicName}}" placement="top">
                                    {{row.topicName}}
                                </ellipsis-tip>
                            </s-button>
                        </div>
                        <div slot="c-brokerNames" class="brokerNames">
                            <ellipsis-tip text="{{row.brokerNames}}" placement="top">
                                {{row.brokerNames}}
                            </ellipsis-tip>
                        </div>
                        <div slot="c-operation">
                            <s-button
                                skin="stringfy"
                                on-click="onMessageSend(row)"
                                disabled="{{row.messageDisabled||operationDisabled}}"
                            >
                                消息发送
                            </s-button>
                            <s-button skin="stringfy" on-click="onEdit(row)" disabled="{{operationDisabled}}">
                                编辑
                            </s-button>
                            <s-button skin="stringfy" on-click="onDelete($event,row)" disabled="{{operationDisabled}}">
                                删除
                            </s-button>
                        </div>
                        <div slot="empty">
                            <s-empty vertical emptyTitle="暂无数据" emptyText="{{emptyTip}}">
                                <create-btn
                                    slot="action"
                                    on-click="onCreate"
                                    skin="stringfy"
                                    disabled="{{operationDisabled}}"
                                >
                                    创建主题
                                </create-btn>
                            </s-empty>
                        </div>
                    </s-table>
                    <s-pagination
                        slot="pager"
                        s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                        layout="${PAGINATION_LAYOUT}"
                        total="{{pager.count}}"
                        pageSize="{{pager.pageSize}}"
                        page="{{pager.page}}"
                        pageSizes="{{pager.pageSizes}}"
                        max-item="{{7}}"
                        on-pagerChange="onPageChange"
                        on-pagerSizeChange="onPageSizeChange"
                    />
                </app-list-page>
            </block-box>
        </template>
    `;

    static components = {
        'block-box': BlockBox,
        'app-list-page': AppListPage,
        's-search': Search,
        's-table': Table,
        's-pagination': Pagination,
        's-button': Button,
        'ellipsis-tip': EllipsisTip,
        'create-btn': CreateBtn,
        'refresh-btn': RefreshBtn,
        's-empty': Empty
    };

    static computed = {
        operationDisabled() {
            const detail = this.data.get('detail') as IClusterDetail;
            return isClusterStatusDisabled(detail);
        }
    };

    static filters = {};

    initData() {
        return {
            searchbox: {
                placeholder: '请输⼊主题名称进⾏搜索'
            },
            table: {
                ...TABLE_SUI,
                Columns
            },
            selection: {
                ...SELECTION_SUI_MULTI
            },
            pager: {...PAGER_SUI}
        };
    }

    async attached() {
        this.getComList();
        this.getBrokerList();
    }

    async getTableList() {
        const {
            pager,
            route: {
                query: {clusterId}
            },
            searchbox: {keyword}
        } = this.data.get('');
        let params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            topicName: keyword
        });
        const {totalCount, result} = (await api.getTopicList({clusterId, params})) as ListPage<TopicListProps>;
        this.data.set('pager.count', totalCount);
        const datasource = result.map(item => {
            return {
                ...item,
                brokerNames: item.brokers.map(item => item.brokerName),
                messageDisabled: !permissionArr.includes(item.permission)
            };
        });
        this.data.set('table.datasource', datasource);
        this.data.set('emptyTip', keyword ? '暂⽆符合条件的主题，您可以尝试更换筛选条件。' : '您还没有创建任何主题');
    }
    onMessageSend(row: TopicListProps) {
        const {
            route: {
                query: {clusterId}
            }
        } = this.data.get('');
        const dialog = new MessageSend({
            data: {
                clusterId,
                topicName: row.topicName
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {});
    }

    async getBrokerList() {
        const {
            route: {
                query: {clusterId}
            }
        } = this.data.get('');
        const {result} = await api.clusterNodes({
            clusterId,
            params: {
                pageNo: 1,
                pageSize: 10000
            }
        });

        // 获取去重后的 brokerId 列表
        const brokerList = Array.from(new Set(result.map(item => item.brokerId))).map(brokerId => ({
            label: brokerId,
            value: brokerId
        }));

        this.data.set('brokerList', brokerList);
    }

    onCreate() {
        const {
            route: {
                query: {clusterId}
            },
            brokerList
        } = this.data.get('');

        const dialog = new DetailTopicCreateDialog({data: {clusterId, brokerList}});
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onOperateRefresh(true);
        });
    }
    onEdit(row: TopicListProps) {
        const {
            route: {
                query: {clusterId}
            },
            brokerList
        } = this.data.get('');
        const dialog = new DetailTopicCreateDialog({
            data: {
                isEdit: true,
                datasource: row,
                clusterId,
                brokerList
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onOperateRefresh();
        });
    }

    async onDelete(e?: any, row?: TopicListProps) {
        const {
            selection: {selectedIndex},
            table: {datasource},
            route: {
                query: {clusterId}
            }
        } = this.data.get('');

        const dialogDatasource = row
            ? [row]
            : datasource.filter((item: TopicListProps, index: number) => selectedIndex.includes(index));
        const dialog = new DetailTopicDeleteDialog({
            data: {
                clusterId,
                datasource: dialogDatasource
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onOperateRefresh(true);
        });
    }

    onSelectChange(event: {value: {selectedIndex: number[]; selectedItems: Array<Object>}}) {
        this.data.set('selection.selectedIndex', event.value.selectedIndex);
    }

    onTopicNameClick(row: TopicListProps) {
        updateUrlQuery({topicName: row.topicName});
        this.data.set('topicName', row.topicName);
    }

    // ! 主题后端有2s延迟 后端已经做了缓存刷新 但有偶发刷新异常 加入防抖1s
    @debounce(1000)
    onOperateRefresh(isInitRefresh: boolean = false) {
        if (isInitRefresh) {
            this.data.set('searchbox.keyword', '');
            this.onPageChange({value: {page: 1, pageSize: this.data.get('pager.pageSize')}});
        } else {
            this.onRefresh();
        }
    }
}
