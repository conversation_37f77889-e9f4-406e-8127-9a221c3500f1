/**
 * 主题详情
 * @file index.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Breadcrumb, Loading, Button} from '@baidu/sui';
import {AppDetailCell} from '@baidu/sui-biz';
import {BlockBox, EllipsisTip} from '@components/index';
import {api} from '@common/client';
import {ClusterTopicPermissionTypeList} from '@common/enums';
import {TopicListProps} from '../index.d';
import {TopicDetailTable} from './detail-table';
import {DetailTopicCreateDialog} from '../dialog/create-dialog';
import {DetailTopicDeleteDialog} from '../dialog/delete-dialog';
import {debounce} from '@common/decorators';
import {updateUrlQuery, isClusterStatusDisabled} from '@common/utils';
import {Broker} from '.././index.d';
import './index.less';

const klass = 'm-detail-topic-detail';
export class DetailTopicDetail extends Component {
    static template = html`
        <div class="${klass}">
            <div class="${klass}-title">
                <s-breadcrumb class="mb16">
                    <s-breadcrumb-item>
                        <a class="label" on-click="onExitClick">主题管理</a>
                        <span slot="separator" class="s-breadcrumbitem-separator">></span>
                    </s-breadcrumb-item>
                    <s-breadcrumb-item>
                        <span class="label s-breadcrumbitem-current">{{topicName}}</span>
                        <span slot="separator" class="s-breadcrumbitem-separator"></span>
                    </s-breadcrumb-item>
                </s-breadcrumb>
                <div class="${klass}-title-operation">
                    <s-button class="op-btn mr8" on-click="onEdit" disabled="{{operationDisabled}}">编辑</s-button>
                    <s-button class="op-btn mr8" on-click="onDelete" disabled="{{operationDisabled}}">删除</s-button>
                    <s-button class="op-btn" on-click="onRefresh" disabled="{{loading}}">刷新</s-button>
                </div>
            </div>
            <block-box title="基本信息">
                <s-loading loading="{{loading}}" class="detail-loading">
                    <biz-detail-cell datasource="{{datasource}}">
                        <div slot="c-brokerNames" class="c-ellipsis-tip">
                            <ellipsis-tip text="{{baseDetail.brokerNames}}" placement="top">
                                {{baseDetail.brokerNames}}
                            </ellipsis-tip>
                        </div>
                        <div slot="c-topicName" class="c-ellipsis-tip">
                            <ellipsis-tip text="{{baseDetail.topicName}}" placement="top">
                                {{baseDetail.topicName}}
                            </ellipsis-tip>
                        </div>
                    </biz-detail-cell>
                </s-loading>
                <topic-detail-table
                    s-if="{{baseDetail.brokers}}"
                    s-ref="topic-detail-table"
                    detail="{{detail}}"
                    route="{{route}}"
                    topicName="{{topicName}}"
                    baseDetail="{{baseDetail}}"
                />
            </block-box>
        </div>
    `;

    static components = {
        's-breadcrumb': Breadcrumb,
        's-breadcrumb-item': Breadcrumb.BreadcrumbItem,
        'block-box': BlockBox,
        'ellipsis-tip': EllipsisTip,
        'biz-detail-cell': AppDetailCell,
        's-loading': Loading,
        'topic-detail-table': TopicDetailTable,
        's-button': Button
    };

    static computed: SanComputedProps = {
        datasource() {
            const detail: TopicListProps = this.data.get('baseDetail');
            return [
                {label: '主题名称：', value: detail.topicName, slot: 'topicName'},
                {label: '关联节点组：', value: detail.brokers?.map(item => item.brokerName)},
                {label: '权限：', value: ClusterTopicPermissionTypeList.getTextFromValue(detail.permission)},
                {label: '读列队总数：', value: detail.readQueueSum},
                {label: '写列队总数：', value: detail.writeQueueSum}
            ];
        },
        operationDisabled() {
            const detail = this.data.get('detail');
            return isClusterStatusDisabled(detail);
        }
    };

    initData() {
        return {
            breadcrumbRoutes: [],
            baseDetail: {
                topicName: ''
            },
            loading: true
        };
    }

    inited() {
        this.initDetailData();
        this.getBrokerList();
    }

    async initDetailData() {
        const {
            route: {
                query: {clusterId}
            },
            topicName
        } = this.data.get('');
        this.data.set('loading', true);
        const result: TopicListProps = await api.getTopicDetail({
            clusterId,
            topicName
        });

        this.data.set('baseDetail', result);
        this.data.set('loading', false);
    }
    async getBrokerList() {
        const {
            route: {
                query: {clusterId}
            }
        } = this.data.get('');
        const {result} = await api.clusterNodes({
            clusterId,
            params: {
                pageNo: 1,
                pageSize: 10000
            }
        });

        // 获取去重后的 brokerId 列表
        const brokerList = Array.from(new Set(result.map(item => item.brokerId))).map(brokerId => ({
            label: brokerId,
            value: brokerId
        }));

        this.data.set('brokerList', brokerList);
    }

    onExitClick() {
        const {
            route: {
                query: {clusterId}
            }
        } = this.data.get('');
        this.data.set('topicName', '');
        updateUrlQuery({clusterId}, true);
    }

    onEdit() {
        const {
            route: {
                query: {clusterId}
            },
            baseDetail,
            brokerList
        } = this.data.get('');
        const dialog = new DetailTopicCreateDialog({
            data: {
                isEdit: true,
                datasource: baseDetail,
                clusterId,
                brokerList
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onDebounce('refresh');
        });
    }

    async onDelete() {
        const {
            route: {
                query: {clusterId}
            },
            baseDetail
        } = this.data.get('');
        baseDetail.brokerNames = baseDetail.brokers?.map((item: Broker) => item.brokerName);

        const dialog = new DetailTopicDeleteDialog({
            data: {
                datasource: [baseDetail],
                clusterId
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onDebounce('exit');
        });
    }

    @debounce(1000)
    onDebounce(type: 'refresh' | 'exit') {
        if (type === 'refresh') {
            this.onRefresh();
        } else {
            this.onExitClick();
        }
    }

    onRefresh() {
        this.initDetailData();
        this.ref('topic-detail-table') && this.ref('topic-detail-table').getComList();
    }
}
