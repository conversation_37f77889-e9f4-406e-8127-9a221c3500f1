/**
 * 主题详情-队列信息/消费组信息
 * @file index.ts
 * <AUTHOR>
 */
import {html} from '@baiducloud/runtime';
import {BlockBox, CommonTable, EllipsisTip, MultiFilter} from '@components/index';
import {Tabs, Table, Pagination, Tooltip} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz';
import {InfoCircle2} from '@baidu/xicon-san';
import {TABLE_SUI, PAGER_SUI} from '@common/config';
import {formatTime, formatTopicPerm, isAuthenticationModeNone, pickEmpty, updateUrlQuery} from '@common/utils';
import {api} from '@common/client';
import {ClusterConsumerMessageModel} from '@common/enums';
import {TopicDetailExpTable} from './exp-table';
import {Queue, ConsumerGroup, Broker} from '../../index.d';
import {AclUserListView} from '@pages/cluster/detail/consumer/consumer-detail/detail-table/index.d';
import './index.less';
import {IClusterDetail} from '@pages/cluster/detail/index.d';

enum TableTab {
    Queue = 'queue',
    ConsumerGroup = 'consumerGroup',
    Perm = 'permission'
}

const Table_Tab_List = [
    {label: '队列信息', value: TableTab.Queue, apiName: 'getTopicQueuesList'},
    {label: '消费组信息', value: TableTab.ConsumerGroup, apiName: 'getTopicConsumerGroupsList'}
];

const columnsMap = {
    [TableTab.Queue]: [
        {name: 'brokerName', label: '关联节点组', filter: {options: [], value: ''}},
        {name: 'queueId', label: '写队列ID'},
        {name: 'minOffset', label: '最小位点'},
        {name: 'maxOffset', label: '最大位点'},
        {
            name: 'lastUpdateTime',
            label: '消息更新时间',
            render: (item: Queue) => formatTime(item.lastUpdateTime)
        }
    ],
    [TableTab.ConsumerGroup]: [
        {name: 'groupName', label: '消费组名称', width: '25%'},
        {name: 'retryMaxTimes', label: '最大重试次数', width: '30%'},
        {
            name: 'consumeBroadcastEnable',
            label: '广播模式消费',
            width: '15%',
            render: (item: ConsumerGroup) => (item.consumeBroadcastEnable ? '允许' : '禁止')
        },
        {
            name: 'messageModel',
            label: '消费模式',
            width: '10%',
            render: (item: ConsumerGroup) => ClusterConsumerMessageModel.getTextFromValue(item.messageModel)
        }
    ],
    [TableTab.Perm]: [
        {name: 'accessKey', label: '用户名'},
        {
            name: 'whiteRemoteAddress',
            label: 'IP白名单'
        },
        {name: 'admin', label: '是否为管理员', render: (item: AclUserListView) => (item.admin ? '是' : '否')},
        {
            name: 'finalPerm',
            label: '权限',
            render: (item: AclUserListView) => formatTopicPerm(item.finalPerm)
        }
    ]
};
const klass = 'topic-detail-table';
export class TopicDetailTable extends CommonTable {
    static template = html`
        <template>
            <s-tabs active="{= activeTab =}" on-change="onTabChange" class="${klass}">
                <s-tabpane s-for="i in tabList" label="{{i.label}}" key="{{i.value}}" />
            </s-tabs>
            <s-table
                class="mt16"
                columns="{{table.columnsMap[activeTab]}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                has-Expand-Row="{{activeTab === TableTab.ConsumerGroup}}"
                datasource="{{table.datasource}}"
                on-selected-change="onSelectChange"
                on-exprow-expand="onRowExpand"
            >
                <span slot="h-brokerName-filter">
                    <multi-filter options="{{brokerList}}" value="{= brokerNames =}" on-change="onStatusChange" />
                </span>
                <span slot="h-messageModel">
                    <span>消费模式</span>
                    <s-tooltip content="消费模式为广播模式时，不再记录消费进度和消费时间" placement="topRight">
                        <s-icon-info class="pick-icon ml8" />
                    </s-tooltip>
                </span>
                <div slot="empty">
                    <s-empty emptyTitle="暂无数据" emptyText="" actionText="" />
                </div>
                <exp-table
                    slot="sub-table"
                    groupName="{{row.groupName}}"
                    topicName="{{topicName}}"
                    route="{{route}}"
                    brokers="{{baseDetail.brokers}}"
                />
                <div class="table-ellipsis" slot="c-brokerNames">
                    <ellipsis-tip text="{{row.brokerNames}}" placement="top"> {{row.brokerNames}} </ellipsis-tip>
                </div>
                <div class="table-ellipsis" slot="c-whiteRemoteAddress">
                    <ellipsis-tip
                        s-if="row.whiteRemoteAddress.length"
                        text="{{row.whiteRemoteAddress | filterWhiteRemoteAddress}}"
                        placement="top"
                    />
                    <div s-else>-</div>
                </div>
            </s-table>
            <s-pagination
                class="mt16"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="total, pageSize, pager"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </template>
    `;

    static components = {
        'block-box': BlockBox,
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        's-table': Table,
        's-pagination': Pagination,
        's-empty': Empty,
        's-tooltip': Tooltip,
        's-icon-info': InfoCircle2,
        'ellipsis-tip': EllipsisTip,
        'exp-table': TopicDetailExpTable,
        'multi-filter': MultiFilter
    };

    initData() {
        return {
            activeTab: TableTab.Queue,
            table: {
                ...TABLE_SUI,
                columnsMap,
                exprowLoading: true
            },
            pager: {...PAGER_SUI},
            TableTab
        };
    }

    static computed = {
        tabList() {
            let list = [...Table_Tab_List];
            const detail = this.data.get('detail') as IClusterDetail;
            if (detail && !isAuthenticationModeNone(detail.authenticationModes)) {
                list.push({label: '授权信息', value: TableTab.Perm, apiName: 'getTopicUsers'});
            }
            return list;
        }
    };

    inited() {
        const {
            route: {
                query: {activeTab}
            }
        } = this.data.get('');
        activeTab && this.data.set('activeTab', activeTab);
        this.initBrokerList();
    }

    attached(): void {
        this.getComList();
    }

    async getTableList() {
        const {
            pager,
            route: {
                query: {clusterId}
            },
            activeTab,
            topicName,
            brokerNames
        } = this.data.get('');
        let param = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            brokerNames
        });
        const tabList = this.data.get('tabList') as typeof Table_Tab_List;
        const currentApiName = tabList.find(item => item.value === activeTab)!.apiName;
        const {totalCount, result} = await api[currentApiName]({
            params: param,
            topicName,
            clusterId
        });
        this.data.set('pager.count', totalCount);
        this.data.set(
            'table.datasource',
            activeTab === TableTab.ConsumerGroup
                ? result.map((item: ConsumerGroup) => ({...item, subSlot: 'sub-table'}))
                : result
        );
    }

    onTabChange({value: {key: activeTab}}: {value: {key: TableTab}}) {
        // 更新当前选中tab到url
        updateUrlQuery({activeTab});

        this.data.set('table.datasource', []);
        this.data.set('activeTab', activeTab);
        this.onPageChange({value: {page: 1, pageSize: 10}});
        this.initBrokerList();
    }

    initBrokerList() {
        const {brokers} = this.data.get('baseDetail');
        const brokerList = brokers.map((item: Broker) => ({
            text: `${item.brokerName} | 读队列:${item.readQueueNum} 写队列:${item.writeQueueNum}`,
            value: item.brokerName
        }));
        const brokerNames = brokers.map((item: Broker) => item.brokerName);
        this.data.set('brokerList', brokerList);
        this.data.set('brokerNames', brokerNames);
    }

    onStatusChange({value}: {value: string[]}) {
        this.data.set('brokerNames', value);
        this.onPageChange({value: {page: 1, pageSize: this.data.get('pager.pageSize')}});
    }
}
