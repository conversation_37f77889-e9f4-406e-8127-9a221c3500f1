/**
 * 主题详情-消费组信息-子列表
 * @file index.ts
 * <AUTHOR>
 */
import {html} from '@baiducloud/runtime';
import {Table, Pagination} from '@baidu/sui';
import {BlockBox, CommonTable, MultiFilter} from '@components/index';
import {TABLE_SUI, PAGER_SUI} from '@common/config';
import {pickEmpty, formatTime} from '@common/utils';
import {api} from '@common/client';
import {ConsumerGroupSubList, Broker} from '../../../index.d';
import './index.less';

const columns = [
    {name: 'brokerName', label: '关联节点组', filter: {options: [], value: ''}},
    {name: 'queueId', label: '读队列ID'},
    {name: 'brokerOffset', label: '队列消息总数'},
    {name: 'consumerOffset', label: '已消费消息数'},
    {name: 'diffTotal', label: '未消费消息数'},
    {
        name: 'lastConsumeTime',
        label: '最后消费时间',
        render: (item: ConsumerGroupSubList) => formatTime(item.lastConsumeTime)
    }
];

const klass = 'topic-detail-exp-table';

export class TopicDetailExpTable extends CommonTable {
    static template = html`
        <div class="${klass}">
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                datasource="{{table.datasource}}"
                error="{{table.error}}"
            >
                <span slot="h-brokerName-filter">
                    <multi-filter options="{{brokerList}}" value="{= brokerNames =}" on-change="onStatusChange" />
                </span>
                <div slot="empty">
                    <s-empty emptyTitle="暂无数据" emptyText="" actionText="" />
                </div>
            </s-table>
            <s-pagination
                class="mt16"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="total, pager"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        's-table': Table,
        's-pagination': Pagination,
        'multi-filter': MultiFilter
    };

    initData() {
        return {
            table: {
                ...TABLE_SUI,
                columns
            },
            pager: {...PAGER_SUI},
            brokerNames: []
        };
    }

    inited() {
        this.getComList();
        const brokers = this.data.get('brokers');
        const brokerList = brokers.map((item: Broker) => ({
            text: item.brokerName,
            value: item.brokerName
        }));
        const brokerNames = brokers.map((item: Broker) => item.brokerName);
        this.data.set('brokerList', brokerList);
        this.data.set('brokerNames', brokerNames);
    }

    async getTableList() {
        const {pager, route, groupName, topicName, brokerNames} = this.data.get('');
        const {clusterId} = route.query;
        let params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            brokerNames
        });

        const {totalCount, result} = await api.getTopicConsumerGroupsQueue({
            clusterId,
            groupName,
            topicName,
            params
        });
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', result);
    }

    onStatusChange({value}: {value: string[]}) {
        this.data.set('brokerNames', value);
        this.onPageChange({value: {page: 1, pageSize: this.data.get('pager.pageSize')}});
    }
}
