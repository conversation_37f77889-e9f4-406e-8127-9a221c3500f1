.m-detail-topic-detail {
    .detail-loading {
        width: 100%;
    }

    &-title {
        display: flex;
        justify-content: space-between;

        &-operation {
            .op-btn {
                width: 48px;
            }
        }
    }

    .detail-cell {
        padding-right: 16px;

        .c-ellipsis-tip {
            height: 20px;

            .text-ellipsis-tip {
                display: inline-flex;
            }
        }

        &:nth-child(3n + 1) label {
            width: 72px;
            flex-shrink: 0;
        }

        &:nth-child(3n + 2) label {
            flex-shrink: 0;
        }
    }

    .s-pagination-wrapper {
        justify-content: flex-end;
    }
}
