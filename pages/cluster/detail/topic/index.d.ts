/** 关联节点组列表 */
export interface Broker {
    /**
     * 节点组名称
     * global:数组，custom:字符串
     */
    brokerName: string[] | string;
    /**
     * 读队列数
     */
    readQueueNum: number;
    /**
     * 写队列数
     */
    writeQueueNum: number;
}

/** 主题列表信息 */
export interface TopicListProps {
    /**
     * 主题名称
     */
    topicName: string;
    /**
     * 关联节点组
     */
    brokers: Broker[];
    /**
     * 权限
     */
    permission: number;
    /**
     * 读队列总数
     */
    readQueueSum: number;
    /**
     * 写队列总数
     */
    writeQueueSum: number;
    /**
     * 全部的节点组列表
     */
    brokerNames?: string[];
}

/** 创建/编辑主题 表单数据类型-关联节点组-下拉列表 */
export interface BrokerList {
    value: string;
    label: string;
    disabled?: boolean;
}

/** 创建/编辑主题 表单数据类型-关联节点组 */
export interface FromDataType {
    brokersData: Broker[];
    options: BrokerList[];
}

/** 创建/编辑主题 表单数据类型 */
interface FromData {
    topicName: string;
    /**
     * 权限
     * 2: 发布; 4: 订阅; 6:发布+订阅
     */
    permission: 2 | 4 | 6;
    /**
     * 关联节点组类型
     */
    associatedNodeGroupType: 'global' | 'custom';
    /**
     * 全局设置关联节点组列表
     */
    global: FromDataType;
    /**
     * 自定义设置关联节点组列表
     */
    custom: FromDataType;
}

/** 创建/编辑主题 关联节点组-枚举类型 */
export enum AssociatedNodeGroupType {
    CUSTOM = 'custom',
    GLOBAL = 'global'
}

/** 主题详情-队列信息 */
export interface Queue {
    /**
     * 关联节点组
     */
    brokerName: string;
    /**
     * 消息更新时间
     */
    lastUpdateTime: string;
    /**
     * 最大位点
     */
    maxOffset: number;
    /**
     * 最小位点
     */
    minOffset: number;
    /**
     * 写队列ID
     */
    queueId: number;
}

/** 主题详情-消费组信息 */
export interface ConsumerGroup {
    /**
     * 广播模式消费
     */
    consumeBroadcastEnable: boolean;
    /**
     * 消费组名称
     */
    groupName: string;
    /**
     * 消费模式
     */
    messageModel: string;
    /**
     * 最大重试次数
     */
    retryMaxTimes: number;
    subSlot: string;
}

/** 主题详情-消费组信息-子列表 */
export interface ConsumerGroupSubList {
    /**
     * 关联节点组
     */
    brokerName: string;
    /**
     * 队列消息总数
     */
    brokerOffset: number;
    /**
     * 客户端ID
     */
    clientId: string;
    /**
     * 已消费消息数
     */
    consumerOffset: number;
    /**
     * 未消费消息数
     */
    diffTotal: number;
    /**
     * 消费组名称
     */
    groupName: string;
    /**
     * 最后消费时间
     */
    lastConsumeTime: string;
    /**
     * 读队列ID
     */
    queueId: number;
}
