import {FlushDiskType, ModeType} from '@common/enums/biz';
import {IClusterItem, IFeClusterItemCustomProp} from '../list/index.d';
/**
 * 返回结果，返回一个对象
 */
export interface IClusterDetail extends IClusterItem {
    /**
     * 账号 ID
     */
    accountId: string;
    /**
     * 配置信息
     */
    config: Config;
    /**
     * 是否开启部署集
     */
    deploySetEnabled: boolean;
    /**
     * 过期时间，预付费才展示
     */
    expirationTime: string;
    /**
     * 刷盘方式，刷盘方式，可选：ASYNC_FLUSH / SYNC_FLUSH，默认 ASYNC_FLUSH
     */
    flushDiskType?: FlushDiskType;
    /**
     * 部署模式
     */
    mode: ModeType;
    /**
     * 接入点地址列表
     */
    nameServerEndpoints: AccessEndpoint[];
    /**
     * broker节点机型名称，broker节点机型名称
     */
    nodeSpec: string;
    /**
     * broker节点机型ID，broker 节点机型 ID
     */
    nodeType: string;
    /**
     * Broker（节点组） 內节点数，Broker（节点组） 內节点数。可选：
     * * Dledger架构：3
     * * Master-Slave架构：1-7
     */
    numberOfNodesPerBroker: number;
    /**
     * 总 Broker（节点组） 数，总 Broker（节点组） 数
     */
    numberOfBrokers?: number;
    /**
     * 节点总数，由部署架构、主从比例、可用区数量同时决定，自动计算出来
     */
    numberOfBrokerNodes: number;
    /**
     * 单节点磁盘块数，单节点的磁盘块数
     */
    numberOfDisks: number;
    /**
     * 商品类型，参数区别于付费类型，含义相同。可选：Postpay / Prepay
     */
    productType: string;
    /**
     * 自动分配模式下公网带宽，自动分配模式下公网带宽
     */
    publicAccessBandwidth: number;
    /**
     * 是否开启公网访问
     */
    publicAccessEnabled: boolean;
    /**
     * 公网访问模式，开启公网时，公网访问模式，可选：AUTO_ASSIGN / MANUAL_SELECT
     */
    publicAccessMode: string;
    /**
     * 集群地域
     */
    region: string;
    /**
     * 是否支持自动续，选择预付费时，是否支持自动续费
     */
    renew: boolean | null;
    /**
     * 自动续费时长，自动续费时长，单位使用 timeUnit 提供的时间单位
     */
    renewTimeLength: number | null;
    /**
     * 自动续费时间单位，自动续费时间单位，month / yaer
     */
    renewTimeUnit: RenewTimeUnit;
    runningTime: RunningTime;
    /**
     * 安全组信息列表
     */
    securityGroups: SecurityGroup[];
    /**
     * 单块磁盘容量，单块磁盘容量，单位：GB，范围：100~32768
     */
    storageSize: number;
    /**
     * 磁盘类型，磁盘类型，可选: hp1 / enhanced_ssd_pl1
     */
    storageType: StorageType;
    /**
     * 子网信息列表
     */
    subnets: Subnet[];
    /**
     * 标签列表
     */
    tags: Tag[];
    /**
     * 预付费时长，选择预付费生效
     */
    timeLength?: number;
    /**
     * 预付费时间单位，选择预付费生效
     */
    timeUnit?: string;
    /**
     * 部署类型
     */
    type: string;
    /**
     * 用户 ID，只用于跳转到下单的订单处，userId 不是资源（集群）的属主，accountId 才是
     */
    userId: string;
    /**
     * VPC 信息
     */
    vpc: Vpc;
    [property: string]: any;
}

export type IFeClusterDetail = IClusterDetail & IFeClusterItemCustomProp;
/**
 * 接入点地址列表项
 *
 * AccessEndpoint
 */
export interface AccessEndpoint {
    /**
     * 通信协议，可选：remoting \ gRpc
     */
    communicationProtocol: string;
    /**
     * 接入点地址
     */
    endpoint: string;
    [property: string]: any;
}

/**
 * 配置信息
 *
 * Config
 */
export interface Config {
    /**
     * 自定义配置描述
     */
    configDescription: string;
    /**
     * 自定义配置 ID
     */
    configId: string;
    /**
     * 自定义配置名称
     */
    configName: string;
    /**
     * 自定义配置修订描述
     */
    revisionDescription: string;
    /**
     * 自定义配置修订 ID
     */
    revisionId: number;
    [property: string]: any;
}

/**
 * 自动续费时间单位，自动续费时间单位，month / yaer
 */
export enum RenewTimeUnit {
    Month = 'month',
    Year = 'year'
}

export interface RunningTime {
    day: number;
    hour: number;
    minute: number;
    /**
     * 运行总时长，单位：毫秒
     */
    runningTimeMs: number;
    second: number;
    [property: string]: any;
}

/**
 * SecurityGroup
 */
export interface SecurityGroup {
    /**
     * 安全组名称
     */
    name: string;
    /**
     * 安全组短 ID
     */
    securityGroupId: string;
    /**
     * VPC 短 ID
     */
    vpcId: string;
    [property: string]: any;
}

/**
 * 集群状态
 */
export enum ClusterStatus {
    Active = 'ACTIVE',
    DeployFailed = 'DEPLOY_FAILED',
    DeployRollbackFailed = 'DEPLOY_ROLLBACK_FAILED',
    DeployRollbacking = 'DEPLOY_ROLLBACKING',
    Deploying = 'DEPLOYING',
    New = 'NEW',
    Passive = 'PASSIVE',
    PreRebooting = 'PRE_REBOOTING',
    PreResuming = 'PRE_RESUMING',
    PreSuspending = 'PRE_SUSPENDING',
    PreUpdating = 'PRE_UPDATING',
    RebootRollbackFailed = 'REBOOT_ROLLBACK_FAILED',
    RebootRollbacking = 'REBOOT_ROLLBACKING',
    Rebooting = 'REBOOTING',
    Resuming = 'RESUMING',
    Suspended = 'SUSPENDED',
    Suspending = 'SUSPENDING',
    UpdateRollbackFailed = 'UPDATE_ROLLBACK_FAILED',
    UpdateRollbacking = 'UPDATE_ROLLBACKING',
    Updating = 'UPDATING'
}

/**
 * 磁盘类型，磁盘类型，可选: hp1 / enhanced_ssd_pl1
 */
export enum StorageType {
    EnhancedssdPl1 = 'enhanced_ssd_pl1',
    Hp1 = 'hp1'
}

/**
 * Subnet
 */
export interface Subnet {
    /**
     * 子网 cidr信息
     */
    cidr: string;
    /**
     * 子网名称
     */
    name: string;
    /**
     * 子网短 ID
     */
    subnetId: string;
    /**
     * 子网 UUID
     */
    subnetUuid: string;
    /**
     * VPC 短 ID
     */
    vpcId: string;
    /**
     * 子网对应的可用区
     */
    zone: string;
    [property: string]: any;
}

/**
 * 标签
 *
 * Tag
 */
export interface Tag {
    /**
     * 标签键
     */
    tagKey: string;
    /**
     * 标签值
     */
    tagValue: string;
    [property: string]: any;
}

/**
 * VPC 信息
 *
 * Vpc
 */
export interface Vpc {
    /**
     * VPC分配的 cidr
     */
    cidr: string;
    /**
     * VPC 名称
     */
    name: string;
    /**
     * VPC 短 ID
     */
    vpcId: string;
    /**
     * VPC UUID
     */
    vpcUuid: string;
    [property: string]: any;
}
