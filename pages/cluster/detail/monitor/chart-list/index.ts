/**
 * 监控图表
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {IMetric} from '../metrics/index.d';
import {ChartItem} from '../components/chart-item';
import './index.less';
import {ISelectedMetricsMap} from '../metrics';
import {ChartDetail} from '../chart-detail';
import {EChartsOption} from 'echarts';
import {IMonitorType, IRangeFeTime} from '../filter-area';
import {IDimensions} from '../components/dimensions-filter/index.d';
import {IMetricChangeType} from '../index.d';

const klass = 'm-cluster-detail-echarts';

export class ChartList extends Component {
    static template = html`
        <template>
            <div class="${klass}">
                <chart-item
                    class="${klass}-item"
                    s-for="item, index in metrics"
                    s-ref="chartItem-{{index}}"
                    data="{{item}}"
                    rangeTime="{{rangeTime}}"
                    selectedMetricsMap="{{selectedMetricsMap}}"
                    dimensions="{{dimensions}}"
                    monitorType="{{monitorType}}"
                    cycle="{{60}}"
                    on-view-detail="handleViewDetail"
                    legendData="{{legendData}}"
                    legendValue="{{legendValue}}"
                    hasBorder
                    showViewDetail
                    showTitle
                    route="{{route}}"
                />
            </div>
        </template>
    `;

    static components = {
        'chart-item': ChartItem,
    };

    initData() {
        return {
            // ---- 由组件传入 ----start
            selectedMetricsMap: {},
            rangeTime: {},
            dimensions: [],
            route: {},
            legendData: [],
            legendValue: [],
            // ---- 由组件传入 ----end
            metrics: [],
        };
    }

    attached() {}

    handleSelectedMetricsChange(selectedMetricsMap: ISelectedMetricsMap, changeType: IMetricChangeType) {
        // 置空metrics：为了确保 指标弹窗确认后，根据metrics遍历生成的chart-item都重新渲染
        this.data.set('metrics', []);
        this.nextTick(() => {
            this.data.set('selectedMetricsMap', selectedMetricsMap);
            this.setMetrics(selectedMetricsMap, changeType);
        });
    }

    async setMetrics(selectedMetricsMap: ISelectedMetricsMap, changeType: IMetricChangeType) {
        let metrics: Array<IMetric> = [];
        for (const [category, selectedMetrics] of Object.entries(selectedMetricsMap)) {
            metrics = metrics.concat(selectedMetrics);
        }

        this.data.set('metrics', metrics);
        const {monitorType} = this.data.get('');
        if (changeType === IMetricChangeType.Init) {
            // 其它监控类型，在维度数据请求后触发接口请求
            monitorType === IMonitorType.Cluster &&
                this.nextTick(() => {
                    this.batchGetMetricsData();
                });
        } else {
            this.nextTick(() => {
                this.batchGetMetricsData();
            });
        }
    }

    batchGetMetricsData() {
        const metrics = this.data.get('metrics') as IMetric[];
        metrics.forEach((item, index) => {
            this.nextTick(async () => {
                const refChartItem = this.ref(`chartItem-${index}`) as ChartItem;
                // 无子维度时，才在此触发监控数据获取，
                // 有子维度时，需要在chart-item当中，待子维度数据获取完成后，再获取监控数据
                if (!item.hasSubDimension) {
                    refChartItem.getMetricData();
                }
            });
        });
    }

    handleViewDetail(e: {data: IMetric; selectedMetricsMap: ISelectedMetricsMap; chartOptions: EChartsOption}) {
        const {data, selectedMetricsMap, chartOptions} = e;
        const {monitorType, route, rangeTime, dimensions, legendData} = this.data.get('');
        let chartDetailData: {
            monitorType: IMonitorType;
            route: IRoute;
            data: IMetric;
            selectedMetricsMap: ISelectedMetricsMap;
            chartOptions: EChartsOption;
            dimensions: IDimensions;
            rangeTime?: IRangeFeTime;
            legendData: string[];
        } = {
            monitorType,
            route,
            data,
            selectedMetricsMap,
            chartOptions,
            dimensions,
            legendData,
        };

        if (rangeTime.begin && rangeTime.end) {
            chartDetailData.rangeTime = rangeTime;
        }

        const dialog = new ChartDetail({
            data: chartDetailData,
        });

        dialog.attach(document.body);
    }
}
