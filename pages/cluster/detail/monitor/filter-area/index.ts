/**
 * 筛选区域，包括时间、监控类型、刷新按钮&指标齿轮、节点组（集群监控）/ 节点ID（节点监控） / 主题 /消费组
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html, Enum} from '@baiducloud/runtime';
import {Button, Select, DatePicker, Tooltip, Radio, Notification} from '@baidu/sui';
import './index.less';
import {RefreshBtn, SettingBtn} from '@components/index';
import {IMonitorTab} from '../index';
import {One_Hour_Time_Ms} from '@common/config';
import {DimensionsFilter} from '../components/dimensions-filter';
import {IDimensions} from '../components/dimensions-filter/index.d';

const klass = 'm-monitor-filter';

enum TimeItem {
    '1H' = '1h',
    '3H' = '3h',
    '6H' = '6h',
    '9H' = '9h',
    '1D' = '1D',
    '3D' = '3D',
    '7D' = '7D',
    '14D' = '14D',
    '30D' = '30D',
    'Custom' = 'custom',
}

export interface IRangeFeTime {
    begin: Date | number;
    end: Date | number;
}

// 时间选取
export const TIME_LIST = new Enum(
    {alias: '1H', text: '近1小时', value: TimeItem['1H'], time: One_Hour_Time_Ms},
    {alias: '3H', text: '近3小时', value: TimeItem['3H'], time: 3 * One_Hour_Time_Ms},
    {alias: '6H', text: '近6小时', value: TimeItem['6H'], time: 6 * One_Hour_Time_Ms},
    {alias: '1D', text: '近1天', value: TimeItem['1D'], time: 24 * One_Hour_Time_Ms},
    {alias: '3D', text: '近3天', value: TimeItem['3D'], time: 3 * 24 * One_Hour_Time_Ms},
    {alias: '7D', text: '近7天', value: TimeItem['7D'], time: 7 * 24 * One_Hour_Time_Ms},
    {alias: '14D', text: '近14天', value: TimeItem['14D'], time: 14 * 24 * One_Hour_Time_Ms},
    {alias: '30D', text: '近30天', value: TimeItem['30D'], time: 30 * 24 * One_Hour_Time_Ms},
    {alias: 'Custom', text: '自定义', value: TimeItem.Custom},
);

/** 监控指标类型 */
export enum IMonitorType {
    /** 集群监控 */
    Cluster = 'CLUSTER',
    /** 节点监控：节点组 */
    Broker = 'BROKER',
    /** 主机监控：节点ID */
    Node = 'NODE',
    /** 主题监控：主题 */
    Topic = 'TOPIC',
    /** 消费组监控——消费组 */
    Consumergroup = 'CONSUMERGROUP',
    /** 消费组监控-消费组分主题监控 */
    CosumerGroupTopic = 'CONSUMERGROUP_TOPIC',
}

const formatMonitorTypes = (activeTab: string) => {
    switch (activeTab) {
        case IMonitorTab.Cluster:
            return [];
        case IMonitorTab.Node:
            return [
                {label: '服务监控', value: IMonitorType.Broker},
                {label: '主机监控', value: IMonitorType.Node},
            ];
        case IMonitorTab.Theme:
            return [{label: '基础监控', value: IMonitorType.Topic}];
        case IMonitorTab.ConsumerGroup:
            return [
                {label: '基础监控', value: IMonitorType.Consumergroup},
                {label: '消费组分主题', value: IMonitorType.CosumerGroupTopic},
            ];
        default:
            return [];
    }
};

export const getDefaultRangeTime = () => {
    const nowTime = new Date().getTime();
    return {
        begin: new Date(nowTime - One_Hour_Time_Ms),
        end: new Date(nowTime),
    };
};

export const getRange = () => {
    const nowDate = new Date();
    const nowTime = nowDate.getTime();
    return {
        begin: new Date(nowTime - One_Hour_Time_Ms * 24 * 40),
        end: nowDate,
    };
};

export class FilterArea extends Component {
    static template = html`
        <div class="${klass}">
            <div class="${klass}__basic">
                <label class="select-label">时间筛选：</label>
                <s-select
                    class="mr5"
                    value="{{curTime}}"
                    datasource="{{timeList}}"
                    on-change="onRecentChange"
                    width="{{100}}"
                />
                <s-date-range-picker
                    mode="second"
                    value="{{rangeTime}}"
                    class="mr24"
                    range="{{range}}"
                    on-change="onCustomTimeChange"
                    clearable="{{false}}"
                />
                <div s-if="monitorTypes.length">
                    <label>监控类型：</label>
                    <s-radio-group
                        value="{= monitorType =}"
                        datasource="{{monitorTypes}}"
                        on-change="onMonitorTypeChange"
                    ></s-radio-group>
                </div>
                <div class="right">
                    <refresh-btn on-click="onRefresh" />
                    <s-tooltip content="指标筛选" placement="topRight" class="ml8">
                        <setting-btn class="right ml8" on-click="onOpenMetrics" />
                    </s-tooltip>
                </div>
            </div>
            <dimensions-filter
                monitorType="{{monitorType}}"
                route="{{route}}"
                on-dimensions-change="handleDimensionsChange"
            />
        </div>
    `;

    static components = {
        's-select': Select,
        's-date-range-picker': DatePicker.DateRangePicker,
        's-radio-group': Radio.RadioGroup,
        's-radio': Radio,
        's-tooltip': Tooltip,
        's-button': Button,
        'refresh-btn': RefreshBtn,
        'setting-btn': SettingBtn,
        'dimensions-filter': DimensionsFilter,
    };

    static computed: SanComputedProps = {
        monitorTypes() {
            const activeTab = this.data.get('activeTab');
            return formatMonitorTypes(activeTab);
        },
    };

    initData() {
        const nowDate = new Date();
        const nowTime = nowDate.getTime();

        return {
            timeList: TIME_LIST.toArray(),
            curTime: TIME_LIST['1H'],
            rangeTime: getDefaultRangeTime(),
            IMonitorType,
            range: getRange(),
            monitorType: IMonitorType.Cluster,
            selectDatasource: [],
            selectValue: [],
            consumerGroupTopics: [],
        };
    }

    onRecentChange(e: {value: string}) {
        const value = e.value;
        const now = new Date().getTime();
        const time = TIME_LIST.fromValue(value).time;
        const range = {
            begin: new Date(now - time),
            end: new Date(now),
        };
        this.data.set('curTime', value);
        if (value !== TimeItem.Custom) {
            this.data.set('rangeTime', range);
            this.fire('recent-change', range);
        }
    }

    onCustomTimeChange(e: {value: {begin: Date; end: Date}}) {
        const {begin, end} = e.value;
        let beginTime = new Date(begin).getTime();
        let endTime = new Date(end).getTime();
        this.data.set('range', getRange());
        this.data.set('curTime', TimeItem.Custom);
        this.data.set('rangeTime', {begin: beginTime, end: endTime});
        this.fire('recent-change', {begin: beginTime, end: endTime});
    }

    // activeTab修改后处理逻辑
    handleActiveTabChange(key: IMonitorTab) {
        const monitorTypes = formatMonitorTypes(key);
        this.data.set('activeTab', key);
        let monitorType = monitorTypes[0]?.value;
        if (key === IMonitorTab.Cluster) {
            monitorType = IMonitorType.Cluster;
        }
        this.data.set('monitorType', monitorType);
        this.onMonitorTypeChange({value: monitorType});
    }

    onMonitorTypeChange(e: {value: IMonitorType}) {
        this.fire('monitor-type-change', {
            type: e.value,
        });
    }

    handleDimensionsChange(e: {type: IMonitorType; dimensions: IDimensions}) {
        this.fire('dimensions-change', e);
    }

    onRefresh() {
        const curTime = this.data.get('curTime');
        if (curTime !== TimeItem.Custom) {
            this.onRecentChange({value: curTime});
        } else {
            const curTime = new Date();
            const newTime = {
                begin: curTime,
                end: curTime,
            };
            this.onCustomTimeChange({
                value: newTime,
            });
        }
    }

    refresh() {
        this.onRefresh();
    }

    onOpenMetrics() {
        this.fire('open-metrics', {});
    }
}
