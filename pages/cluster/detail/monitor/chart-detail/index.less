.chart-item-detail {
    .s-dialog-wrapper {
        width: 100vw;
        height: 90vh;
        max-height: initial;

        .s-dialog-content {
            width: 100% !important;
            height: 100% !important;
            display: flex;
            flex-direction: column;
        }
    }

    &__header {
        display: flex;

        .s-dropdown-wrap.s-dropdown-visible .s-button .x-icon {
            transform: rotate(180deg);
        }
    }

    &__selected-menu-item {
        color: var(--text-primary-color);
    }

    &__filter-area {
        display: flex;
        align-items: center;

        &-left {
            margin-right: auto;
        }
    }

    &__chart-container {
        flex: 1;
    }

    &__metric-list {
        max-height: 300px;
        overflow: auto;
    }
}
