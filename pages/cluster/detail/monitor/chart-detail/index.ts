/**
 * 监控详情
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, DatePicker, Button, Select, Dropdown, Menu, Checkbox} from '@baidu/sui';
import {Down} from '@baidu/xicon-san';
import {ISelectedMetricsMap} from '../metrics/index';
import {IMetric} from '../metrics/index.d';
import './index.less';
import {One_Hour_Time_Ms} from '@common/config';
import {ChartItem} from '../components/chart-item';
import {RefreshBtn} from '@components/refresh-btn';
import {DimensionsFilter} from '../components/dimensions-filter';
import _ from 'lodash';
import {IDimensions} from '../components/dimensions-filter/index.d';
import {getDefaultRangeTime, getRange} from '../filter-area';

const klass = 'chart-item-detail';

const CycleList = [
    {label: '1分钟', value: 60},
    {label: '5分钟', value: 5 * 60},
    {label: '10分钟', value: 10 * 60},
    {label: '15分钟', value: 15 * 60},
    {label: '30分钟', value: 30 * 60},
    {label: '1小时', value: 60 * 60}
];
type ICycleValue = (typeof CycleList)[0]['value'];

const shortcutTime = [
    {text: '1小时', value: One_Hour_Time_Ms},
    {text: '6小时', value: One_Hour_Time_Ms * 6},
    {text: '1天', value: One_Hour_Time_Ms * 24},
    {text: '7天', value: One_Hour_Time_Ms * 24 * 7},
    {text: '14天', value: One_Hour_Time_Ms * 24 * 14},
    {text: '40天', value: One_Hour_Time_Ms * 24 * 40}
];

type IStatisticsValue = 'average' | 'sum' | 'maximum' | 'minimum';
type IStatisticsItem = {
    value: IStatisticsValue;
    text: string;
};
const StatisticsList: IStatisticsItem[] = [
    {value: 'average', text: '平均值'},
    {value: 'sum', text: '和值'},
    {value: 'maximum', text: '最大值'},
    {value: 'minimum', text: '最小值'}
];

// 原始值取的是平均值的数据
const StatisticsAverage: IStatisticsItem[] = [{value: 'average', text: '原始值'}];

export class ChartDetail extends Component {
    static template = html`
        <template>
            <s-dialog title="{{data.nameCN}}" open="{=open=}" class="${klass}" width="800" on-cancel="onCancel">
                <div slot="title" class="${klass}__header">
                    <span class="title-text mr8">{{data.nameCN}}</span>
                    <s-dropdown trigger="click" visible="{{dropdownVisible}}">
                        <s-menu slot="overlay" on-click="onMenuSelect" class="${klass}__metric-list">
                            <s-menu-item
                                s-for="m,index in selectedMetrics"
                                key="{{m}}"
                                value="{{m.name}}"
                                class="{{m.name === data.name ? '${klass}__selected-menu-item' : ''}}"
                            >
                                {{m.nameCN}}
                            </s-menu-item>
                        </s-menu>
                        <s-button skin="normal-stringfy" on-click="onDropdownVisibleChange">
                            {{data.nameCN}}
                            <x-icon-down size="{{16}}" />
                        </s-button>
                    </s-dropdown>
                </div>
                <div class="${klass}__filter-area">
                    <div class="${klass}__filter-area-left">
                        <s-date-range-picker
                            mode="second"
                            value="{= rangeTime =}"
                            class="mr24"
                            range="{{range}}"
                            on-change="onRangeTimeChange"
                            shortcut="{{shortcutRangeTime}}"
                            clearable="{{false}}"
                        />
                        <label>统计周期：</label>
                        <s-select
                            datasource="{{computedCycleList}}"
                            value="{= cycle =}"
                            on-change="onCycleChange"
                        ></s-select>
                        <label class="ml8">统计方式：</label>
                        <s-select
                            datasource="{{statisticsList}}"
                            value="{= statistics =}"
                            disabled="{{statisticsDisabled}}"
                            on-change="onStatisticsChange"
                        ></s-select>
                    </div>
                    <s-checkbox value="{= connectNulls =}" on-change="onConnectNullsChange" class="mr16">
                        断点连接
                    </s-checkbox>
                    <refresh-btn on-click="onRefresh"></refresh-btn>
                </div>
                <div class="${klass}__dimensions">
                    <dimensions-filter
                        monitorType="{{monitorType}}"
                        route="{{route}}"
                        on-dimensions-change="handleDimensionsChange"
                    />
                </div>
                <div class="${klass}__chart-container">
                    <chart-item
                        s-ref="chartContainer"
                        data="{{data}}"
                        rangeTime="{{rangeTime}}"
                        selectedMetricsMap="{{selectedMetricsMap}}"
                        dimensions="{{dimensions}}"
                        monitorType="{{monitorType}}"
                        cycle="{{cycle}}"
                        route="{{route}}"
                        showTable
                        showSubDimension
                        legendData="{{legendData}}"
                    ></chart-item>
                </div>
                <div slot="footer"></div>
            </s-dialog>
        </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-dropdown': Dropdown,
        's-menu': Menu,
        's-menu-item': Menu.Item,
        'x-icon-down': Down,
        's-button': Button,
        's-date-range-picker': DatePicker.DateRangePicker,
        's-select': Select,
        's-checkbox': Checkbox,
        'refresh-btn': RefreshBtn,
        'chart-item': ChartItem,
        'dimensions-filter': DimensionsFilter
    };

    static computed = {
        selectedMetrics() {
            const selectedMetricsMap = this.data.get('selectedMetricsMap') as ISelectedMetricsMap;
            let metrics: IMetric[] = [];
            for (const [key, value] of Object.entries(selectedMetricsMap)) {
                metrics = metrics.concat(value);
            }
            return metrics;
        },
        computedCycleList() {
            const rangeTime = this.data.get('rangeTime');
            const difference = new Date(rangeTime.end).getTime() - new Date(rangeTime.begin).getTime();
            const cycleList = this.data.get('cycleList') as typeof CycleList;
            let list = cycleList;
            if (One_Hour_Time_Ms < difference && difference <= One_Hour_Time_Ms * 24) {
                // 1小时——24小时：仅展示大于等于1分钟的统计周期
                list = cycleList.filter(item => item.value >= 60);
            } else if (One_Hour_Time_Ms * 24 < difference && difference <= One_Hour_Time_Ms * 24 * 3) {
                // 1天小时——3天：仅展示大于等于5分钟的统计周期
                list = cycleList.filter(item => item.value >= 60 * 5);
            } else if (One_Hour_Time_Ms * 24 * 3 < difference && difference <= One_Hour_Time_Ms * 24 * 7) {
                // 3天小时——7天：仅展示大于等于10分钟的统计周期
                list = cycleList.filter(item => item.value >= 60 * 10);
            } else if (One_Hour_Time_Ms * 24 * 7 < difference && difference <= One_Hour_Time_Ms * 24 * 40) {
                // 7天小时——40天：仅展示大于等于30分钟的统计周期
                list = cycleList.filter(item => item.value >= 60 * 30);
            } else if (difference > One_Hour_Time_Ms * 24 * 40) {
                // 40天以上：仅展示大于等于60分钟的统计周期
                list = cycleList.filter(item => item.value >= 60 * 60);
            }
            // 过滤掉
            return list;
        }
    };

    initData() {
        const nowDate = new Date();
        const nowTime = nowDate.getTime();
        return {
            open: true,
            // --- 由组件传入 ---start
            data: {},
            selectedMetricsMap: {},
            route: {},
            dimensions: [],
            legendData: [],
            // --- 由组件传入 ---end
            dropdownVisible: false,
            range: getRange(),
            rangeTime: getDefaultRangeTime(),
            shortcutRangeTime: shortcutTime,
            collectCycle: 60, // 采集周期，默认为60
            cycleList: CycleList,
            cycle: CycleList[0].value,
            statisticsList: StatisticsList,
            statistics: StatisticsList[0].value,
            statisticsDisabled: true,
            connectNulls: false
        };
    }

    attached() {
        this.initShortcutRangeTime();
    }

    initShortcutRangeTime() {
        const shortcutRangeTime = this.data.get('shortcutRangeTime') as typeof shortcutTime;
        const newShorcutRangeTime = shortcutRangeTime.map(time => {
            return {
                text: time.text,
                onClick: (picker: any) => {
                    const date = {
                        begin: new Date(new Date().getTime() - time.value),
                        end: new Date()
                    };
                    picker.setValueByShortCut(date);
                }
            };
        });
        this.data.set('shortcutRangeTime', newShorcutRangeTime);
    }

    onDropdownVisibleChange() {
        this.data.set('dropdownVisible', true);
    }

    onMenuSelect(e: {value: IMetric}) {
        this.data.merge('data', e.value);
        this.data.set('dropdownVisible', false);
        this.nextTick(() => {
            this.handleGetMetricsData();
        });
    }

    onRangeTimeChange(e: {value: {begin: Date; end: Date}}) {
        const {begin, end} = e.value;
        const date = {begin, end};
        this.data.set('range', getRange());
        this.data.set('rangeTime', date);
        const computedCycleList = this.data.get('computedCycleList') as typeof CycleList;
        const newCycle = computedCycleList[0].value;
        this.data.set('cycle', newCycle);
        this.setStatistics(newCycle);
    }

    /**
     * 切换统计周期
     * @param e
     */
    onCycleChange(e: {value: ICycleValue}) {
        this.setStatistics(e.value);
        this.nextTick(() => {
            this.handleGetMetricsData();
        });
    }

    /**
     * 根据统计周期，设置统计方式展示列表
     * 当统计周期为采集周期时，统计方式只有原始值，且置灰
     * @param cycleValue
     */
    setStatistics(cycleValue: ICycleValue) {
        const collectCycle = this.data.get('collectCycle');
        const isStaticsDisabled = collectCycle === cycleValue;
        this.data.set('statisticsDisabled', isStaticsDisabled);
        const statisticsList = isStaticsDisabled ? StatisticsAverage : StatisticsList;
        this.data.set('statisticsList', statisticsList);

        if (isStaticsDisabled) {
            this.data.set('statistics', StatisticsAverage[0].value);
            this.data.set('data.statistics', StatisticsAverage[0].value);
        }
    }

    /**
     * 切换统计方式
     * @param e
     */
    onStatisticsChange(e: {value: IStatisticsValue}) {
        this.data.set('statistics', e.value);
        this.data.set('data.statistics', e.value);
        this.nextTick(() => {
            this.handleGetMetricsData();
        });
    }

    onConnectNullsChange(e: {value: boolean}) {
        const refChartContainer = this.ref('chartContainer') as ChartItem;
        refChartContainer.handleConnectNullsChange(e.value);
    }

    /**
     * 监控维度变化
     * @param data
     */
    handleDimensionsChange(data: {
        /** 监控维度 */
        dimensions: IDimensions;
    }) {
        const {dimensions} = data;
        this.data.set('dimensions', dimensions);
        this.nextTick(() => {
            this.handleGetMetricsData();
        });
    }

    onRefresh() {
        this.data.set('rangeTime', {
            begin: new Date(new Date().getTime() - One_Hour_Time_Ms),
            end: new Date(new Date().getTime())
        });
    }

    handleGetMetricsData() {
        const data = this.data.get('data') as IMetric;
        // 无子维度时，才在此触发监控数据获取，
        // 有子维度时，需要在chart-item当中，待子维度数据获取完成后，再获取监控数据
        if (!data.hasSubDimension) {
            const refchartContainer = this.ref('chartContainer') as ChartItem;
            refchartContainer && refchartContainer.getMetricData();
        }
    }

    onCancel() {
        this.data.set('open', false);
    }

    detached() {
        this.dispose && this.dispose();
    }
}
