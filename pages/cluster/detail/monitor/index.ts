/**
 * 集群监控
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Tabs, Radio} from '@baidu/sui';
import {BlockBox} from '@components/block-box';
import {FilterArea, getDefaultRangeTime, IMonitorType, IRangeFeTime} from './filter-area';
import {ISelectedMetricsMap, ISelectedMetricsValueMap, MonitorMetrics} from './metrics';
import {api} from '@common/client';
import {IMetricCategory} from './metrics/index.d';
import {ChartList} from './chart-list';
import {IDimensions} from './components/dimensions-filter/index.d';
import {IMetricChangeType} from './index.d';

const klass = 'm-detail-monitor';

export enum IMonitorTab {
    Cluster = 'cluster',
    Node = 'node',
    Theme = 'Theme',
    ConsumerGroup = 'consumerGroup',
}

const Monitor_Tab_List = [
    {label: '集群监控', value: IMonitorTab.Cluster},
    {label: '节点监控', value: IMonitorTab.Node},
    {label: '主题监控', value: IMonitorTab.Theme},
    {label: '消费组监控', value: IMonitorTab.ConsumerGroup},
];

export class DetailMonitor extends Component {
    static template = html`
        <div class="${klass}">
            <block-box title="集群监控">
                <s-tabs active="{= activeTab =}" on-change="onTabChange">
                    <s-tabpane s-for="i in tabList" label="{{i.label}}" key="{{i.value}}" />
                </s-tabs>
                <filter-area
                    s-ref="filterArea"
                    activeTab="{{activeTab}}"
                    monitorType="{{monitorType}}"
                    route="{{route}}"
                    on-open-metrics="openMetric"
                    on-recent-change="handleRecentChange"
                    on-tab-change="handleTabChange"
                    on-monitor-type-change="handleMonitorTypeChange"
                    on-dimensions-change="handleDimensionsChange"
                >
                </filter-area>
                <chart-list
                    s-ref="chartList"
                    monitorType="{{monitorType}}"
                    route="{{route}}"
                    selectedMetricsMap="{{selectedMetricsMap}}"
                    rangeTime="{{rangeTime}}"
                    dimensions="{{dimensions}}"
                    legendData="{{legendData}}"
                    legendValue="{{legendValue}}"
                    on-batch-get-metrics-data="handleBatchGetMetricsData"
                />
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        'filter-area': FilterArea,
        's-radio-group': Radio.RadioGroup,
        'chart-list': ChartList,
    };

    static computed: SanComputedProps = {};

    initData() {
        return {
            tabList: Monitor_Tab_List,
            activeTab: IMonitorTab.Cluster,
            // 传入弹窗
            categoryMetrics: [],
            // --- 传入子组件  ---- start
            selectedMetricsMap: {},
            rangeTime: getDefaultRangeTime(),
            dimensions: [],
            monitorType: IMonitorType.Cluster,
            legendData: [],
            legendValue: [],
            // --- 传入子组件  ---- end
        };
    }

    attached() {
        this.handleInitTab();
    }

    handleMonitorTypeChange(e: {type: IMonitorType}) {
        const type = e.type;
        this.data.set('monitorType', e.type);
        this.getMetrics(type);
    }

    async getMetrics(type: IMonitorType) {
        this.data.set('categoryMetrics', []);
        this.data.set('selectedMetricsMap', {});
        const res = (await api.monitorItems({type})) as IMetricCategory[];
        let initSelectMetricsMap: ISelectedMetricsMap = {};
        const data: Array<IMetricCategory & {value: number}> =
            res?.map((item, index) => {
                let newItem = {
                    ...item,
                    value: index,
                    metrics: item.metrics || [],
                };
                !initSelectMetricsMap[newItem.value] && (initSelectMetricsMap[newItem.value] = []);

                newItem.metrics.forEach((label) => {
                    if (label.defaultDisplay) {
                        initSelectMetricsMap[newItem.value].push(label);
                    }
                });
                return newItem;
            }) || [];
        this.data.set('categoryMetrics', data);
        this.data.set('selectedMetricsMap', initSelectMetricsMap);
        this.handleSelectedMetricsChange(initSelectMetricsMap, IMetricChangeType.Init);
    }

    handleInitTab() {
        this.getMetrics(IMonitorType.Cluster);
    }

    onTabChange(e: {value: {key: IMonitorTab}}) {
        const refFilterArea = this.ref('filterArea') as FilterArea;
        refFilterArea.handleActiveTabChange(e.value.key);
    }

    openMetric() {
        const {categoryMetrics, selectedMetricsMap} = this.data.get('');
        const dialog = new MonitorMetrics({
            data: {
                categoryMetrics,
                selectedMetricsValueMap: this.getSelectedMetricsValueMap(selectedMetricsMap),
            },
        });

        dialog.on('confirm', (data) => {
            this.data.set('selectedMetricsMap', data);
            this.handleSelectedMetricsChange(data as unknown as ISelectedMetricsMap, IMetricChangeType.Change);
        });
        dialog.attach(document.body);
    }

    getSelectedMetricsValueMap(selectedMetricsMap: ISelectedMetricsMap) {
        let selectedMetricsValueMap: ISelectedMetricsValueMap = {};
        for (const [key, metrics] of Object.entries(selectedMetricsMap)) {
            selectedMetricsValueMap[key] = metrics.map((m) => m.name);
        }

        return selectedMetricsValueMap;
    }

    handleSelectedMetricsChange(selectedMetricsMap: ISelectedMetricsMap, changeType: IMetricChangeType) {
        const refChartList = this.ref('chartList') as ChartList;
        refChartList.handleSelectedMetricsChange(selectedMetricsMap, changeType);
    }

    handleRecentChange(rangeTime: IRangeFeTime) {
        this.data.set('rangeTime', rangeTime);
    }

    /**
     * 监控维度变化
     * @param data
     */
    handleDimensionsChange(data: {
        /** 监控类型 */
        monitorType: IMonitorType;
        /** 监控维度 */
        dimensions: IDimensions;
        legendData: string[];
        legendValue?: string[];
    }) {
        const {dimensions, legendData, legendValue} = data;
        this.data.set('legendData', legendData);
        this.data.set('dimensions', dimensions);
        this.data.set('legendValue', legendValue);
        this.handleBatchGetMetricsData();
    }

    handleBatchGetMetricsData() {
        const refChartList = this.ref('chartList') as ChartList;
        refChartList && refChartList.batchGetMetricsData();
    }

    refresh() {
        const refFilterArea = this.ref('filterArea') as FilterArea;
        refFilterArea.refresh();
    }
}
