/**
 * 监控项列表项
 */
export interface IMetricCategory {
    /**
     * 监控项标签，如：常用、分区、GC 等
     */
    label: string;
    /**
     * 同标签监控项列表
     */
    metrics: IMetric[];
    [property: string]: any;
}

export type IStatistics = 'average' | 'sum' | 'maximum' | 'minimum';

/**
 * 同标签监控项列表项
 */
export interface IMetric {
    /**
     * 是否默认展示
     */
    defaultDisplay: boolean;
    /**
     * 是否还有子维度
     */
    hasSubDimension: boolean;
    /**
     * 指标名
     */
    name: string;
    /**
     * 指标名中文
     */
    nameCN: string;
    /**
     * 统计方式
     */
    statistics: string;
    /**
     * 指标单位
     */
    unit: string;
    [property: string]: any;
}
