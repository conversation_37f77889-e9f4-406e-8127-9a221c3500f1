/**
 * 指标筛选
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Checkbox, Button, Radio} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz';
import {IMetricCategory, IMetric} from './index.d';
import './index.less';

const klass = 'm-monitor-metrics';

export interface ISelectedMetricsValueMap {
    [key: string]: IMetric['value'][];
}
export interface ISelectedMetricsMap {
    [key: string]: IMetric[];
}

export class MonitorMetrics extends Component {
    static template = html`
        <template>
            <s-dialog title="指标筛选" open="{= open =}" class="${klass}" width="900" on-cancel="onCancel">
                <s-biz-empty s-if="!categoryMetrics.length" actionText="{{''}}" />
                <template s-else>
                    <div class="${klass}__category">
                        <s-radio-group
                            datasource="{{categoryMetrics}}"
                            radioType="button"
                            value="{= category =}"
                        ></s-radio-group>
                    </div>
                    <div class="${klass}__metrics">
                        <div class="${klass}__check-all">
                            <s-checkbox
                                checked="{{isCheckAll}}"
                                label="全选"
                                indeterminate="{{indeterminate}}"
                                on-change="onSelectAll"
                            />
                        </div>
                        <div class="${klass}__metrics-list">
                            <s-checkbox-group value="{{selectedMetrics}}" on-change="onCheckboxChange">
                                <s-checkbox s-for="item, index in metrics" value="{{item.name}}">
                                    <p>{{item.nameCN}}</p>
                                    <p>{{item.name}}</p>
                                </s-checkbox>
                            </s-checkbox-group>
                        </div>
                    </div>
                </template>
                <div slot="footer">
                    <s-button on-click="onCancel">取消</s-button>
                    <s-button skin="primary" on-click="onConfirm" loading="{{loading}}">确定</s-button>
                </div>
            </s-dialog>
        </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-checkbox': Checkbox,
        's-button': Button,
        's-radio-group': Radio.RadioGroup,
        's-biz-empty': Empty,
    };

    static computed = {
        metrics(): IMetric[] {
            const category = this.data.get('category');
            const categoryMetrics = this.data.get('categoryMetrics') as IMetricCategory[];
            return categoryMetrics.find((item) => item.value === category)?.metrics || [];
        },
        selectedMetrics(): IMetric['value'] {
            const selectedMetricsValueMap = this.data.get('selectedMetricsValueMap');
            const category = this.data.get('category');
            return selectedMetricsValueMap[category] || [];
        },
        indeterminate() {
            const metrics = this.data.get('metrics') as IMetric[];
            const selectedMetrics = this.data.get('selectedMetrics') as IMetric['value'][];
            let selectedLen = selectedMetrics.length;
            return selectedLen && selectedLen < metrics.length;
        },
        isCheckAll() {
            const metrics = this.data.get('metrics') as IMetric[];
            const selectedMetrics = this.data.get('selectedMetrics') as IMetric['value'][];
            return selectedMetrics.length === metrics.length;
        },
    };

    initData() {
        return {
            open: true,
            category: '',
            // ---- 首次由挂载弹窗时传入----start
            categoryMetrics: [],
            selectedMetricsValueMap: {},
            // ---- 首次由挂载弹窗时传入----end
        };
    }

    attached() {
        const {categoryMetrics} = this.data.get('');
        this.data.set('category', categoryMetrics[0].value);
    }

    onCheckboxChange(e: {value: IMetric['value']}) {
        const category = this.data.get('category');
        this.data.set(`selectedMetricsValueMap[${category}]`, e.value);
    }

    onSelectAll(e: {value: boolean}) {
        let newSelect: IMetric['value'][] = [];
        const category = this.data.get('category');
        if (e.value) {
            const metrics = this.data.get('metrics') as IMetric[];
            newSelect = metrics.map((item) => item.name);
        }
        this.data.set(`selectedMetricsValueMap.${category}`, newSelect);
    }

    onConfirm() {
        const {selectedMetricsValueMap, categoryMetrics} = this.data.get('') as {
            selectedMetricsValueMap: ISelectedMetricsValueMap;
            categoryMetrics: IMetricCategory[];
        };
        let selectedMetricsMap: ISelectedMetricsMap = {};
        for (const [key, values] of Object.entries(selectedMetricsValueMap)) {
            let keyValue = Number(key);
            !selectedMetricsMap[keyValue] && (selectedMetricsMap[keyValue] = []);

            const keyMetrics = categoryMetrics.find((c) => c.value === keyValue)?.metrics || [];
            values.forEach((n) => {
                const metric = keyMetrics.find((m) => m.name === n);
                metric && selectedMetricsMap[keyValue].push(metric);
            });
        }
        this.fire('confirm', selectedMetricsMap);
        this.onCancel();
    }

    onCancel() {
        this.data.set('open', false);
    }

    detached() {
        this.dispose && this.dispose();
    }
}
