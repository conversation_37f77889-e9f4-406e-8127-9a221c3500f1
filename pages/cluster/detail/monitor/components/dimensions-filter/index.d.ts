/**
 * broker 列表项
 */
export interface IBrokerNode {
    /**
     * 节点组
     */
    brokerName: string;
    /**
     * 节点服务ID
     */
    brokerServerId: string;
    /**
     * 节点虚机ID
     */
    nodeId: string;
    /**
     * 节点状态
     */
    status: string;
    [property: string]: any;
}

export interface ITopicItem {
    /** 主题名称 */
    name: string;
    [property: string]: any;
}

export interface IConsumerGroupItem {
    /** 消费组名 */
    name: string;
    [property: string]: any;
}

export interface IConsumerGroupTopicItem {
    /** 消费组下的主题名称 */
    name: string;
    [property: string]: any;
}

/** 监控类型对应的指标类型：获取监控指标时，传入的type枚举 */
export enum IMetricType {
    /** 集群监控 */
    CLUSTER = 'CLUSTER',
    /** 节点组 */
    BROKER = 'BROKER',
    /** 节点ID */
    NODE = 'NODE',
    /** 主题 */
    TOPIC = 'TOPIC',
    /** 消费组 */
    CONSUMERGROUP = 'CONSUMERGROUP',
    /** 消费组监控-消费组分主题监控 */
    CONSUMERGROUP_TOPIC = 'CONSUMERGROUP',
}

export enum IDimensionItemName {
    ClusterId = 'ClusterId',
    BrokerServerId = 'BrokerServerId',
    InstanceId = 'InstanceId',
    NodeId = 'NodeId',
    BrokerName = 'BrokerName',
    Topic = 'Topic',
    ConsumerGroup = 'ConsumerGroup',
}

export interface IDimensionItem {
    name: IDimensionItemName;
    value: string;
    [property: string]: any;
}

export type IDimensions = IDimensionItem[][];

export type IDimensionDataItem = ISelectItem;

export type IDimensionSelectValue = string[];

export type IGroupDimensionSelectValue = string;

export type IGroupTopicDimensionValue = string[];
