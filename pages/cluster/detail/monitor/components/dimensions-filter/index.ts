/**
 * 监控维度筛选
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Select, Checkbox} from '@baidu/sui';
import {IMonitorType} from '../../filter-area';
import {api} from '@common/client';
import {
    ITopicItem,
    IBrokerNode,
    IConsumerGroupItem,
    IConsumerGroupTopicItem,
    IDimensionItemName,
    IDimensionSelectValue,
    IGroupDimensionSelectValue,
    IGroupTopicDimensionValue,
    IDimensionDataItem,
} from './index.d';

import './index.less';

const klass = 'm-monitor-dimensions-filter';
export class DimensionsFilter extends Component {
    static template = html`
        <div s-if="showSelect" class="${klass}">
            <label class="select-label">{{selectLabel}}</label>
            <s-select
                value="{= selectDimensionValue =}"
                datasource="{{dimensionDatasource}}"
                width="{{340}}"
                multiple="{{monitorType !== IMonitorType.CosumerGroupTopic}}"
                checkAll
                loading="{{loading.dimensionDatasource}}"
                on-change="onSelectChange"
            />
            <div s-if="monitorType === IMonitorType.CosumerGroupTopic" class="consumer-topic-select-wrap">
                <label class="select-label">主题：</label>
                <s-select
                    datasource="{{consumerGroupTopicDatasource}}"
                    checkAll
                    multiple
                    on-change="onConsumerGroupTopicChange"
                    value="{= consumerGroupTopicValue =}"
                />
            </div>
            <s-checkbox label="反选" class="ml8" on-change="onReverseChange"></s-checkbox>
        </div>
    `;

    static components = {
        's-select': Select,
        's-checkbox': Checkbox,
    };

    static computed: SanComputedProps = {
        showSelect() {
            const monitorType = this.data.get('monitorType') as IMonitorType;
            return ![IMonitorType.Cluster].includes(monitorType);
        },
        selectLabel() {
            const monitorType = this.data.get('monitorType') as IMonitorType;
            const labelMap = {
                [IMonitorType.Cluster]: '',
                [IMonitorType.Broker]: '节点组：',
                [IMonitorType.Node]: '节点ID：',
                [IMonitorType.Topic]: '主题：',
                [IMonitorType.Consumergroup]: '消费组：',
                [IMonitorType.CosumerGroupTopic]: '消费组：',
            };

            return labelMap[monitorType];
        },
    };

    initData() {
        return {
            // ---- 组件传入 ---start
            monitorType: '',
            // ---- 组件传入 ---end
            dimensionDatasource: [],
            selectDimensionValue: [],
            IMonitorType,
            consumerGroupTopicDatasource: [],
            consumerGroupTopicValue: [],
        };
    }

    attached() {
        this.watch('monitorType', () => {
            this.getDimensionsSource();
        });
        this.getDimensionsSource();
    }

    /** 请求select数据 */
    async getDimensionsSource() {
        const {monitorType} = this.data.get('') as {
            monitorType: IMonitorType;
        };
        this.data.set('dimensionDatasource', []);
        this.data.set('selectDimensionValue', []);
        try {
            this.data.set('loading.dimensionDatasource', true);
            switch (monitorType) {
                case IMonitorType.Cluster:
                    this.handleDimensionsChange();
                    break;
                case IMonitorType.Broker:
                case IMonitorType.Node:
                    await this.getMonitorBrokerNodes();
                    break;
                case IMonitorType.Topic:
                    await this.getMonitorTopics();
                    break;
                case IMonitorType.Consumergroup:
                    await this.getMonitorConsumerGroups();
                    break;
                case IMonitorType.CosumerGroupTopic:
                    await this.getMonitorConsumerGroups();
                    break;
                default:
                    break;
            }
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('loading.dimensionDatasource', false);
        }
    }

    async getMonitorBrokerNodes() {
        const {clusterId} = this.data.get('route.query');
        const res = (await api.monitorBrokers({clusterId})) as IBrokerNode[];
        const {monitorType} = this.data.get('');
        const isBrokerMonitorType = IMonitorType.Broker === monitorType;
        const data = res?.map((item) => {
            return {
                ...item,
                label: item.brokerServerId,
                value: isBrokerMonitorType ? item.brokerServerId : item.nodeId,
            };
        }) || [];
        this.data.set('dimensionDatasource', data);
        const selectValue = data.map((item) => item.value);
        this.data.set('selectDimensionValue', selectValue);
        this.handleDimensionsChange();
    }
    async getMonitorTopics() {
        const {clusterId} = this.data.get('route.query');
        const res = (await api.monitorTopics({clusterId})) as ITopicItem[];
        const data = res?.map((item) => ({label: item.name, value: item.name})) || [];
        this.data.set('dimensionDatasource', data);
        const selectValue = data.map((item) => item.value);
        this.data.set('selectDimensionValue', selectValue);
        this.handleDimensionsChange();
    }

    async getMonitorConsumerGroups() {
        const {clusterId} = this.data.get('route.query');
        const res = (await api.monitorConsumerGroups({clusterId})) as IConsumerGroupItem[];
        const data = res?.map((item) => ({label: item.name, value: item.name})) || [];
        this.data.set('dimensionDatasource', data);
        let selectValue: IDimensionSelectValue | IGroupDimensionSelectValue = [];
        const monitorType = this.data.get('monitorType');
        if (monitorType === IMonitorType.CosumerGroupTopic) {
            selectValue = data[0]?.value;
            selectValue && this.getMonitorGroupTopics(selectValue);
        } else {
            selectValue = data.map((item) => item.value);
        }
        this.data.set('selectDimensionValue', selectValue);
        if (monitorType === IMonitorType.Consumergroup) {
            this.handleDimensionsChange();
        }
    }

    onSelectChange(e: {value: string | string[]}) {
        this.data.set('selectDimensionValue', e.value);
        const monitorType = this.data.get('monitorType') as IMonitorType;
        if (monitorType === IMonitorType.CosumerGroupTopic) {
            this.getMonitorGroupTopics(e.value as string);
        } else {
            this.handleDimensionsChange();
        }
    }

    async getMonitorGroupTopics(groupName: IConsumerGroupItem['name']) {
        const {clusterId} = this.data.get('route.query');
        const res = (await api.monitorGroupTopics({clusterId, groupName})) as IConsumerGroupTopicItem[];
        const data = res?.map((item) => ({label: item.name, value: item.name})) || [];
        this.data.set('consumerGroupTopicDatasource', data);
        const initGroupTopicValue = data.map((item) => item.value);
        this.data.set('consumerGroupTopicValue', initGroupTopicValue);
        this.handleDimensionsChange();
    }

    onConsumerGroupTopicChange(e: {value: IGroupTopicDimensionValue}) {
        this.data.set('consumerGroupTopicValue', e.value);
        this.handleDimensionsChange();
    }

    onReverseChange(e: {value: boolean}) {
        const {
            dimensionDatasource,
            monitorType,
            consumerGroupTopicDatasource,
            selectDimensionValue,
            consumerGroupTopicValue,
        } = this.data.get('') as {
            dimensionDatasource: IDimensionDataItem[];
            monitorType: IMonitorType;
            consumerGroupTopicDatasource: IConsumerGroupTopicItem[];
            selectDimensionValue: IDimensionSelectValue | IGroupDimensionSelectValue;
            consumerGroupTopicValue: IGroupTopicDimensionValue;
        };
        if (monitorType === IMonitorType.CosumerGroupTopic) {
            let newGroupTopicValues: IGroupTopicDimensionValue = [];
            newGroupTopicValues = consumerGroupTopicDatasource.reduce((prev, current) => {
                !consumerGroupTopicValue.includes(current.value) && prev.push(current.value);
                return prev;
            }, newGroupTopicValues);
            this.data.set('consumerGroupTopicValue', newGroupTopicValues);
        } else {
            let newSelectValues: IDimensionSelectValue = [];
            newSelectValues = dimensionDatasource.reduce((prev, current) => {
                !selectDimensionValue.includes(current.value) && prev.push(current.value);
                return prev;
            }, newSelectValues);
            this.data.set('selectDimensionValue', newSelectValues);
        }
        this.handleDimensionsChange();
    }

    handleDimensionsChange() {
        const {
            dimensionDatasource,
            monitorType,
            consumerGroupTopicDatasource,
            selectDimensionValue,
            consumerGroupTopicValue,
        } = this.data.get('') as {
            dimensionDatasource: IDimensionDataItem[];
            monitorType: IMonitorType;
            consumerGroupTopicDatasource: IDimensionDataItem[];
            selectDimensionValue: IDimensionSelectValue | IGroupDimensionSelectValue;
            consumerGroupTopicValue: IConsumerGroupTopicItem['name'];
        };

        let selectValues = selectDimensionValue;
        let selectDatasource: IDimensionDataItem[] = dimensionDatasource;
        if (monitorType === IMonitorType.CosumerGroupTopic) {
            selectValues = consumerGroupTopicValue;
            selectDatasource = consumerGroupTopicDatasource;
        }

        const legendData = selectDatasource
            .filter((item) => selectValues.includes(item.value))
            .map((item) => item.label);
        // 主机监控legend名称和value不对应，需要新的映射。
        const legendValue = selectDatasource
            .filter((item) => selectValues.includes(item.value))
            .map((item) => item.value);
        this.fire('dimensions-change', {
            monitorType: monitorType,
            dimensions: this.formatDimensions(),
            legendData,
            legendValue
        });
    }

    formatDimensions() {
        const {selectDimensionValue, route, monitorType, dimensionDatasource} = this.data.get('') as {
            selectDimensionValue: string[];
            route: {query: {clusterId: string}};
            monitorType: IMonitorType;
            dimensionDatasource: IDimensionDataItem[];
        };
        const clusterDimension = {
            name: IDimensionItemName.ClusterId,
            value: route.query.clusterId,
        };
        const dimensionsMap = {
            [IMonitorType.Cluster]: IDimensionItemName.ClusterId,
            [IMonitorType.Broker]: IDimensionItemName.BrokerServerId,
            [IMonitorType.Node]: IDimensionItemName.NodeId,
            [IMonitorType.Topic]: IDimensionItemName.Topic,
            [IMonitorType.Consumergroup]: IDimensionItemName.ConsumerGroup,
            [IMonitorType.CosumerGroupTopic]: IDimensionItemName.Topic,
        };
        let dimensions = [];
        if (monitorType === IMonitorType.Cluster) {
            // 集群监控
            dimensions = [[clusterDimension]];
        } else if ([IMonitorType.Broker].includes(monitorType)) {
            // 节点监控——服务监控
            dimensions = (selectDimensionValue as string[])?.map((v) => {
                const selectItem = dimensionDatasource.find((item) => item.value === v);
                return [
                    clusterDimension,
                    {name: IDimensionItemName.BrokerName, value: selectItem!.brokerName},
                    {name: dimensionsMap[monitorType], value: v},
                ];
            });
        } else if (monitorType === IMonitorType.CosumerGroupTopic) {
            // 消费组监控——消费者分主题
            const consumerGroupDimension = {
                name: IDimensionItemName.ConsumerGroup,
                value: selectDimensionValue,
            };

            const {consumerGroupTopicValue} = this.data.get('') as {
                consumerGroupTopicValue: IConsumerGroupTopicItem[];
            };
            dimensions = consumerGroupTopicValue.map((item) => {
                return [
                    clusterDimension,
                    consumerGroupDimension,
                    {
                        name: IDimensionItemName.Topic,
                        value: item,
                    },
                ];
            });
        } else {
            // 节点监控——主机监控
            // 消费组监控——基础监控
            dimensions = (selectDimensionValue as string[])?.map((item) => {
                return [clusterDimension, {name: dimensionsMap[monitorType], value: item}];
            });
        }

        return dimensions;
    }
}
