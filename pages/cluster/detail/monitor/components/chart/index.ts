/**
 * echart图表
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import * as echarts from 'echarts';
import './index.less';

const klass = 'm-chart';
export class Chart extends Component {
    static template = html`
        <template>
            <div s-ref="chartContainer" class="${klass}__content"></div>
        </template>
    `;

    chart!: echarts.ECharts;
    initData() {
        return {
            // --- 由组件传入 ---start
            chartOptions: {},
            // --- 由组件传入 ---end
        };
    }

    static computed = {};

    attached() {
        this.initChart();
        this.watch('chartOptions', () => {
            this.updateChart();
        });
        this.handleResize = this.handleResize.bind(this);
        window.addEventListener('resize', this.handleResize);
    }

    initChart() {
        const refChartContainer = this.ref('chartContainer') as HTMLElement;
        const myChart = echarts.init(refChartContainer);
        this.chart = myChart;
        this.updateChart();
    }

    updateChart() {
        const chartOptions = this.data.get('chartOptions');
        this.chart.setOption(chartOptions);
    }

    handleResize() {
        this.chart.resize();
    }

    dettached() {
        window.removeEventListener('resize', this.handleResize);
    }
}
