/**
 * 图表项
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {SeriesOption} from 'echarts';
import './index.less';
import {Button, Loading, Table, Select} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz';
import {ArrowsAlt} from '@baidu/xicon-san';
import {formatEmpty, formatUtcTime, getBcmMetricCommonParams, isInvalid} from '@common/utils';
import {IMonitorType, IRangeFeTime} from '../../filter-area';
import {formatter, getChartData} from './util';
import {IMetric} from '../../metrics/index.d';
import {ChartCommonOptions} from './config';
import {Chart} from '../chart';
import {IDimensions} from '../dimensions-filter/index.d';
import _ from 'lodash';
import {IChartItemData, IMetricData, IOrderBy, ISubDimension, ISubDimensionListItem, ITableDataItem} from './index.d';
import {api, thirdApi} from '@common/client';

const klass = 'chart-item';
export class ChartItem extends Component {
    static template = html`
        <template>
            <div class="{{hasBorder ? '${klass}__wrap has-border': '${klass}__wrap'}}">
                <div class="${klass}__top" s-if="showTitle || showViewDetail || data.hasSubDimension">
                    <span s-if="showTitle" class="${klass}__title">{{data.nameCN}}</span>
                    <span s-if="showViewDetail" class="${klass}__enlarge">
                        <s-button skin="normal-stringfy" on-click="onViewChartDetail">
                            <x-icon-arrow-alt size="{{18}}" />
                        </s-button>
                    </span>
                </div>
                <s-loading s-if="loading" loading></s-loading>
                <s-biz-empty s-elif="!loading && (!dimensions.length || !metricsData.length)" actionText="" />
                <chart-content s-else s-ref="chartContainer" chartOptions="{{chartOptions}}"></chart-content>
                <s-table
                    s-if="showTable"
                    columns="{{table.columns}}"
                    datasource="{{table.datasource}}"
                    loading="{{loading}}"
                    on-sort="onSort"
                >
                    <div slot="c-name" style="color: {{row.color}}">{{row.name}}</div>
                    <div slot="c-max">{{row.unit | formatterNum(row.statistics, row.max)}}</div>
                    <div slot="c-min">{{row.unit | formatterNum(row.statistics, row.min)}}</div>
                    <div slot="c-avg">{{row.unit | formatterNum(row.statistics, row.avg)}}</div>
                </s-table>
            </div>
        </template>
    `;

    static components = {
        's-button': Button,
        'x-icon-arrow-alt': ArrowsAlt,
        's-select': Select,
        's-loading': Loading,
        'chart-content': Chart,
        's-table': Table,
        's-biz-empty': Empty
    };

    static filters: SanFilterProps = {
        formatterNum(unit: IMetric['unit'], statistics: IMetric['statistics'], num?: number) {
            if (isInvalid(num)) {
                return formatEmpty(num);
            }
            return formatter(unit, statistics, num as number);
        }
    };

    chart!: echarts.ECharts;
    initData() {
        return {
            // --- 由组件传入 ---start
            data: {},
            rangeTime: {},
            selectedMetricsMap: {},
            dimensions: [],
            // --- 由组件传入 ---end
            chartOptions: _.cloneDeep(ChartCommonOptions),
            hasBorder: false,
            loading: false,
            showViewDetail: false,
            showTitle: false,
            showTable: false,
            subDimensions: [],
            subDimensionValues: [],
            legendData: [],
            legendValue: [],
            metricsData: [],
            table: {
                columns: [
                    {
                        name: 'name',
                        label: '监控对象'
                    },
                    {
                        name: 'max',
                        label: 'Max',
                        sortable: true,
                        width: '20%'
                    },
                    {
                        name: 'min',
                        label: 'Min',
                        sortable: true,
                        width: '20%'
                    },
                    {
                        name: 'avg',
                        label: 'Avg',
                        sortable: true,
                        width: '20%'
                    }
                ],
                datasource: []
            }
        };
    }

    static computed = {};

    async attached() {
        const {data} = this.data.get('') as {
            data: IMetric;
        };

        if (data.hasSubDimension) {
            await this.getSubDimensions();
            this.getMetricData();
        }
        this.watch('rangeTime', () => {
            this.getMetricData();
        });
    }

    async getSubDimensions() {
        const {query} = this.data.get('route') as IRoute;
        const data = this.data.get('data') as IMetric;
        const res = (await api.monitorSubDimensions({
            clusterId: query.clusterId,
            params: {
                metricName: data.name
            }
        })) as ISubDimension[];

        // 处理所有子维度组，为每个组的第一个选项设置为默认值
        let initSubDimensionValues: Array<ISubDimensionListItem['value']> = [];
        const subDimensions = (res || []).map((item, index) => {
            initSubDimensionValues[index] = item.dimensionList[0].value;
            return {
                ...item,
                dimensionList: item.dimensionList.map(d => ({...d, label: d.name}))
            };
        });

        this.data.set('subDimensions', subDimensions);
        this.data.set('subDimensionValues', initSubDimensionValues);
    }

    onSubDimensionChange() {
        this.nextTick(() => {
            this.getMetricData();
        });
    }

    async getMetricData() {
        const data = this.data.get('data') as IMetric;
        const {rangeTime, cycle, legendData, monitorType, legendValue, subDimensions} = this.data.get('') as {
            rangeTime: IRangeFeTime;
            cycle: number;
            legendData: string[];
            legendValue: string[];
            monitorType: IMonitorType;
            subDimensions: ISubDimension[];
        };

        const TypeMap = {
            [IMonitorType.Cluster]: 'Cluster',
            [IMonitorType.Broker]: 'Broker',
            [IMonitorType.Node]: 'Node',
            [IMonitorType.Topic]: 'Topic',
            [IMonitorType.Consumergroup]: 'ConsumerGroup',
            [IMonitorType.CosumerGroupTopic]: 'ConsumerGroup'
        };

        this.data.set('loading', true);
        try {
            let allMetrics: IMetricData[] = [];

            // 检查是否有子维度
            if (data.hasSubDimension) {
                const dimensionsList = this.formatDimensionsForSubDimensions();
                let tableColorLength = 0;

                // 为每个子维度组合执行API调用
                const promises = dimensionsList.map(async dimensions => {
                    const params = {
                        scope: 'BCE_ROCKETMQ',
                        ...getBcmMetricCommonParams(),
                        type: TypeMap[monitorType],
                        metricNames: [data.name],
                        startTime: formatUtcTime(rangeTime.begin),
                        endTime: formatUtcTime(rangeTime.end),
                        statistics: [data.statistics],
                        dimensions,
                        cycle
                    };
                    const res = (await thirdApi.bcmMetricData(params)) as {metrics: IMetricData[]};
                    allMetrics = res?.metrics || [];
                    const metricsArr = allMetrics.map(m => ({...m, ...data, subDimensions}));

                    const orderNameMap = {
                        [IMonitorType.Cluster]: 'Cluster',
                        [IMonitorType.Broker]: 'BrokerServerId',
                        [IMonitorType.Node]: 'NodeId',
                        [IMonitorType.Topic]: 'Topic',
                        [IMonitorType.Consumergroup]: 'ConsumerGroup',
                        [IMonitorType.CosumerGroupTopic]: 'Topic'
                    };
                    const chartData = getChartData(
                        metricsArr,
                        legendData,
                        orderNameMap[monitorType],
                        legendValue,
                        tableColorLength
                    );
                    tableColorLength += metricsArr.length;
                    return chartData;
                });

                const results = await Promise.all(promises);
                const chartData = this.mergeIntoFirstFromArray(results);
                console.log('aw', chartData);
                this.data.set('metricsData', allMetrics);
                this.data.set('chartOptions', chartData.chartOptions);
                this.data.set('table.datasource', chartData.tableDatasource);
            }
            else {
                // 没有子维度的情况，使用原有逻辑
                const dimensions = this.formatDimensions();
                if (!dimensions.length) {
                    return;
                }

                const params = {
                    scope: 'BCE_ROCKETMQ',
                    ...getBcmMetricCommonParams(),
                    type: TypeMap[monitorType],
                    metricNames: [data.name],
                    startTime: formatUtcTime(rangeTime.begin),
                    endTime: formatUtcTime(rangeTime.end),
                    statistics: [data.statistics],
                    dimensions,
                    cycle
                };
                const res = (await thirdApi.bcmMetricData(params)) as {metrics: IMetricData[]};
                allMetrics = res?.metrics || [];
                const metricsArr = allMetrics.map(m => ({...m, ...data}));

                const orderNameMap = {
                    [IMonitorType.Cluster]: 'Cluster',
                    [IMonitorType.Broker]: 'BrokerServerId',
                    [IMonitorType.Node]: 'NodeId',
                    [IMonitorType.Topic]: 'Topic',
                    [IMonitorType.Consumergroup]: 'ConsumerGroup',
                    [IMonitorType.CosumerGroupTopic]: 'Topic'
                };
                const chartData = getChartData(metricsArr, legendData, orderNameMap[monitorType], legendValue);

                this.data.set('metricsData', allMetrics);
                this.data.set('chartOptions', chartData.chartOptions);
                this.data.set('table.datasource', chartData.tableDatasource);
            }
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('loading', false);
        }
    }

    formatDimensions() {
        const {data, dimensions, subDimensions, subDimensionValues} = this.data.get('') as {
            data: IChartItemData;
            dimensions: IDimensions;
            subDimensions: ISubDimension[];
            subDimensionValues: Array<ISubDimensionListItem['value']>;
        };
        if (!data.hasSubDimension || !subDimensionValues.length) {
            return dimensions;
        }

        const subDimensionsArr = subDimensionValues.map((item, index) => {
            const subDimension = subDimensions[index];
            return {name: subDimension.dimensionName, value: item};
        });
        return dimensions.map(d => {
            return [...d, ...subDimensionsArr];
        });
    }

    /**
     * 为子维度生成多个dimensions数组，每个数组包含一个子维度值组合
     */
    formatDimensionsForSubDimensions(): IDimensions[] {
        const {dimensions, subDimensions} = this.data.get('') as {
            dimensions: IDimensions;
            subDimensions: ISubDimension[];
        };

        if (!subDimensions.length) {
            return [dimensions];
        }
        const result: IDimensions[] = [];

        // 生成所有子维度组合
        const generateCombinations = (index: number, currentCombination: any[]) => {
            if (index >= subDimensions.length) {
                // 为每个原始dimension添加当前子维度组合
                const newDimensions = dimensions.map(d => {
                    return [...d, ...currentCombination];
                });
                result.push(newDimensions);
                return;
            }

            const currentSubDimension = subDimensions[index];
            for (const item of currentSubDimension.dimensionList) {
                const newCombination = [
                    ...currentCombination,
                    {name: currentSubDimension.dimensionName as any, value: item.value, subName: item.label}
                ];
                generateCombinations(index + 1, newCombination);
            }
        };

        generateCombinations(0, []);
        return result;
    }

    mergeIntoFirstFromArray(opts: any[]) {
        if (!Array.isArray(opts) || opts.length === 0) {
            return {};
        }

        const res = JSON.parse(JSON.stringify(opts[0]));

        for (let i = 1; i < opts.length; i++) {
            const table = opts[i].tableDatasource ?? {};
            const curr = opts[i].chartOptions ?? {};

            // 合并 series
            const secondSeries = curr.series ?? [];
            res.chartOptions.series = [
                ...(res.chartOptions.series ?? []),
                ...secondSeries.map((item: any) => ({...item}))
            ];

            // 合并 legend.data
            const firstLegendData = res.chartOptions.legend?.data ?? [];
            const secondLegendData = curr.legend?.data ?? [];
            res.chartOptions.legend = {
                ...res.chartOptions.legend,
                data: [...firstLegendData, ...secondLegendData],
            };

            // 合并 tableDatasource
            res.tableDatasource = [...res.tableDatasource, ...table];

        }

        return res;
    }

    /**
     * 放大弹窗
     */
    onViewChartDetail() {
        const {data, selectedMetricsMap, chartOptions, subDimensionValues} = this.data.get('');
        this.fire('view-detail', {
            data,
            selectedMetricsMap,
            chartOptions,
            subDimensionValues
        });
    }

    /** 改变断点连接 */
    handleConnectNullsChange(value: boolean) {
        let newChartOptions = _.cloneDeep(this.data.get('chartOptions'));
        newChartOptions.series = (newChartOptions.series || []).map((item: SeriesOption) => {
            return {
                ...item,
                connectNulls: value
            };
        });
        this.data.set('chartOptions', newChartOptions);
    }

    onSort(e: {value: {orderBy: IOrderBy; order: 'desc' | 'asc'}}) {
        const {orderBy, order} = e.value;
        this.data.set('orderData', {
            orderBy,
            order
        });
        const datasource = _.cloneDeep(this.data.get('table.datasource')) as ITableDataItem[];
        datasource.sort((a, b) => {
            const x = a[orderBy] || 0;
            const y = b[orderBy] || 0;
            if (order === 'desc') {
                return x - y;
            } else {
                return y - x;
            }
        });
        this.nextTick(() => {
            this.data.set('table.datasource', datasource);
        });
    }
}
