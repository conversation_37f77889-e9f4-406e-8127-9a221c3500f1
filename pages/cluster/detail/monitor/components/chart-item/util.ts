import _ from 'lodash';
import {deepExtend, formatEmpty, formatTime, isInvalid} from '@common/utils';
import {EChartsOption} from 'echarts';
import {ChartCommonOptions, Color} from './config';
import {IMetricData, IStatistics, ITableDataItem} from './index.d';
import {IMetric} from '../../metrics/index.d';
const kByteUnit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB', 'BB'];
const kBitUnit = ['', 'k', 'm', 'g', 't', 'p', 'e', 'z', 'y', 'b'];

export const isByteUnit = (value: string) => {
    return _.indexOf(kByteUnit, value) !== -1;
};

export const percent = (value: string | number, precision: number) => {
    return Number(value).toFixed(precision) + '%';
};

export const bytes = (value: string | number, precision?: number, byteUnit?: number, unit?: IMetric['unit']) => {
    precision = precision ?? 2;
    byteUnit = byteUnit ?? 1024;
    let idx = unit ? _.indexOf(kByteUnit, unit) : 0;
    let len = kByteUnit.length - 1;
    value = Number(value);
    while (value >= byteUnit && idx < len) {
        value = value / byteUnit;
        idx++;
    }
    return Number(value).toFixed(precision) + kByteUnit[idx];
};

export const bits = (value: string | number, precision = 2, bitUnit = 1000) => {
    let idx = 0;
    let len = kBitUnit.length - 1;
    value = Number(value);
    while (value >= bitUnit && idx < len) {
        value = value / bitUnit;
        idx++;
    }
    return Number(value).toFixed(precision) + kBitUnit[idx];
};

export const number = (value: string | number, precision = 1): string => {
    value = Number(value);
    if (value < 10000) {
        return value + '';
    }
    // 15000、20000，当number为0的时候，都是2万
    // 所以需要判断一下value是否能整除，不能整除的至少保留一位小数
    else if (value < 1000000) {
        return (value / 10000).toFixed(value % 10000 === 0 ? precision : Math.max(1, precision)) + '万';
    } else if (value < 10000000) {
        return (value / 1000000).toFixed(value % 1000000 === 0 ? precision : Math.max(1, precision)) + '百万';
    }
    return (value / 10000000.0).toFixed(value % 10000000 === 0 ? precision : Math.max(1, precision)) + '千万';
};

/**
 * 处理数值
 * @param unit
 * @param statistics
 * @param value
 * @param byteUnit
 * @param precision
 * @param upperCaseUnit
 * @returns
 */
export const formatter = (
    unit: IMetric['unit'],
    statistics: IMetric['statistics'],
    value: number,
    byteUnit = 1024,
    precision = 2,
    upperCaseUnit = false
) => {
    unit = unit.toLowerCase();
    if (statistics === 'sampleCount') {
        return upperCaseUnit ? number(value).toUpperCase() : number(value);
    } else if (unit === 'enum') {
        return value;
    } else if (
        unit === '字节' ||
        unit === '字节/秒' ||
        unit === 'bytes' ||
        unit === 'bytes/s' ||
        unit === 'byte' ||
        unit === 'byte/s'
    ) {
        const bytesStr = bytes(value, precision);
        return upperCaseUnit ? bytesStr.toUpperCase() : bytesStr;
    } else if (unit === 'bps') {
        const bitsStr = bits(value, precision);
        return upperCaseUnit ? bitsStr.toUpperCase() : bitsStr;
    } else if (unit === '百分比' || unit === '%') {
        const percentStr = percent(value, precision);
        return upperCaseUnit ? percentStr.toUpperCase() : percentStr;
    } else if (isByteUnit(unit)) {
        const bytesStr = bytes(value, precision, byteUnit, unit);
        return upperCaseUnit ? bytesStr.toUpperCase() : bytesStr;
    } else {
        return Number(value).toFixed(precision);
    }
};

export const getYAxis = (metric: IMetricData & IMetric): Pick<EChartsOption, 'yAxis'> => {
    let yAxisFormatter;
    let yAxisUnit = '';
    const {unit, precision} = metric;
    const isPercent = unit === '%' || unit === '百分比';
    // 对于不同单位的处理
    if (metric.statistics === 'sampleCount') {
        yAxisUnit = '个';
    } else if (
        unit === '字节' ||
        unit === '字节/秒' ||
        unit === 'bytes' ||
        unit === 'bytes/s' ||
        unit === 'byte' ||
        unit === 'byte/s'
    ) {
        const type = ['字节', 'bytes', 'byte'];
        let suffix = _.includes(type, unit) ? '' : '/s';
        yAxisFormatter = function (value: number) {
            let prefix = value < 0 ? formatEmpty('') : '';
            value = Math.abs(value);
            const newValue = bytes(value, 1);
            return prefix + newValue + suffix;
        };
    } else if (unit === 'bps') {
        yAxisFormatter = function (value: number) {
            let prefix = value < 0 ? formatEmpty('') : '';
            value = Math.abs(value);
            // 保留1位小数的话
            // 当纵坐标是0 0.125 0.15 0.175 0.2的时候
            // 就会出现三个0.1
            const newValue = bits(value, precision, metric.bitUnit).toUpperCase();
            return prefix + newValue;
        };
    } else if (unit === '百分比') {
        yAxisFormatter = '{value}%';
    } else {
        yAxisFormatter = function (value: number) {
            return number(value, metric.precision);
        };
    }

    return {
        yAxis: {
            type: 'value',
            name: `[${metric.unit}]`,
            nameTextStyle: {
                color: '#84868C',
                fontSize: 12,
                align: 'left'
            },
            max: isPercent ? 100 : undefined,
            axisLabel: {
                formatter: yAxisFormatter
            }
        }
    };
};

export const getTooltipOptions = (metric: IMetricData & IMetric): Pick<EChartsOption, 'tooltip'> => {
    const {unit} = metric;
    console.log('???si', unit);
    const tooltipFormatter = (params: any) => {
        let itemString = '';
        let time = '';
        if (params.length > 0) {
            time = `<span style="color: #5C5F66;">${params[0].axisValueLabel}</span>`;
        }
        _.each(params, (item, index) => {
            let value = item.value;
            let valueStr = '';
            let prefix;
            if (isNaN(value)) {
                valueStr = formatEmpty('');
            } else {
                prefix = value < 0 ? formatEmpty('') : '';
                value = Math.abs(value);
                valueStr = prefix + formatter(unit, metric.statistics, value);
                console.log('???', valueStr);
            }
            itemString += `${index ? '</br>' : ''}
                <span style="color:${item.color};
                    display: inline-block;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    vertical-align:middle;
                    white-space: nowrap">
                    ${item.seriesName}：
                </span>
                <span style="color: #151B26;">${valueStr}</span>`;
        });
        const containerStart = '<div style="max-height:190px;overflow:auto;">';
        const containerEnd = '</div>';
        return time + containerStart + itemString + containerEnd;
    };
    return {
        tooltip: {
            trigger: 'axis',
            formatter: tooltipFormatter
        }
    };
};

/**
 *
 * @param data 指标数据
 * @param customChartOptions 自定义echart图标参数
 * @returns
 */
export const getChartData = (
    data: Array<IMetricData & IMetric>,
    legendData: string[],
    orderName: string,
    legendValue?: string[],
    tableColorLength?: number
): {
    /** echart所需参数 */
    chartOptions: EChartsOption;
    /** 表格数据 */
    tableDatasource: any[];
} => {
    let xAxisData: string[] = [];
    let seriesData: EChartsOption['series'] = [];
    let tableDatasource: ITableDataItem[] = [];

    const legendLen = legendData?.length;

    // 创建一个新的legendData为添加标签等修改做处理
    const legendNewData = data.map((item, index) => {
        const base = legendData?.[index] ?? '';
        if (item?.hasSubDimension) {
            const dim = item.dimensions?.[1];
            const subList = item.subDimensions?.find((d: any) => d.dimensionName === dim?.name)?.dimensionList;
            const subName = subList?.find((d: any) => d.value === dim?.value)?.label;
            return base + (subName ? '(' + subName + ')' : '');
        }
        return base;
    });
    // 创建一个 Map 来存储 legendData 的顺序索引
    const legendOrder = new Map<string, number>();
    // 需求需要改nodeid为broker标签，暂时对NodeId进行特殊处理
    if (orderName === 'NodeId') {
        legendValue?.forEach((item, index) => {
            legendOrder.set(item, index);
        });
    }
    else {
        legendData.forEach((item, index) => {
            legendOrder.set(item, index);
        });
    }
    data.sort((a, b) => {
        const orderAValue = a.dimensions.find(item => item.name === orderName)?.value;
        const orderBValue = b.dimensions.find(item => item.name === orderName)?.value;

        // 默认值为 Infinity（未在 legendData 中的项放在最后）
        const orderA = orderAValue ? (legendOrder.get(orderAValue) as number) : Infinity;
        const orderB = orderBValue ? (legendOrder.get(orderBValue) as number) : Infinity;
        return orderA - orderB;
    });
    data.forEach((item, index) => {
        // 包含有效数据 & 无效数据
        let seriesItemData: Array<number | undefined> = [];
        // 有效数据
        let validData: number[] = [];
        item.dataPoints?.forEach(d => {
            if (index === 0) {
                const localTime = formatTime(d.timestamp, 'MM-DD HH:mm') as string;
                xAxisData.push(localTime);
            }
            const value = d[item.statistics as IStatistics];
            seriesItemData.push(value);
            if (!isInvalid(value)) {
                validData.push(value as number);
            }
        });
        const legendName = legendNewData[index];
        seriesData.push({
            type: 'line',
            showSymbol: false,
            name: legendLen ? legendName : item.nameCN,
            data: seriesItemData,
            connectNulls: false
        });

        const validLen = validData.length;
        const max = validLen ? _.max(validData) : void 0;
        const min = validLen ? _.min(validData) : void 0;
        const avg = +(_.sum(validData) / validLen).toFixed(2);

        tableDatasource.push({
            name: legendLen > 1 ? legendName : item.metricName,
            max,
            min,
            avg,
            unit: item.unit,
            statistics: item.statistics,
            color: Color[(index + (tableColorLength ?? 0)) % Color.length]
        });
    });
    let chartOptions = _.cloneDeep(ChartCommonOptions);
    const firstData = data[0];
    console.log('555', firstData, getTooltipOptions(firstData));

    let extendChartOptions: EChartsOption = {
        xAxis: {
            data: xAxisData
        },
        series: seriesData,
        ...getYAxis(firstData),
        ...getTooltipOptions(firstData)
    };
    if (legendLen) {
        extendChartOptions.legend = {
            top: 0,
            right: '16px',
            itemHeight: 2,
            itemWidth: 12,
            icon: 'rect',
            scrollDataIndex: 0,
            orient: 'horizontal',
            type: 'scroll',
            pageIconSize: 8,
            data: legendNewData
        };
    }
    deepExtend(chartOptions, extendChartOptions);
    return {
        chartOptions,
        tableDatasource
    };
};
