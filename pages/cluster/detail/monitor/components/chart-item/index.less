.chart-item {
    &__wrap {
        position: relative;
        width: 100%;
        height: 100%;
        border-radius: 6px;
        display: inline-flex;
        flex-direction: column;

        .s-loading {
            width: 100%;
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        chart-content {
            flex: 1;
        }

        &.has-border {
            border: 1px solid var(--border-color);
        }

        .s-empty {
            flex: 1;
        }
    }

    &__top {
        display: flex;
        padding: 16px 16px 0;
        align-items: center;
    }

    &__title {
        font-size: 14px;
        font-weight: 500;
        margin-right: auto;
    }

    &__sub-dimensions {
        margin-left: auto;

        &:not(:last-child) {
            margin-right: 8px;
        }
    }

    &__enlarge {
        z-index: 1;
        margin-left: 8px;

        .s-button.s-button-skin-normal-stringfy {
            padding: 0;
        }
    }
}
