import {IMetric} from '../../metrics/index.d';

export type IStatistics = 'average' | 'sum' | 'maximum' | 'minimum';

export interface IMetricData {
    region: string;
    scope: string;
    userId: string;
    resourceId: string;
    metricName: string;
    dimensions: {
        name: string;
        value: string;
    }[];
    dataPoints: Array<{
        average?: number;
        sum?: number;
        maximum?: number;
        minimum?: number;
        timestamp: string;
    }>;
}

export interface ITableDataItem {
    name: string;
    max?: number;
    min?: number;
    avg?: number;
    unit: string;
    statistics: string;
    color: string;
}

export interface ISubDimension {
    /**
     * 维度可选值，比如diskId对应的磁盘ID列表，interfaceId对应的
     */
    dimensionList: ISubDimensionListItem[];
    /**
     * 维度名，维度名，比如diskId、interfaceId等
     */
    dimensionName: string;
    [property: string]: any;
}

/**
 * 维度可选值
 */
export interface ISubDimensionListItem {
    /**
     * 值描述，比如数据盘
     */
    name: string;
    /**
     * 值，比如vda
     */
    value: string;
    [property: string]: any;
}

/** chart图标data */
export interface IChartItemData extends IMetric {
    subDimensions: ISubDimension[];
}

export type IOrderBy = 'max' | 'min' | 'avg';
