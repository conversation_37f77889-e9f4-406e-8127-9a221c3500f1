import {EChartsOption} from 'echarts';

const handleIcon =
    'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAIAQMAAAD6NPz1AAAAAXNSR0IB2cksfwAAAAZQTFRFJGjy////vMk6XQAAABNJREFUeJxjZGBgBCKhdwgEFgEALswECUHouoYAAAAASUVORK5CYII=';

export const Color = [
    '#2468F2',
    '#77D160',
    '#E9B21A',
    '#FAD6D7',
    '#0BA69B',
    '#A247CC',
    '#63A60B',
    '#BF870D',
    '#9781FA',
    '#5ED1CF'
];

export const ChartCommonOptions: EChartsOption = {
    color: Color,
    tooltip: {
        trigger: 'axis'
    },
    grid: {
        left: 46,
        right: 46,
        containLabel: true
    },
    yAxis: {
        type: 'value'
    },
    dataZoom: [
        {
            start: 0,
            end: 100,
            height: 8,
            backgroundColor: '#F2F2F3',
            moveHandleStyle: {
                color: 'rgab(0,0,0,0)'
            },
            fillerColor: '#D3E0FC',
            borderColor: 'transparent',
            emphasis: {
                moveHandleStyle: {
                    color: 'rgab(0,0,0,0)'
                }
            },
            handleSize: '105%',
            handleIcon
        }
    ],
    xAxis: {
        type: 'category',
        boundaryGap: false,
        data: [],
        offset: 12
    },
    series: [{type: 'line', data: []}]
};
