/**
 * 用户管理列表
 * @file index.ts
 * <AUTHOR>
 */
import {html} from '@baiducloud/runtime';
import {BlockBox, CommonTable, CreateBtn, EllipsisTip, RefreshBtn, SingleDialog} from '@components/index';
import {Search, Table, Pagination, Button, Menu, Dropdown, Notification} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz';
import {api} from '@common/client';
import {PAGER_SUI, PAGINATION_LAYOUT, TABLE_SUI} from '@common/config';
import {formatEmpty, formatGroupPerm, formatTopicPerm, isClusterStatusDisabled, pickEmpty} from '@common/utils';
import './index.less';
import {OutlinedDown} from '@baidu/sui-icon';
import {IAclUserListItem} from './index.d';
import ResetSecretKey from '../reset-secret-key';
import CreateUser from '../create';
import {PermType} from '../permission-drawer/index.d';
import UserAclPermissionDrawer from '../permission-drawer';
import {IClusterDetail} from '../../index.d';

const klass = 'm-detail-user-acl-list';

export default class UserAclList extends CommonTable {
    static template = html`
        <template>
            <block-box title="用户管理" class="${klass}">
                <div class="${klass}__filter flex mb16">
                    <create-btn on-click="onCreateUser" disabled="{{operationDisabled}}">创建用户</create-btn>
                    <s-search
                        class="${klass}__search-box"
                        value="{= searchbox.keyword =}"
                        on-search="onSearch"
                        width="170"
                        placeholder="请输入用户名进行搜索"
                        clearable
                    />
                    <refresh-btn on-click="onRefresh" class="ml8" />
                </div>
                <s-table
                    columns="{{table.columns}}"
                    datasource="{{table.datasource}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                >
                    <div slot="c-whiteRemoteAddress">
                        <ellipsis-tip
                            s-if="row.whiteRemoteAddress.length"
                            text="{{row.whiteRemoteAddress | filterWhiteRemoteAddress}}"
                            placement="top"
                        />
                        <div s-else>{{'' | formatEmpty}}</div>
                    </div>
                    <div slot="c-admin">{{row.admin ? '是' : '否'}}</div>
                    <div slot="c-defaultTopicPerm">{{row.defaultTopicPerm | formatTopicPerm}}</div>
                    <div slot="c-defaultGroupPerm">{{row.defaultGroupPerm | formatGroupPerm}}</div>
                    <div slot="empty">
                        <s-biz-empty
                            vertical
                            emptyTitle="暂无数据"
                            emptyText="{{table.emptyText}}"
                            actionText="创建用户"
                        >
                            <create-btn
                                slot="action"
                                on-click="onCreateUser"
                                skin="stringfy"
                                disabled="{{operationDisabled}}"
                            >
                                创建用户
                            </create-btn>
                        </s-biz-empty>
                    </div>
                    <div slot="c-actions">
                        <s-button skin="stringfy" on-click="onEdit(row)" disabled="{{operationDisabled}}">
                            编辑
                        </s-button>
                        <s-button skin="stringfy" on-click="onDelete(row)" disabled="{{operationDisabled}}">
                            删除
                        </s-button>
                        <s-dropdown class="${klass}__action-dropdown">
                            <s-menu slot="overlay">
                                <s-menu-item>
                                    <s-button
                                        skin="normal-stringfy"
                                        on-click="onPermission($event, PermType.Topic, row)"
                                        disabled="{{operationDisabled}}"
                                    >
                                        主题权限
                                    </s-button>
                                </s-menu-item>
                                <s-menu-item>
                                    <s-button
                                        skin="normal-stringfy"
                                        on-click="onPermission($event, PermType.Consumer, row)"
                                        disabled="{{operationDisabled}}"
                                    >
                                        消费组权限
                                    </s-button>
                                </s-menu-item>
                                <s-menu-item>
                                    <s-button
                                        skin="normal-stringfy"
                                        on-click="onResetSecretKey(row)"
                                        disabled="{{operationDisabled}}"
                                    >
                                        重置密码
                                    </s-button>
                                </s-menu-item>
                            </s-menu>
                            <s-button skin="stringfy">更多 <s-icon-down /></s-button>
                        </s-dropdown>
                    </div>
                </s-table>
                <s-pagination
                    class="${klass}__pager mt16"
                    s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                    layout="${PAGINATION_LAYOUT}"
                    total="{{pager.count}}"
                    pageSize="{{pager.pageSize}}"
                    page="{{pager.page}}"
                    pageSizes="{{pager.pageSizes}}"
                    max-item="{{7}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange"
                />
            </block-box>
            <x-single-dialog s-ref="singleDialog" />
        </template>
    `;
    static components = {
        'block-box': BlockBox,
        'create-btn': CreateBtn,
        's-search': Search,
        'refresh-btn': RefreshBtn,
        's-table': Table,
        's-pagination': Pagination,
        's-biz-empty': Empty,
        'ellipsis-tip': EllipsisTip,
        's-button': Button,
        's-dropdown': Dropdown,
        's-menu': Menu,
        's-menu-item': Menu.Item,
        's-icon-down': OutlinedDown,
        'x-single-dialog': SingleDialog
    };

    static filters = {
        formatTopicPerm,
        formatGroupPerm,
        filterWhiteRemoteAddress(whiteRemoteAddress: IAclUserListItem['whiteRemoteAddress']) {
            return whiteRemoteAddress.join('，');
        },
        formatEmpty
    };

    static computed = {
        operationDisabled() {
            const detail = this.data.get('detail') as IClusterDetail;
            return isClusterStatusDisabled(detail);
        }
    };

    initData() {
        return {
            searchbox: {
                keyword: ''
            },
            table: {
                ...TABLE_SUI,
                columns: [
                    {name: 'accessKey', label: '用户名'},
                    {name: 'whiteRemoteAddress', label: 'IP白名单'},
                    {name: 'admin', label: '是否为管理员'},
                    {name: 'defaultTopicPerm', label: '默认主题权限'},
                    {name: 'defaultGroupPerm', label: '默认消费组权限'},
                    {name: 'actions', label: '操作'}
                ]
            },
            pager: {...PAGER_SUI},
            PermType
        };
    }

    attached() {
        this.getComList();
    }

    async getTableList() {
        const {searchbox, pager, route} = this.data.get('');
        const {clusterId} = route.query;
        const params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            keywordType: 'accessKey',
            keyword: searchbox.keyword
        });

        const res = (await api.aclUserList({
            clusterId,
            params
        })) as ListPage<IAclUserListItem>;
        const {totalCount, result} = res;
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', result || []);
        const emptyStr = searchbox.keyword ? '暂无符合条件的用户，您可以尝试更换筛选条件。' : '您还没有创建任何用户';
        this.data.set('table.emptyText', emptyStr);
    }

    onDelete(row: IAclUserListItem) {
        const {query} = this.data.get('route') as IRoute;
        const tipStr = `删除后数据将无法恢复，请谨慎操作，确认删除用户<br>${row.accessKey}吗?`;
        this.ref('singleDialog').open('删除用户', tipStr, async () => {
            await api.deleteAclUser({
                clusterId: query.clusterId,
                accessKey: row.accessKey
            });
            Notification.success(`用户${row.accessKey}删除成功`);
            this.onRefresh();
        });
    }

    onResetSecretKey(row: IAclUserListItem) {
        const {query} = this.data.get('route') as IRoute;
        const dialog = new ResetSecretKey({
            data: {
                clusterId: query.clusterId,
                row
            }
        });
        dialog.attach(document.body);
    }
    onCreateUser() {
        const route = this.data.get('route') as IRoute;
        const createuser = new CreateUser({
            data: {
                route
            }
        });
        createuser.on('success', () => {
            this.onRefresh();
        });
        createuser.attach(document.body);
    }
    onEdit(row: IAclUserListItem) {
        const route = this.data.get('route') as IRoute;
        const edituser = new CreateUser({
            data: {
                row,
                route
            }
        });
        edituser.on('success', () => {
            this.onRefresh();
        });
        edituser.attach(document.body);
    }

    onPermission(e: Event, type: PermType, row: IAclUserListItem) {
        e && e.stopPropagation();
        const {route} = this.data.get('');
        const {clusterId} = route.query;
        const drawer = new UserAclPermissionDrawer({
            data: {
                type,
                detail: row,
                clusterId
            }
        });

        drawer.attach(document.body);
    }
}
