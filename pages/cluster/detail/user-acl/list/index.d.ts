/**
 * AclUserListView
 */
export interface IAclUserListItem {
    /**
     * 用户名，用户名
     */
    accessKey: string;
    /**
     * 是否为管理员，是否为管理员
     */
    admin: boolean;
    /**
     * 默认消费组权限，默认消费组权限
     */
    defaultGroupPerm: IPermission;
    /**
     * 默认主题权限，默认主题权限
     */
    defaultTopicPerm: IPermission;
    /**
     * IP白名单列表，IP白名单列表
     */
    whiteRemoteAddress: string[];
    [property: string]: any;
}

/** 权限枚举 */
export enum IPermission {
    Deny = 'DENY',
    Pub = 'PUB',
    PubSub = 'PUB_SUB',
    Sub = 'SUB'
}
