import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Form, Select, Radio, Notification} from '@baidu/sui';
import './index.less';
import {PermType, IAclPermissionListItem, PermUpdateType} from '../index.d';
import {PermTypeInfo} from '..';
import {ConsumerGroupPerssionArr, TopicPerssionArr} from '@common/enums/biz';
import {TopicListProps} from '@pages/cluster/detail/topic/index.d';
import {ConsumerListProps} from '@pages/cluster/detail/consumer/consumer-list';
import {api} from '@common/client';

import './index.less';

const klass = 'm-detail-user-acl-perm-new';
export default class UserAclNewPermDialog extends Component {
    static template = html`
        <template>
            <s-dialog
                class="${klass} {{type}}"
                title="添加{{text}}权限"
                width="{{600}}"
                open="{{open}}"
                confirming="{{confirming}}"
                on-confirm="onConfirm"
                on-close="onClose"
            >
                <s-form s-ref="form" data="{=formData=}" label-align="left" rules="{{rules}}">
                    <s-form-item prop="accessKey" label="用户名：">{{formData.accessKey}}</s-form-item>
                    <s-form-item prop="names" label="{{text}}名称：">
                        <s-select
                            loading="{{loading}}"
                            datasource="{{nameList}}"
                            multiple
                            value="{=formData.names=}"
                            taggable
                            width="{{type === PermType.Topic ? 476 : 464}}"
                            getPopupContainer="{{getPopupContainer}}"
                        />
                    </s-form-item>
                    <s-form-item prop="policy" label="{{text}}权限：">
                        <s-radio-group
                            datasource="{{radioGroupDataSource}}"
                            value="{=formData.policy=}"
                            radioType="button"
                        >
                    </s-form-item>
                </s-form>
            </s-dialog>
        </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-select': Select,
        's-radio-group': Radio.RadioGroup
    };

    initData() {
        return {
            formData: {
                accessKey: '',
                names: [],
                policy: ''
            },
            rules: {
                names: [
                    {
                        validator: (rule: any, value: string, callback: (text?: string) => void) => {
                            if (value.length < 1) {
                                return callback(`请选择${this.data.get('text')}`);
                            }
                            callback();
                        }
                    }
                ]
            },
            open: true,
            loading: true,
            nameList: [],
            confirming: false,
            getPopupContainer: () => document.body,
            radioGroupDataSource: [],
            PermType
        };
    }

    attached() {
        this.initNameList();
        this.initFormData();

        // 因为是在弹窗里的 因此需要阻止下事件冒泡
        const drawerNode = document.querySelector('.m-detail-user-acl-perm-new');
        if (drawerNode) {
            drawerNode.addEventListener('click', (e: any) => {
                const event = e || window.event;
                event.stopPropagation();
            });
        }
    }

    initFormData() {
        const {type} = this.data.get('');
        const radioGroup = type === PermType.Topic ? TopicPerssionArr : ConsumerGroupPerssionArr;
        this.data.set('radioGroupDataSource', radioGroup);
        this.data.set('formData.policy', radioGroup[0].value);
    }

    async initNameList() {
        this.data.set('loading', true);
        const {type, clusterId, detail} = this.data.get('');
        const enumItem = PermTypeInfo.fromValue(type);
        const [originList, permList] = await Promise.all([
            api[enumItem.listOrigninApi]({
                clusterId,
                params: {
                    pageNo: 1,
                    pageSize: 10000
                }
            }),
            api[enumItem.listPermApi]({
                params: {
                    pageNo: 1,
                    pageSize: 10000
                },
                clusterId,
                accessKey: detail.accessKey
            })
        ]);
        const premMap = permList.result.reduce((res: {[key in string]: boolean}, item: IAclPermissionListItem) => {
            res[item.name] = true;
            return res;
        }, {});
        this.data.set(
            'nameList',
            originList.result.reduce(
                (res: Array<{label: string; value: string}>, item: TopicListProps & ConsumerListProps) => {
                    const currentName = item.topicName ?? item.groupName;
                    if (!premMap[currentName]) {
                        res.push({
                            value: currentName,
                            label: currentName
                        });
                    }
                    return res;
                },
                []
            )
        );
        this.data.set('loading', false);
    }
    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }

    async onConfirm() {
        try {
            this.data.set('confirming', true);
            await this.ref('form').validateFields();
            const {clusterId, formData, type, text} = this.data.get('');
            const enumItem = PermTypeInfo.fromValue(type);
            await api[enumItem.updateApi]({
                clusterId,
                params: formData.names.map((item: string) => ({
                    name: item,
                    policy: formData.policy,
                    action: PermUpdateType.Add
                })),
                accessKey: formData.accessKey
            });
            Notification.success(`添加${text}权限成功`);
            this.onClose();
            this.fire('success', {});
        } catch (error) {
        } finally {
            this.data.set('confirming', false);
        }
    }
}
