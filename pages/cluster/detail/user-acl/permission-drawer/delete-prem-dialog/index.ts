import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Alert, Table, Notification} from '@baidu/sui';
import {api} from '@common/client';
import {PermTypeInfo} from '..';
const klass = 'm-detail-user-acl-perm-del';

export class UserAclDeletePermDialog extends Component {
    static template = html`
        <template>
            <s-dialog
                class="${klass}"
                title="删除{{text}}权限"
                width="{{800}}"
                open="{{true}}"
                on-confirm="onConfirm"
                on-close="onClose"
            >
                <s-alert class="mb16" skin="warning">
                    删除后数据将无法恢复，请谨慎操作，确认删除以下 {{datasource.length}} 个{{text}}权限吗？
                </s-alert>
                <s-table columns="{{columns}}" datasource="{{datasource}}" max-height="328" />
            </s-dialog>
        </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-alert': Alert,
        's-table': Table
    };

    initData() {
        return {
            datasource: []
        };
    }

    attached() {
        // 因为是在弹窗里的 因此需要阻止下事件冒泡
        const drawerNode = document.querySelector('.m-detail-user-acl-perm-del');
        if (drawerNode) {
            drawerNode.addEventListener('click', (e: any) => {
                const event = e || window.event;
                event.stopPropagation();
            });
        }
    }

    async onConfirm() {
        try {
            const {clusterId, datasource, detail, type, text} = this.data.get('');
            const enumItem = PermTypeInfo.fromValue(type);
            await api[enumItem.updateApi]({
                params: datasource,
                clusterId,
                accessKey: detail.accessKey
            });
            Notification.success(`${text}权限已更新`);
        } catch (error) {
        } finally {
            // ! 暂时先在单个删除和批量删除后，均直接跳到列表页面并刷新
            this.fire('success', {});
            this.onClose();
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
