import {html, Enum} from '@baiducloud/runtime';
import {CommonTable, CreateBtn, EllipsisTip, RefreshBtn} from '@components/index';
import {Search, Table, Pagination, But<PERSON>, Drawer, Alert, Radio, Notification} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz';
import {pickEmpty} from '@common/utils';
import {PermType, IAclPermissionListItem, PermUpdateType} from './index.d';
import {IAclUserListItem} from '../list/index.d';
import {ConsumerGroupPerssionArr, TopicPerssionArr, TopicPerssions} from '@common/enums/biz';
import {ALL_ENUM} from '@common/enums';
import {api} from '@common/client';
import {PAGER_SUI, TABLE_SUI, SELECTION_SUI_MULTI} from '@common/config';
import UserAclNewPermDialog from './new-perm-dialog';

import './index.less';
import {UserAclDeletePermDialog} from './delete-prem-dialog';

const allEnum = ALL_ENUM.toArray();

export const PermTypeInfo = new Enum(
    {
        alias: PermType.Topic,
        text: '主题',
        value: PermType.Topic,
        listPermApi: 'aclUserTopicPerm',
        updateApi: 'updateAclUserTopicPerm',
        listOrigninApi: 'getTopicList',
        defaultPerm: 'defaultTopicPerm'
    },
    {
        alias: PermType.Consumer,
        text: '消费组',
        value: PermType.Consumer,
        listPermApi: 'aclUserGroupPerm',
        updateApi: 'updateAclUserGroupPerm',
        listOrigninApi: 'consumerGroupsList',
        defaultPerm: 'defaultGroupPerm'
    }
);

const klass = 'm-detail-user-acl-permission-drawer';

export default class UserAclPermissionDrawer extends CommonTable {
    static template = html`
        <div>
            <s-drawer
                class="${klass}"
                open="{=open=}"
                title="{{text}}权限"
                on-close="onClose"
                size="{{1000}}"
            >
                <s-alert class="${klass}-alert mb16" skin="info">
                    未添加权限的{{text}}将使用默认{{text}}权限，当前用户：<span class="bold">{{detail.accessKey}}</span>
                    默认{{text}}权限：<span class="bold">{{detail | filterPerm(type)}}
                </s-alert>
                <div class="${klass}__filter flex mb16">
                    <create-btn on-click="onCreate">添加{{text}}权限</create-btn>
                    <s-button
                        class="ml8"
                        disabled="{= !selection.selectedIndex.length || updatingDisable =}"
                        on-click="onDelete()"
                        width="{{46}}"
                    >
                        删除
                    </s-button>
                    <s-search
                        class="${klass}__search-box"
                        value="{= searchbox.name =}"
                        on-search="onSearch"
                        width="170"
                        placeholder="请输入{{text}}名称进行搜索"
                        clearable
                    />
                    <refresh-btn on-click="onRefresh" class="ml8" />
                </div>
                <s-table
                    columns="{{table.columns}}"
                    datasource="{{table.datasource}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    selection="{{selection}}"
                    on-selected-change="onSelectChange"
                    on-filter="onTableFilter($event)"
                >
                    <div slot="empty">
                        <s-biz-empty
                            vertical
                            emptyTitle="暂无数据"
                            emptyText="{{emptyText}}"
                            actionText="添加{{text}}权限"
                            on-click="onCreate"
                        />
                    </div>
                    <div slot="c-policy">
                        <s-radio-group
                            datasource="{{radioGroupDataSource}}"
                            on-change="handlePolicyChange($event, row)"
                            value="{{row.policy}}"
                            disabled="{{updatingDisable}}"
                        >
                    </div>
                    <div slot="c-actions">
                        <s-button skin="stringfy" on-click="onDelete(row)" disabled="{{updatingDisable}}">删除</s-button>
                    </div>
                </s-table>
                <s-pagination
                    class="${klass}__pager mt16"
                    s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                    layout="total, pageSize, pager"
                    total="{{pager.count}}"
                    pageSize="{{pager.pageSize}}"
                    page="{{pager.page}}"
                    pageSizes="{{pager.pageSizes}}"
                    max-item="{{7}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange"
                />
            </s-drawer>
        </div>
    `;

    static components = {
        's-drawer': Drawer,
        'create-btn': CreateBtn,
        's-search': Search,
        'refresh-btn': RefreshBtn,
        's-table': Table,
        's-pagination': Pagination,
        's-biz-empty': Empty,
        'ellipsis-tip': EllipsisTip,
        's-button': Button,
        's-alert': Alert,
        's-radio-group': Radio.RadioGroup
    };

    static filters = {
        filterPerm(detail: IAclUserListItem, type: PermType) {
            const enumItem = PermTypeInfo.fromValue(type);

            return TopicPerssions.getTextFromValue(detail[enumItem.defaultPerm]);
        }
    };

    static computed: SanComputedProps = {
        text() {
            return PermTypeInfo.getTextFromValue(this.data.get('type'));
        },
        radioGroupDataSource() {
            return this.data.get('type') === PermType.Topic ? TopicPerssionArr : ConsumerGroupPerssionArr;
        }
    };

    initData() {
        return {
            open: false,
            type: PermType.Topic,
            searchbox: {
                name: ''
            },
            table: {
                ...TABLE_SUI
            },
            pager: {...PAGER_SUI},
            selection: {
                ...SELECTION_SUI_MULTI
            },
            PermType,
            updatingDisable: false,
            radioGroupDataSource: []
        };
    }

    inited() {}

    attached() {
        this.data.set('open', true);
        this.getComList();
        // 初始化columns
        this.initColumns();
    }

    initColumns() {
        const type = this.data.get('type');
        this.data.set('table.columns', [
            {name: 'name', label: `${PermTypeInfo.getTextFromValue(type)}名称`, width: '56%'},
            {
                name: 'policy',
                label: '权限',
                width: '36%',
                filter: {
                    options: [...allEnum, ...(type === PermType.Topic ? TopicPerssionArr : ConsumerGroupPerssionArr)],
                    value: allEnum[0].value
                }
            },
            {name: 'actions', label: '操作', width: '8%'}
        ]);
    }

    async getTableList(): Promise<void> {
        const {
            searchbox: {name},
            pager,
            clusterId,
            detail,
            type,
            policy
        } = this.data.get('');
        const text = this.data.get('text');
        let keywordType: string[] = [];
        let keyword: string[] = [];

        if (name) {
            keywordType.push('name');
            keyword.push(name);
        }
        if (policy) {
            keywordType.push('policy');
            keyword.push(policy);
        }
        let param = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            keywordType,
            keyword
        });
        const enumItem = PermTypeInfo.fromValue(type);
        const {totalCount, result} = await api[enumItem.listPermApi]({
            params: param,
            clusterId,
            accessKey: detail.accessKey
        });
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', result);
        this.data.set(
            'emptyText',
            name ? `暂无符合条件的${text}权限，您可以尝试更换筛选条件。 ` : `您还没有添加任何${text}权限 `
        );
    }

    onSelectChange(event: {value: {selectedIndex: number[]; selectedItems: object[]}}) {
        this.data.set('selection.selectedIndex', event.value.selectedIndex);
    }

    async handlePolicyChange(target: {value: string}, row: IAclPermissionListItem) {
        const {type, clusterId, detail, text} = this.data.get('');
        const enumItem = PermTypeInfo.fromValue(type);

        this.data.set('updatingDisable', true);

        try {
            await api[enumItem.updateApi]({
                params: [
                    {
                        name: row.name,
                        policy: target.value,
                        action: PermUpdateType.Update
                    }
                ],
                clusterId,
                accessKey: detail.accessKey
            });
            Notification.success(`${text}权限已更新`);
            this.onRefresh();
        } catch (error) {
        } finally {
            this.data.set('updatingDisable', false);
        }
    }

    onTableFilter(args: {
        field: {name: string; filter: {value: string | number}};
        filter: {value: string | number; stopPropagation: () => void};
    }) {
        args.filter && args.filter.stopPropagation();
        this.onFilter(args);
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        setTimeout(() => {
            this.dispose && this.dispose();
        }, 1000);
    }

    async onDelete(row?: IAclPermissionListItem) {
        const {type, clusterId, detail, text} = this.data.get('');
        let params = [];
        if (row) {
            params = [
                {
                    name: row.name,
                    policy: row.policy,
                    action: PermUpdateType.Delete
                }
            ];
        } else {
            const datasource: IAclPermissionListItem[] = this.data.get('table.datasource');
            const {selectedIndex} = this.data.get('selection');
            const selects = datasource.filter((item, index) => selectedIndex.includes(index));

            params = selects.map(item => ({
                name: item.name,
                policy: item.policy,
                action: PermUpdateType.Delete
            }));
        }
        const dialog = new UserAclDeletePermDialog({
            data: {
                columns: [
                    {name: 'name', label: `${text}名称`},
                    {
                        name: 'policy',
                        label: '权限',
                        render: (item: {name: string; policy: string}) => TopicPerssions.getTextFromValue(item.policy),
                        width: '120px'
                    }
                ],
                datasource: params,
                text,
                detail,
                clusterId,
                type
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onPageChange({value: {page: 1, pageSize: this.data.get('pager.pageSize')}});
        });
    }

    onCreate() {
        const {type, clusterId, detail, nameList, text} = this.data.get('');
        const dialog = new UserAclNewPermDialog({
            data: {
                type,
                clusterId,
                detail,
                nameList,
                text,
                formData: {
                    accessKey: detail.accessKey
                }
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.onPageChange({value: {page: 1, pageSize: this.data.get('pager.pageSize')}});
        });
    }
}
