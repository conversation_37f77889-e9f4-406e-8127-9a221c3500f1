/**
 * 密码规则面板
 * @file index.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {Popover, Input} from '@baidu/sui';
import './index.less';
import {CheckCircle2, CloseCircle2} from '@baidu/xicon-san';
import {asInput} from '@common/decorators';
import _ from 'lodash';
import {checkKeyboardContinuousChar, hasContinuousStr, hasRepeatStr} from './utils';
import {isInvalid} from '@common/utils';

interface IRule {
    /** 规则文案 */
    text: string;
    /** 校验函数 */
    validator: (value: string) => boolean;
    /** 是否通过校验 */
    passed: boolean;
}

const klass = 'password-popover';

@asInput('input')
export class PasswordPopover extends Component {
    static template = html`
        <template>
            <s-popover visible="{= showPassTip =}" trigger="focus" placement="top">
                <div slot="content" class="${klass}">
                    <div class="${klass}__title mb4">{{title}}</div>
                    <div s-for="item in rules" class="${klass}__rule-item">
                        <template s-if="value">
                            <x-icon-check-circle s-if="item.passed" color="#30BF13" theme="fill" size="{{12}}" />
                            <x-icon-close-circle s-else color="#F33E3E" theme="fill" size="{{12}}" />
                        </template>
                        <x-icon-check-circle s-else color="#84868c" size="{{12}}" />
                        <span class="${klass}__rule-item-text ml4">{{item.text}}</span>
                    </div>
                    <div class="${klass}__passlevel-title mt4">密码强度：{{passLevel.text}}</div>
                    <div class="${klass}__passlevel-list mt4 {{levelClass}}">
                        <span class="${klass}__passlevel-item"></span>
                        <span class="${klass}__passlevel-item"></span>
                        <span class="${klass}__passlevel-item"></span>
                    </div>
                </div>
                <s-input
                    width="{{inputWidth}}"
                    showPasswordIcon
                    on-blur="onBlur"
                    on-focus="onFocus"
                    type="password"
                    value="{= value =}"
                    placeholder="请输入密码"
                    on-input="onInput"
                ></s-input>
            </s-popover>
        </template>
    `;
    static components = {
        's-popover': Popover,
        's-input': Input,
        'x-icon-check-circle': CheckCircle2,
        'x-icon-close-circle': CloseCircle2
    };

    static computed: SanComputedProps = {
        passLevel() {
            const value = this.data.get('value') || '';
            const rules = this.data.get('rules') as IRule[];
            const isAllPassed = _.every(rules, item => item.passed);

            const level = _.compact([
                hasRepeatStr(value),
                hasContinuousStr(value),
                checkKeyboardContinuousChar(value)
            ]).length;

            const levelTextMap: Record<number, string> = {
                0: '高',
                1: '中'
            };

            const levelText = levelTextMap[level] || '低';
            return {
                level: isAllPassed ? level : '',
                text: isAllPassed ? levelText : '-'
            };
        },
        levelClass() {
            const passLevel = this.data.get('passLevel');
            if (isInvalid(passLevel.level)) {
                return '';
            }
            return `${klass}__passlevel ${klass}__passlevel--${passLevel.level}`;
        }
    };
    initData() {
        return {
            inputWidth: 388,
            showPassTip: false,
            title: '出于安全考虑，密码需要满足以下条件：',
            rules: [],
            value: ''
        };
    }

    onFocus() {
        this.toogleTip(true);
    }

    toogleTip(visible: boolean) {
        this.data.set('showPassTip', visible);
    }

    onInput(e: HTMLInputElement) {
        this.validates(e);
    }

    validates(e: HTMLInputElement) {
        const rules = this.data.get('rules') as IRule[];
        const newRules = rules.map(item => {
            item.passed = typeof item.validator === 'function' ? item.validator(e.value) : true;
            return {
                ...item
            };
        });
        this.data.set('rules', newRules);
    }
}
