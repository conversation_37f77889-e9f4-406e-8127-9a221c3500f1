/**
 * 字符串是否是连续的，如 abc，123，cba 等
 * @param str
 * @param len
 * @returns
 */
export const hasContinuousStr = (str: string, len = 3) => {
    let repeatSize = 1; // 用于连续个数的统计
    for (let i = 1; i < str.length; i++) {
        // @ts-ignore
        if (str[i] - str[i - 1] === 1 || str[i] - str[i - 1] === -1) {
            repeatSize += 1;
        }

        if (repeatSize > len) {
            return true;
        }
    }
    return false;
};

/**
 * 字符串是否存在某个重复的字符, 如 aaa, 111 等
 * @param str
 * @returns
 */
export const hasRepeatStr = (str: string) => {
    const reg = new RegExp(/(\w)\1{2}/g);
    return reg.test(str);
};

/**
 * 判断字符串是否键盘三连（横着、竖着）
 * @param {String} str
 * @returns boolean 是否满足键盘3连键
 */
export function checkKeyboardContinuousChar(str: string | string[]) {
    const c1 = [
        ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+'],
        ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '{', '}', '|'],
        ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ':', '"'],
        ['z', 'x', 'c', 'v', 'b', 'n', 'm', '<', '>', '?']
    ];
    const c2 = [
        ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='],
        ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']', '\\'],
        ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', "'"],
        ['z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/']
    ];
    if (!Array.isArray(str)) {
        str = str.toLowerCase().split('');
    }
    // 获取坐标位置
    const y = [];
    const x = [];
    for (let c = 0; c < str.length; c++) {
        y[c] = 0; // 当做~`键处理
        x[c] = -1;
        for (let i = 0; i < c1.length; i++) {
            for (let j = 0; j < c1[i].length; j++) {
                if (str[c] === c1[i][j]) {
                    y[c] = i;
                    x[c] = j;
                }
            }
        }
        if (x[c] !== -1) {
            continue;
        }

        for (let i = 0; i < c2.length; i++) {
            for (let j = 0; j < c2[i].length; j++) {
                if (str[c] === c2[i][j]) {
                    y[c] = i;
                    x[c] = j;
                }
            }
        }
    }
    // 匹配坐标连线
    for (let c = 1; c < str.length - 1; c++) {
        // 横着同一行
        if (y[c - 1] === y[c] && y[c] === y[c + 1]) {
            // 从左往右或者从右往左一排
            if ((x[c - 1] + 1 === x[c] && x[c] + 1 === x[c + 1]) || (x[c + 1] + 1 === x[c] && x[c] + 1 === x[c - 1])) {
                return true;
            }
        }
        // 竖着同一列
        if (x[c - 1] === x[c] && x[c] === x[c + 1]) {
            // 从下往上或者从下往下同一列
            if ((y[c - 1] + 1 === y[c] && y[c] + 1 === y[c + 1]) || (y[c + 1] + 1 === y[c] && y[c] + 1 === y[c - 1])) {
                return true;
            }
        }
        // 竖着同一列（类似/而不是\的一列）
        if ((x[c - 1] + 1 === x[c] && x[c] + 1 == x[c + 1]) || (x[c - 1] - 1 === x[c] && x[c] - 1 === x[c + 1])) {
            if ((y[c - 1] + 1 === y[c] && y[c] + 1 === y[c + 1]) || (y[c + 1] + 1 === y[c] && y[c] + 1 === y[c - 1])) {
                return true;
            }
        }
    }
    return false;
}
