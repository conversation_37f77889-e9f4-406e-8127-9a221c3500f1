/**
 * 用户密码
 * @file index.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {PasswordPopover} from '../password-popover';

export class AclPassword extends Component {
    static template = html`
        <template>
            <password-popver rules="{{rules}}" value="{= value =}"></password-popver>
        </template>
    `;
    static components = {
        'password-popver': PasswordPopover
    };

    initData() {
        return {
            rules: [
                {
                    text: '8～32位字符',
                    passed: false,
                    validator: (value: string) => {
                        const valLen = value.length;
                        return valLen >= 8 && valLen <= 32;
                    }
                },
                {
                    text: '英文字母、数字和特殊符号必须同时存在',
                    validator: (value: string) => {
                        return /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^*()]).+$/.test(value);
                    }
                },
                {
                    text: '符号仅限!@#$%^*()',
                    passed: false,
                    validator: (value: string) => {
                        return /[!@#$%^*()]/.test(value);
                    }
                }
            ],
            value: ''
        };
    }
}
