/**
 * 重置Miami
 * @file index.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {Dialog, Form, Input, Button, Notification} from '@baidu/sui';
import './index.less';
import {AclPassword} from '../components/acl-password';
import {AclSecretKeyReg, AlcSecretKeyRegTip} from '../config';
import {api} from '@common/client';
import {secretKeyNotifacation} from '../create/utils';
const klass = 'dialog-reset-key';

export default class ResetSecretKey extends Component {
    static template = html`
        <template>
            <s-dialog open="{= open =}" title="重置密码" class="${klass}" width="{{520}}" on-close="onClose">
                <s-form s-ref="form" rules="{{rules}}" data="{= formData =}" label-align="left">
                    <s-form-item label="用户名：" required>{{row.accessKey}}</s-form-item>
                    <s-form-item label="新密码：" prop="secretKey">
                        <acl-password value="{= formData.secretKey =}" />
                    </s-form-item>
                    <s-form-item label="确认密码：" prop="confirmSecretKey">
                        <s-input
                            type="password"
                            value="{= formData.confirmSecretKey =}"
                            showPasswordIcon
                            width="{{388}}"
                        ></s-input>
                    </s-form-item>
                </s-form>
                <footer slot="footer">
                    <s-button on-click="onClose">取消</s-button>
                    <s-button on-click="onConfirm" skin="primary" loading="{{confirmLoading}}">确定</s-button>
                </footer>
            </s-dialog>
        </template>
    `;
    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        'acl-password': AclPassword,
        's-button': Button
    };
    initData() {
        return {
            open: true,
            row: null,
            formData: {
                secretKey: '',
                confirmSecretKey: ''
            },
            rules: {
                secretKey: [
                    {required: true, message: '请输入密码'},
                    {
                        validator: (rule: any, value: string, callback: (str?: string) => void) => {
                            if (!AclSecretKeyReg.test(value)) {
                                return callback(AlcSecretKeyRegTip);
                            }
                            callback();
                        }
                    }
                ],
                confirmSecretKey: [
                    {required: true, message: '请输入密码'},
                    {
                        validator: (rule: any, value: string, callback: (str?: string) => void) => {
                            const secretKey = this.data.get('formData.secretKey');
                            if (value !== secretKey) {
                                return callback('两次密码不一致，请检查');
                            }
                            callback();
                        }
                    }
                ]
            },
            confirmLoading: false
        };
    }

    attached() {}

    async onConfirm() {
        await (this.ref('form') as any).validateFields();

        const {clusterId, row, formData} = this.data.get('');
        const {accessKey} = row;
        const {confirmSecretKey} = formData;

        try {
            this.data.set('confirmLoading', true);
            await api.resetAclPassword({
                clusterId,
                accessKey,
                params: {
                    newPassword: confirmSecretKey
                }
            });
            const content = secretKeyNotifacation(accessKey, confirmSecretKey);
            Notification.success(content, {
                title: '重置密码成功',
                duration: -1,
                width: 400
            });
            this.fire('success', {});
            this.onClose();
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('confirmLoading', false);
        }
    }

    onClose() {
        this.data.set('open', false);
        this.dispose();
    }
}
