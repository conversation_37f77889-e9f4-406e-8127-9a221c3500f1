import {Button, Input} from '@baidu/sui';
import {defineComponent} from 'san';
import {Eye, Closeeyes} from '@baidu/xicon-san';
import {ClipBoard} from '@baidu/sui-biz';
export const secretKeyNotifacation = (accessKey, secretKey) => {
    const content = defineComponent({
        template: /* html */ `
                        <template>
                             <div>用户名：{{accessKey}}</div>
                             <div>密码：{{onChangeSecretKey}}
                                <s-eye
                                    theme="line"
                                    color="#000"
                                    size="{{20}}" strokeLinejoin="round" s-if="show"  on-click='onToggle'/>
                                <s-closeeyes
                                    s-else theme="line"
                                    color="#000"
                                    size="{{20}}" strokeLinejoin="round" on-click='onToggle'/>
                             </div>
                             <div>
                             <s-clip-board text="{{credentials}}">
                               <s-button style="color:blue;border: 1px solid #fff;padding: 0 12px 0 0">
                               复制用户名密码</s-button>
                             </s-clip-board>
                             </div>
                        </template>
                    `,
        components: {
            's-input': Input,
            's-eye': Eye,
            's-closeeyes': Closeeyes,
            's-clip-board': ClipBoard,
            's-button': Button
        },
        computed: {
            onChangeSecretKey() {
                const show = this.data.get('show');
                const secretKey = this.data.get('secretKey');
                const hiddeenkey = function (secretKey) {
                    const maskCharacter = '•';
                    const passwordLength = secretKey.length;
                    let maskedPassword = maskCharacter.repeat(passwordLength);
                    return maskedPassword;
                };
                const secretKeyelse = show ? secretKey : hiddeenkey(secretKey);
                return secretKeyelse;
            }
        },
        initData() {
            return {
                accessKey,
                secretKey,
                show: false,
                credentials: `
                            用户名: ${accessKey},
                            密码: ${secretKey}
                        `
            };
        },
        onToggle() {
            this.data.set('show', !this.data.get('show'));
        }
    });
    return content;
};
