/**
 * 用户管理--创建用户
 * @file index.ts
 * <AUTHOR>
 */
import {html} from '@baiducloud/runtime';
import {BlockBox} from '@components/index';
import {Button, Menu, Input, Form, Switch, Popover, Radio, Dialog, Notification} from '@baidu/sui';
import {Component} from 'san';
import {TopicPerssionArr, ConsumerGroupPerssionArr, IPermissionType} from '@common/enums';
import './index.less';
import {Eye, Closeeyes} from '@baidu/xicon-san';
import {api} from '@common/client';
import _ from 'lodash';
import {AclPassword} from '../components/acl-password';
import {secretKeyNotifacation} from '../create/utils';
import {AclSecretKeyReg, AlcSecretKeyRegTip} from '../config';
const klass = 'user-create-dialog';
export default class CreateUser extends Component {
    static template = html`
        <template>
            <s-dialog open="{=open=}" title="{{isCreate ? '创建用户' : '编辑用户'}}" class="${klass}">
                    <s-form
                        s-ref="form"
                        data="{=formData=}"
                        isRequired="{{require}}"
                        rules="{{rules}}"
                        label-align="left"
                        class="create-form-container"
                    >
                        <s-form-item prop="accessKey" label="用户名：">
                            <s-input
                                s-if="isCreate"
                                value="{=formData.accessKey=}"
                                placeholder="请输入用户名"
                                width="{{400}}"
                            ></s-input>
                            <div s-else>{{formData.accessKey}}</div>
                            <div slot="help" s-if="isCreate">
                                英文字母开头，8～16位字符，只能包含英文字母、数字，下划线（_）
                            </div>
                        </s-form-item>
                        <s-form-item s-if="isCreate" prop="secretKey" label="密码：">
                            <acl-password value="{= formData.secretKey =}" />
                        </s-form-item>
                        <s-form-item s-if="isCreate" prop="confirmPassword" label="确认密码：">
                            <s-input
                                type="password"
                                width="{{400}}"
                                value="{=formData.confirmPassword=}"
                                showPasswordIcon="{{true}}"
                                placeholder="请输入密码"
                            ></s-input>
                        </s-form-item>
                        <s-form-item prop="whiteRemoteAddress" label="IP白名单：">
                            <s-textarea
                                width="{{400}}"
                                height="{{70}}"
                                value="{=formData.whiteRemoteAddress=}"
                                placeholder="请输入白名单IP，多个IP地址逗号分隔"
                            >
                            </s-textarea>
                        </s-form-item>
                        <s-form-item prop="admin" label="是否为管理员：">
                            <s-switch checked="{=formData.admin=}" active-text="是" inactive-text="否" />
                        </s-form-item>
                        <s-form-item prop="defaultTopicPerm" label="默认主题权限：">
                            <s-radio-group
                                datasource="{{topicPermGroup.TopicPerssionArr}}"
                                value="{=formData.defaultTopicPerm=}"
                                enhanced
                                radioType="button"
                            ></s-radio-group>
                        </s-form-item>
                        <s-form-item prop="defaultGroupPerm" label="默认消费组权限：">
                            <s-radio-group
                                datasource="{{groupPermGroup.ConsumerGroupPerssionArr}}"
                                value="{=formData.defaultGroupPerm=}"
                                enhanced
                                radioType="button"
                            ></s-radio-group>
                        </s-form-item>
                    </s-form>
                </div>
                <footer slot="footer">
                    <s-button on-click="onClose">取消</s-button>
                    <s-button on-click="onConfirm" skin="primary" loading="{{confirmLoading}}">确定</s-button>
                </footer>
            </s-dialog>
        </template>
    `;
    static components = {
        'block-box': BlockBox,
        's-button': Button,
        's-menu': Menu,
        's-menu-item': Menu.Item,
        's-input': Input,
        's-form': Form,
        's-form-item': Form.Item,
        's-switch': Switch,
        's-radio-group': Radio.RadioGroup,
        's-dialog': Dialog,
        's-textarea': Input.TextArea,
        's-radio': Radio,
        's-popover': Popover,
        's-eye': Eye,
        's-closeeyes': Closeeyes,
        'acl-password': AclPassword
    };
    static computed: SanComputedProps = {
        isCreate() {
            return !this.data.get('row');
        }
    };
    initData() {
        return {
            require: true,
            open: true,
            confirmLoading: false,
            formData: {
                accessKey: '',
                secretKey: '',
                whiteRemoteAddress: '',
                admin: true,
                defaultTopicPerm: IPermissionType.PubSub,
                defaultGroupPerm: IPermissionType.Sub
            },
            rules: {
                accessKey: [
                    {required: true, message: '请输入用户名'},
                    {
                        validator(rule, value, callback) {
                            let pattern = /^[A-Za-z][A-Za-z0-9_]{7,15}$/;
                            const chinesePattern = /[\u4e00-\u9fa5\W]/;
                            if (chinesePattern.test(value)) {
                                if (/[\u4e00-\u9fa5]/.test(value)) {
                                    return callback('用户名不支持中文及特殊字符');
                                }
                                return callback('用户名需英文字母开头，且不能包含特殊字符');
                            }
                            if (!pattern.test(value)) {
                                if (!/^[A-Za-z]/.test(value)) {
                                    return callback('用户名需英文字母开头');
                                } else if (!/[A-Za-z0-9_]{7,15}$/.test(value.slice(1))) {
                                    return callback('用户名需输入8-16个字符');
                                }
                            }

                            callback();
                        }
                    }
                ],
                secretKey: [
                    {required: true, message: '请输入密码'},
                    {
                        validator: (rule, value: string, callback: (str?: string) => void) => {
                            if (!AclSecretKeyReg.test(value)) {
                                return callback(AlcSecretKeyRegTip);
                            }
                            callback();
                        }
                    }
                ],
                confirmPassword: [
                    {required: true, message: '请输入密码'},
                    {
                        validator: (rule, value, callback) => {
                            const secretKey = this.data.get('formData.secretKey');
                            if (value !== secretKey) {
                                return callback('两次输入的内容不一致');
                            }
                            callback();
                        }
                    }
                ],
                whiteRemoteAddress: [
                    {
                        validator: async (rule, value, callback) => {
                            if (typeof value === 'string') {
                                const ipList = value
                                    .replace(/，/g, ',')
                                    .split(',')
                                    .map(ip => ip.trim());
                                if (ipList.length > 50) {
                                    return callback('IP白名单最多支持50个');
                                }
                                const ipPattern =
                                    /^((25[0-5]|2[0-4][0-9]|1?[0-9]?[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1?[0-9]?[0-9])$/;
                                for (const ip of ipList) {
                                    if (!ipPattern.test(ip) && value) {
                                        return callback('请输入正确的IP格式');
                                    }
                                }
                            }
                            callback();
                        }
                    }
                ]
            },
            topicPermGroup: {
                TopicPerssionArr
            },
            groupPermGroup: {
                ConsumerGroupPerssionArr
            }
        };
    }

    attached() {
        const isCreate = this.data.get('isCreate');
        if (!isCreate) {
            this.initEditData();
        }
    }

    initEditData() {
        const row = this.data.get('row');
        const {accessKey, whiteRemoteAddress, admin, defaultTopicPerm, defaultGroupPerm} = row;
        this.data.merge('formData', {
            accessKey,
            whiteRemoteAddress,
            admin,
            defaultTopicPerm,
            defaultGroupPerm
        });
    }
    toogleTip(visible: boolean) {
        this.data.set('showPassTip', visible);
    }

    onFocus() {
        this.data.set('isWeakPassword', false);
    }
    onClose() {
        this.data.set('open', false);
    }
    async onConfirm() {
        await this.ref('form').validateFields();
        const isCreate = this.data.get('isCreate');
        const route = this.data.get('route');
        const {clusterId} = route.query;
        const {accessKey, secretKey, whiteRemoteAddress, admin, defaultTopicPerm, defaultGroupPerm} =
            this.data.get('formData');
        try {
            this.data.set('confirmLoading', true);
            if (isCreate) {
                const params = {
                    accessKey,
                    secretKey,
                    whiteRemoteAddress: whiteRemoteAddress ? whiteRemoteAddress.replace(/，/g, ',').split(',') : [],
                    admin,
                    defaultTopicPerm,
                    defaultGroupPerm
                };
                await api.createAclUser({clusterId, params});
                const content = secretKeyNotifacation(accessKey, secretKey);
                Notification.success(content, {
                    title: '创建用户成功',
                    duration: -1,
                    width: 400
                });
            } else {
                const params = {
                    whiteRemoteAddress:
                        whiteRemoteAddress && typeof whiteRemoteAddress === 'string'
                            ? whiteRemoteAddress.replace(/，/g, ',').split(',')
                            : [],
                    admin,
                    defaultTopicPerm,
                    defaultGroupPerm
                };
                await api.changeAclUser({clusterId, accessKey, params});
                Notification.success('编辑用户成功');
            }
            this.fire('success', {});
            this.onClose();
        } catch (err) {
            console.log(err);
        } finally {
            this.data.set('confirmLoading', false);
        }
    }
}
