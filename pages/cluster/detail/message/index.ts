import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Pagination, Table, Button, Select, DatePicker, Tooltip, Input, Form, Row, Col} from '@baidu/sui';
import {AppListPage, SearchBox} from '@baidu/sui-biz';
import {CommonTable} from '@components/table/index';
import MessageDetail from './detail';
import {TipSelect} from '@components/tip-select';
import {TABLE_SUI, PAGER_MAX, PAGINATION_LAYOUT} from '@common/config';
import {api} from '@common/client';
import {Base64} from 'js-base64';
import {IMessageList} from '@common/client/types/message';
import {PAGER_SUI} from '@common/config';
import {formatTime, isClusterStatusDisabled, pickEmpty} from '@common/utils';
import './index.less';
const klass = 'cluster-detail-message';
export default class Message extends CommonTable {
    static template = html`
        <div class="${klass}">
            <app-list-page class="${klass}_content">
                <div slot="pageTitle">
                    <h2 class="title">消息查询</h2>
                </div>
                <s-form s-ref="form" data="{= formData =}" rules="{{rules}}" layout="inline" slot="bulk">
                    <s-row>
                        <s-form-item prop="topicName" label="主题名称：">
                            <s-select value="{=formData.topicName=}" disabled="{{topicDisabled}}" filterable>
                                <s-tooltip s-for="item in topicList" content="{{item.tip}}" placement="right">
                                    <s-option value="{{item.value}}" label="{{item.label}}" />
                                </s-tooltip>
                            </s-select>
                        </s-form-item>
                        <s-form-item prop="key" label="消息key：">
                            <s-input value="{=formData.key=}" placeholder="请输入消息key" />
                        </s-form-item>
                    </s-row>
                    <s-row>
                        <s-form-item prop="msgId" label="Message ID：">
                            <s-input value="{=formData.msgId=}" placeholder="请输入Message ID" />
                        </s-form-item>
                        <s-form-item prop="timedata" label="存储时间：">
                            <s-date-picker value="{=formData.timedata=}" mode="second" />
                        </s-form-item>
                        <s-button
                            skin="primary"
                            class="ml16"
                            width="34"
                            loading="{{queryLoading}}"
                            disabled="{{operationDisabled}}"
                            on-click="onSearchMessage"
                        >
                            查询
                        </s-button>
                    </s-row>
                </s-form>
                <s-table
                    class="btn-format-table"
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    datasource="{{table.datasource}}"
                >
                    <div slot="c-operation">
                        <span class="operation">
                            <s-button skin="stringfy" class="table-btn-slim" on-click="onView($event, row)">
                                查看详情
                            </s-button>
                        </span>
                    </div>
                </s-table>
                <s-pagination
                    slot="pager"
                    s-if="{{pager.count>10}}"
                    layout="${PAGINATION_LAYOUT}"
                    total="{{pager.count}}"
                    pageSize="{{pager.pageSize}}"
                    page="{{pager.page}}"
                    pageSizes="{{pager.pageSizes}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange"
                />
            </app-list-page>
        </div>
    `;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        's-select': Select,
        's-date-picker': DatePicker.DateRangePicker,
        's-tooltip': Tooltip,
        'tip-select': TipSelect,
        's-input': Input,
        's-form': Form,
        's-form-item': Form.Item,
        's-option': Select.Option,
        's-row': Row,
        's-col': Col
    };

    initData() {
        return {
            table: {
                ...TABLE_SUI,
                loading: false,
                columns: [
                    {
                        name: 'topicName',
                        label: '主题名称'
                    },
                    {
                        name: 'msgId',
                        label: 'Message ID'
                    },
                    {
                        name: 'tag',
                        label: '消息tag'
                    },
                    {
                        name: 'key',
                        label: '消息key'
                    },
                    {
                        name: 'storeTime',
                        label: '存储时间'
                    },
                    {
                        name: 'operation',
                        label: '操作'
                    }
                ]
            },
            topicList: [],
            pager: {...PAGER_SUI},
            formData: {
                topicName: '',
                timedata: {
                    begin: new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
                    end: new Date()
                },
                key: '',
                msgId: ''
            },
            rules: {
                topicName: [{required: true, message: '请选择主题名称'}],
                timedata: [
                    {required: true},
                    {
                        validator: (rule, value, callback) => {
                            if (!value.begin || !value.end) {
                                return callback('请选择时间范围');
                            }
                            callback();
                        }
                    }
                ]
            }
        };
    }

    static computed = {
        operationDisabled() {
            const detail = this.data.get('detail') as IClusterDetail;
            return isClusterStatusDisabled(detail);
        }
    };

    async attached() {
        this.getTopicList();
        this.getComList();
    }

    async getTopicList() {
        const {clusterId} = this.data.get('route.query');
        const params = {
            ...PAGER_MAX
        };
        const {result} = await api.getTopicList({params, clusterId});
        this.data.set(
            'topicList',
            result?.map(item => ({text: item.topicName, value: item.topicName, tip: item.topicName}))
        );
        this.data.set('formData.topicName', result[0]?.topicName);
    }

    async getTableList() {
        const {clusterId} = this.data.get('route.query');
        const {formData, pager} = this.data.get('');
        let queryItems = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize
        });
        const {topicName, timedata, key, msgId} = formData;
        const params = {
            topicName,
            key,
            msgId,
            beginTime: new Date(timedata.begin).getTime(),
            endTime: new Date(timedata.end).getTime()
        };
        if (!topicName || !timedata.begin || !timedata.end) {
            return;
        }
        const {result, totalCount} = await api.listMessages({params, clusterId, queryItems});
        this.data.set('pager.count', totalCount);
        result.forEach(item => {
            item.storeTime = formatTime(item.storeTime);
        });
        this.data.set('table.datasource', result);
    }

    async onSearchMessage() {
        await (this.ref('form') as unknown as Form).validateFields();
        this.data.set('queryLoading', true);
        this.getComList();
        this.data.set('queryLoading', false);
    }

    async onView(event: Event, row: IMessageList) {
        event.stopPropagation();
        const {topicName} = row;
        const {clusterId} = this.data.get('route.query');
        const params = {
            topicName: Base64.encodeURI(topicName)
        };
        const dialog = new MessageDetail({data: {message: {...row}, params, clusterId}});
        dialog.attach(document.body);
        dialog.on('success', () => {});
    }
}
