import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Form, Input, Loading} from '@baidu/sui';
import {api} from '@common/client';
import {formatEmpty} from '@common/utils';
import {EllipsisTip} from '@components/index';
import './index.less';
const klass = 'message-detail';
export default class extends Component {
    static template = html` <template>
        <s-dialog title="消息详情" open="{{open}}" class="${klass}">
            <s-loading loading="{{loading}}" size="large">
                <s-form label-align="left" class="message-form">
                    <s-form-item prop="msgId" label="Message ID："> {{message.msgId}} </s-form-item>
                    <s-form-item prop="topicName" label="主题名称："> {{message.topicName}} </s-form-item>
                    <s-form-item prop="tag" label="消息tag：" class="ellipsis-tip">
                        <ellipsis-tip text="{{message.tag}}" placement="top"> {{message.tag|formatEmpty}}</ellipsis-tip>
                    </s-form-item>
                    <s-form-item prop="key" label="消息key：" class="ellipsis-tip">
                        <ellipsis-tip text="{{message.key}}" placement="top">
                            {{message.key|formatEmpty}}</ellipsis-tip
                        ></s-form-item
                    >
                    <s-form-item prop="storeTime" label="存储时间："> {{message.storeTime}} </s-form-item>
                    <s-form-item label="消息体：">
                        <s-textarea width="350" height="100" readonly value="{{message.body}}"> </s-textarea>
                    </s-form-item>
                </s-form>
            </s-loading>
            <div slot="footer"></div>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-form': Form,
        's-form-item': Form.Item,
        's-textarea': Input.TextArea,
        's-loading': Loading,
        'ellipsis-tip': EllipsisTip
    };

    static filters: SanFilterProps = {
        formatEmpty
    };

    initData() {
        return {
            open: true,
            loading: true
        };
    }
    inited() {
        this.getMessage();
    }
    async getMessage() {
        try {
            const {params, clusterId} = this.data.get('');
            const {msgId} = this.data.get('message');
            const {body} = await api.getMessage({messageId: msgId, params, clusterId});
            this.data.set('message.body', body);
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('loading', false);
        }
    }
}
