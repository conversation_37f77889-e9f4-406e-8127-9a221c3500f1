/**
 * 付费信息
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {BlockBox} from '@components/index';
import {AppDetailCell} from '@baidu/sui-biz';
import {Link} from '@baidu/sui';
import './index.less';
import {IClusterDetail} from '../../index.d';
import {formatPayment, formatTime, formatUtcTime, getPayLoop} from '@common/utils';
import {PaymentTextMap} from '@pages/cluster/create/payment-region';
import dayjs from 'dayjs';
import {renderSwitch} from '@common/html';
const klass = 'm-detail-payment';
import {isOneCloudId} from '@common/utils/common';
import {isSandbox} from '@common/config/biz';
export class DetailPayment extends Component {
    static template = html`
        <div class="${klass}">
            <block-box title="付费信息" hasBottomBorder>
                <biz-detail-cell datasource="{{datasource}}">
                    <s-link slot="c-billing" href="{{item.value}}" skin="primary" target="blank">查看</s-link>
                </biz-detail-cell>
            </block-box>
        </div>
    `;
    initData() {
        return {
            isOneCloud: isOneCloudId()
        };
    }

    static components = {
        'block-box': BlockBox,
        'biz-detail-cell': AppDetailCell,
        's-link': Link
    };

    static computed = {
        datasource() {
            const detail = this.data.get('detail');
            const isOneCloud = this.data.get('isOneCloud');
            if (!detail) {
                return [];
            }

            const {
                payment,
                expirationTime,
                region,
                renew,
                renewTimeLength,
                renewTimeUnit,
                clusterId,
                productType,
                createTime
            } = detail as IClusterDetail;
            let billingUrl = '';
            if (!isOneCloud) {
                const startTime = encodeURIComponent(formatUtcTime(dayjs(createTime).startOf('month')));
                const endTime = encodeURIComponent(formatUtcTime(dayjs().endOf('month')));
                const suffex = `serviceType=ROCKETMQ&startTime=${startTime}&endTime=${endTime}&instanceId=${clusterId}&shortId=${clusterId}&region=${region}&productType=${productType}&type=resource`;
                billingUrl = `/billing/bill/resource/detail?${suffex}`;
            } else {
                billingUrl = isSandbox
                    ? 'http://console.cloud-sandbox.baidu-int.com/finance/orders/order?tabKey=BCE'
                    : 'https://console.cloud.baidu-int.com/finance/orders/order?tabKey=BCE';
            }

            let datasource = [
                {label: PaymentTextMap.payment, value: formatPayment(payment)},
                {label: '账单信息：', slot: 'billing', value: billingUrl},
                {label: '到期时间：', value: formatTime(expirationTime)}
            ];

            if (renew !== null) {
                datasource.push({label: PaymentTextMap.autoSwitch, value: renderSwitch(renew)});
                renewTimeLength &&
                    renewTimeUnit &&
                    datasource.push({label: '续费周期', value: getPayLoop(renewTimeLength, renewTimeUnit)});
            }
            return datasource;
        }
    };
}
