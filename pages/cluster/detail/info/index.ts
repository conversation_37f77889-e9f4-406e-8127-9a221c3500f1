/**
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {DetailSummary} from './summary';
import {DetailPayment} from './payment';
import {DetailConfig} from './cluster-config';
import {DetailAccess} from './access';
import {DetailNetwork} from './network';
import {DetailDisk} from './disk';
import {DetailNode} from './node';

export class DetailInfo extends Component {
    static template = html`<div class="detail-info">
        <detail-summary detail="{{detail}}" />
        <detail-payment detail="{{detail}}" />
        <detail-config detail="{{detail}}" route="{{route}}" />
        <detail-access detail="{{detail}}" route="{{route}}" />
        <detail-network detail="{{detail}}" />
        <detail-disk detail="{{detail}}" />
        <detail-node detail="{{detail}}" route="{{route}}" s-ref="detailNode" on-getDetail="getDetail" />
    </div> `;

    static components = {
        'detail-summary': DetailSummary,
        'detail-payment': DetailPayment,
        'detail-config': DetailConfig,
        'detail-access': DetailAccess,
        'detail-network': DetailNetwork,
        'detail-disk': DetailDisk,
        'detail-node': DetailNode
    };

    initData() {
        return {
            /** ----由父组件传入 ---- start */
            detail: null,
            whiteListMap: {},
            route: {}
            /** ----由父组件传入 ---- end */
        };
    }

    attached() {
        this.fire('getDetail', {needLoading: true});
    }

    refresh() {
        this.fire('getDetail', {needLoading: true});
        const refDetailNode = this.ref('detailNode') as unknown as DetailNode;
        refDetailNode.refresh();
    }
    getDetail(options?: {needLoading: boolean}) {
        const {needLoading} = options || {};
        this.fire('getDetail', {needLoading});
    }
}
