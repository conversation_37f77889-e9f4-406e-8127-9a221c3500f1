/**
 * 集群配置
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {BlockBox} from '@components/index';
import {AppDetailCell, TipButton} from '@baidu/sui-biz';
import {Button, Table} from '@baidu/sui';
import './index.less';
import {IClusterDetail} from '../../index.d';
import {AccessTextMap} from '@pages/cluster/create/access-config';
import {AuthenticationModeList} from '@common/enums';
import {ProtocolColumns} from '@common/config';
import {ProtocolTable} from '@pages/cluster/components';
import {api, API_PREFIX} from '@common/client';
import {downloadSSL, isSsLPermissive, renderAuthentictionMode} from '@common/utils';
import {throttle} from '@common/decorators';

const klass = 'm-detail-access';

export class DetailAccess extends Component {
    static template = html`
        <div class="${klass}">
            <block-box title="${AccessTextMap.title}" hasBottomBorder>
                <biz-detail-cell datasource="{{datasource}}">
                    <biz-tip-button
                        slot="c-downloadSsl"
                        on-click="onDownloadSsl"
                        skin="stringfy"
                        content="下载默认证书"
                    >
                        下载
                    </biz-tip-button>
                </biz-detail-cell>
                <protocol-table columns="{{table.columns}}" datasource="{{computedDatasource}}" />
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        'biz-detail-cell': AppDetailCell,
        's-button': Button,
        'biz-tip-button': TipButton,
        's-table': Table,
        'protocol-table': ProtocolTable
    };

    static computed: SanComputedProps = {
        datasource() {
            const detail = this.data.get('detail');

            if (!detail) {
                return [];
            }

            const {encryptionInTransit} = detail as IClusterDetail;

            let datasource: Array<{label: string; value: any; slot?: string}> = [
                {label: AccessTextMap.encryptionInTransit, value: encryptionInTransit?.join(';')}
            ];

            if (isSsLPermissive(encryptionInTransit)) {
                datasource.push({
                    label: 'SSL 证书：',
                    slot: 'downloadSsl',
                    value: encryptionInTransit
                });
            }
            return datasource;
        },
        computedDatasource() {
            const detail = this.data.get('detail');

            if (!detail) {
                return [];
            }
            const {authenticationModes, encryptionInTransit, nameServerEndpoints} = detail as IClusterDetail;
            const detailAuthenticationMode = authenticationModes[0];

            const datasource = [
                {
                    authenticationMode: renderAuthentictionMode(detailAuthenticationMode),
                    aclEnable: detailAuthenticationMode !== AuthenticationModeList.NONE,
                    encryptEnable: isSsLPermissive(encryptionInTransit),
                    endpoint: nameServerEndpoints?.[0]?.endpoint
                }
            ];

            return datasource;
        }
    };

    initData() {
        return {
            table: {
                columns: ProtocolColumns.concat({name: 'endpoint', label: '访问地址', width: 200}),
                datasource: []
            }
        };
    }

    // 下载证书文件
    @throttle(1000)
    async onDownloadSsl() {
        const {clusterId} = this.data.get('route.query');
        await downloadSSL({api: api.certs(clusterId)});
    }
}
