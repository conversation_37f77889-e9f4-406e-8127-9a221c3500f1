/**
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {BlockBox} from '@components/index';
import {AppDetailCell, ClipBoard} from '@baidu/sui-biz';
import './index.less';
import {IClusterDetail} from '../../index.d';
import {ClusterConfigTextMap} from '@pages/cluster/create/cluster-config';
import {formatArchFlush, formatMode, formatRegion, formatRunningTime, formatTime} from '@common/utils';
import {NodeConfigTextMap} from '@pages/cluster/create/node-config';
import {renderSwitch} from '@common/html';

const klass = 'm-detail-summary';

export class DetailSummary extends Component {
    static template = html`
        <div class="${klass}">
            <block-box title="集群概要" hasBottomBorder>
                <biz-detail-cell datasource="{{datasource}}">
                    <div slot="c-clusterId">
                        {{detail.clusterId}}
                        <s-clip-board text="{{detail.clusterId}}" />
                    </div>
                </biz-detail-cell>
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        'biz-detail-cell': AppDetailCell,
        's-clip-board': ClipBoard,
    };

    static computed = {
        datasource() {
            const detail = this.data.get('detail');

            if (!detail) {
                return [];
            }

            const {
                clusterId,
                name,
                version,
                region,
                runningTime,
                createTime,
                arch,
                mode,
                deploySetEnabled,
                flushDiskType,
                numberOfNodesPerBroker,
                zoneNames,
            } = detail as IClusterDetail;

            const datasource = [
                {label: '集群ID：', slot: 'clusterId'},
                {label: ClusterConfigTextMap.name, value: name},
                {label: ClusterConfigTextMap.version, value: version},
                {label: '所在地区：', value: formatRegion(region)},
                {label: '运行时间：', value: formatRunningTime(runningTime)},
                {label: '创建时间：', value: formatTime(createTime)},
                {
                    label: NodeConfigTextMap.arch,
                    value: formatArchFlush({
                        arch,
                        flushDiskType,
                        dledgerNumberOfNodesPerBroker: numberOfNodesPerBroker,
                        masterSlaveNumberOfNodesPerBroker: numberOfNodesPerBroker - 1,
                    }),
                },
                {label: NodeConfigTextMap.mode, value: formatMode({mode, selectedOrderZones: zoneNames})},
                {label: NodeConfigTextMap.deploySetEnabled, value: renderSwitch(deploySetEnabled)},
            ];
            return datasource;
        },
    };
}
