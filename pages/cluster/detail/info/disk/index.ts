/**
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {BlockBox} from '@components/index';
import {DiskTextMap} from '@pages/cluster/create/disk-config';
import {AppDetailCell} from '@baidu/sui-biz';
import {formatStorageSize, formatStorageType, formatTotalStorageSize} from '@common/utils';
import {IClusterDetail} from '../../index.d';
import './index.less';

const klass = 'm-detail-disk';
export class DetailDisk extends Component {
    static template = html`
        <div class="${klass}">
            <block-box title="${DiskTextMap.title}" hasBottomBorder>
                <biz-detail-cell datasource="{{datasource}}"></biz-detail-cell>
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        'biz-detail-cell': AppDetailCell,
    };

    static computed: SanComputedProps = {
        datasource() {
            const detail = this.data.get('detail');

            if (!detail) {
                return [];
            }

            const {storageType, numberOfDisks, storageSize, numberOfBrokerNodes} = detail as IClusterDetail;

            return [
                {label: DiskTextMap.storageType, value: formatStorageType(storageType)},
                {
                    label: DiskTextMap.storageSize,
                    value: formatStorageSize({storageSize, numberOfDisks, storageType}),
                },
                {
                    label: DiskTextMap.totalStorageSize,
                    value: formatTotalStorageSize({storageSize, numberOfDisks, storageType, numberOfBrokerNodes}),
                },
            ];
        },
    };
}
