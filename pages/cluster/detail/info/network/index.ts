/**
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {BlockBox} from '@components/index';
import {AppDetailCell} from '@baidu/sui-biz';
import {Link, Alert} from '@baidu/sui';
import './index.less';
import {IClusterDetail, SecurityGroup} from '../../index.d';
import {NetworkTextMap} from '@pages/cluster/create/network-config';
import {Units} from '@common/config';

const klass = 'm-detail-network';

export class DetailNetwork extends Component {
    static template = html`
        <div class="${klass}">
            <block-box title="网络安全" hasBottomBorder>
                <s-alert class="mb16">依据访问协议设置安全组出、入站的 TCP 协议端口以及 IP 范围</s-alert>
                <biz-detail-cell datasource="{{datasource}}">
                    <span slot="c-securityGroups">
                        <template s-for="row, index in item.value">
                            <s-link skin="primary" target="blank" href="{{row | securityGroupHref}}">
                                {{row.name}}
                            </s-link>
                            {{index < item.value.length - 1 ? '；': ''}}
                        </template>
                    </span>
                </biz-detail-cell>
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        'biz-detail-cell': AppDetailCell,
        's-link': Link,
        's-alert': Alert,
    };

    static computed = {
        datasource() {
            const detail = this.data.get('detail');

            if (!detail) {
                return [];
            }

            const {vpc, subnets, securityGroups, publicAccessEnabled, publicAccessBandwidth} = detail as IClusterDetail;

            let datasource = [
                {label: '所在网络：', value: `${vpc.name}(${vpc.cidr})`},
                {label: '所在子网：', value: subnets.map((item) => `${item.name}(${item.cidr})`).join('<br>')},
                {label: '安全组：', value: securityGroups, slot: 'securityGroups'},
            ];

            if (publicAccessEnabled) {
                datasource.push({
                    label: NetworkTextMap.publicAccessBandwidth,
                    value: `${publicAccessBandwidth} ${Units.Mbps}`
                });
            }
            return datasource;
        },
    };

    static filters: SanFilterProps = {
        securityGroupHref(item: SecurityGroup) {
            return `/network/#/vpc/security/detail?vpcId=${item.vpcId}&id=${item.securityGroupId}`;
        },
    };
}
