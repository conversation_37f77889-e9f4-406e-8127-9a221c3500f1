/**
 * 配置参数项
 */
export interface Configuration {
    /**
     * 参数的分类
     */
    category: string;
    /**
     * 参数当前值，类型不确定，可能是 String、Int 等，依赖 type 字段确定
     */
    currentValue: any[] | boolean | CurrentValueClass | number | number | null | string;
    /**
     * 参数默认值，类型不确定，可能是 String、Int 等，依赖 type 字段确定
     */
    defaultValue: any[] | boolean | DefaultValueClass | number | number | null | string;
    /**
     * 参数项描述
     */
    description: string;
    /**
     * 参数项名称
     */
    name: string;
    /**
     * 参数是否展示，可选：
     */
    overrideMode: 'OPTIONAL' | 'REQUIRED';
    /**
     * 参数值范围
     */
    scope: Array<any[] | boolean | ScopeClass | number | number | null | string>;
    /**
     * 参数的数据类型
     */
    type: string;
    /**
     * 参数的单位
     */
    unit: string;
    /**
     * 参数项更新模式
     */
    updateMode: string;
    [property: string]: any;
}

export interface CurrentValueClass {}

export interface DefaultValueClass {}

export interface ScopeClass {}
