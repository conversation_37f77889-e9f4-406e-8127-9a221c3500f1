/**
 * 配置参数列表展示
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Drawer, Table, Button, Pagination, Search} from '@baidu/sui';
import {formatEmpty, pickEmpty} from '@common/utils';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {PAGER_SUI, PAGINATION_LAYOUT} from '@common/config';
import './index.less';
import {api} from '@common/client';
import {Configuration} from './index.d';
import {CommonTable} from '@components/table';

const klass = 'page-config-dialog';
export class ParamsConfig extends CommonTable {
    static template = html` <template>
        <s-drawer
            title="配置详情"
            class="${klass}"
            open="{{open}}"
            size="{{600}}"
            on-close="onClose"
            maskClose="{{true}}"
            otherClose="{{true}}"
            showClose
        >
            <span class="title mb24">参数信息</span>
            <s-table
                class="${klass}"
                loading="{{table.loading}}"
                columns="{{table.columns}}"
                datasource="{{table.datasource}}"
            >
            </s-table>
            <s-pagination
                s-if="pager.count.length"
                class="${klass}__pager"
                layout="${PAGINATION_LAYOUT}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            >
            </s-pagination>
            <footer slot="footer" class="${klass}__footer s-drawer-footer-defalut">
                <s-button skin="primary" on-click="onClose" class="footer-close-btn">关闭</s-button>
            </footer>
        </s-drawer>
    </template>`;

    static components = {
        's-drawer': Drawer,
        's-button': Button,
        's-table': Table,
        's-pagination': Pagination,
        's-search': Search,
        's-refresh': OutlinedRefresh,
    };

    initData() {
        return {
            open: true,
            table: {
                columns: [
                    {
                        name: 'name',
                        label: '参数名称',
                    },
                    {
                        name: 'scope',
                        label: '参数范围',
                    },
                    {
                        name: 'defaultValue',
                        label: '默认值',
                        width: 80,
                    },
                    {
                        name: 'currentValue',
                        label: '当前值',
                        width: 80,
                    },
                ],
                datasource: [],
                loading: false,
            },
            pager: {...PAGER_SUI},
        };
    }

    static filters = {
        formatEmpty,
    };

    async attached() {
        this.getComList();
    }

    async getTableList() {
        const {clusterId, configId, revisionId, pager} = this.data.get('');
        const {page, pageSize} = pager;
        const param = pickEmpty({
            configId,
            revisionId,
            pageNo: page,
            pageSize,
        });
        const res = (await api.clusterConfigurations({clusterId, params: param})) as ListPage<Configuration>;
        this.data.set('table.datasource', res?.result || []);
        this.data.set('pager.count', res?.totalCount || 0);
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
