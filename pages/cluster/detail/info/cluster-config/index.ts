/**
 * 集群配置
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {BlockBox} from '@components/index';
import {AppDetailCell} from '@baidu/sui-biz';
import {Button} from '@baidu/sui';
import './index.less';
import {throttle} from '@common/decorators';
import {ParamsConfig} from './params-config';

const klass = 'm-detail-config';

export class DetailConfig extends Component {
    static template = html`
        <div class="${klass}">
            <block-box title="集群配置" hasBottomBorder>
                <biz-detail-cell datasource="{{datasource}}">
                    <template slot="c-config">
                        默认配置
                        <s-button on-click="onConfigView" skin="stringfy">查看</s-button>
                    </template>
                </biz-detail-cell>
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        'biz-detail-cell': AppDetailCell,
        's-button': Button,
    };

    static computed = {
        datasource() {
            const detail = this.data.get('detail');

            if (!detail) {
                return [];
            }

            const datasource = [{label: '集群配置：', slot: 'config'}];
            return datasource;
        },
    };

    @throttle(1000)
    onConfigView(e: Event) {
        // 解决抽屉打开后又被自动关闭问题
        // 原因：click事件冒泡冒到抽屉容器的父级，而在组件内部，抽屉容器的父级绑定了click事件，在事件处理函数中关闭弹窗
        e.stopPropagation();
        const {configId, revisionId} = this.data.get('detail.config') || {
            configId: '',
            revisionId: '',
        };
        const dialog = new ParamsConfig({
            data: {
                clusterId: this.data.get('route.query.clusterId'),
                configId,
                revisionId,
            },
        });
        dialog.attach(document.body);
        dialog.on('success', () => {});
    }
}
