/**
 * @file index.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {BlockBox, CommonTable} from '@components/index';
import {AppDetailCell} from '@baidu/sui-biz';
import {Link, Alert, Table, Pagination, Button, Dialog, Notification} from '@baidu/sui';
import './index.less';
import {IClusterDetail} from '../../index.d';
import {INodeItem} from '@common/client/types/cluster';

import {NodeConfigTextMap} from '@pages/cluster/create/node-config';
import {ALL_ENUM, ClusterNodeClientStatus, ClusterStatusType} from '@common/enums';
import {renderStatus} from '@common/html';
import {api} from '@common/client';
import {PAGER_SUI, PAGINATION_LAYOUT} from '@common/config';
import {pickEmpty, renderArrZoneLabel, requestInterval} from '@common/utils';
import {Component} from 'san';
const klass = 'm-detail-node';
class RestartNode extends Component {
    static template = html` <div>
        重启节点最长需要10分钟时间。
        在此期间，RocketMQ集群可用于生成和使用数据，但在重启完成之前，您将无法执行其他集群操作。
    </div>`;
}

export class DetailNode extends CommonTable {
    static template = html`
        <div class="${klass}">
            <block-box title="${NodeConfigTextMap.title}">
                <biz-detail-cell datasource="{{datasource}}" />
                <s-table
                    columns="{{table.columns}}"
                    datasource="{{table.datasource}}"
                    on-filter="onFilter"
                    loading="{{table.loading}}"
                >
                    <div slot="c-zoneNames">{{row.logicalZone | renderArrZoneLabel}}</div>
                    <div slot="c-operation">
                        <s-button
                            class="table-btn-slim"
                            on-click="onReloadNode(row, rowIndex)"
                            disabled="{{disableRestart}}"
                            skin="stringfy"
                        >
                            重启
                        </s-button>
                    </div>
                </s-table>
                <s-pagination
                    s-if="table.datasource.length"
                    class="${klass}__pager"
                    layout="${PAGINATION_LAYOUT}"
                    total="{{pager.count}}"
                    pageSize="{{pager.pageSize}}"
                    page="{{pager.page}}"
                    pageSizes="{{pager.pageSizes}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange"
                />
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        'biz-detail-cell': AppDetailCell,
        's-link': Link,
        's-alert': Alert,
        's-table': Table,
        's-pagination': Pagination,
        's-button': Button
    };

    static computed = {
        datasource() {
            const detail = this.data.get('detail');

            if (!detail) {
                return [];
            }

            const {nodeSpec, numberOfBrokers, numberOfBrokerNodes} = detail as IClusterDetail;

            const datasource = [
                {label: NodeConfigTextMap.nodeType, value: nodeSpec},
                {
                    label: NodeConfigTextMap.numberOfBrokers,
                    value: numberOfBrokers
                },
                {label: NodeConfigTextMap.numberOfBrokerNodes, value: numberOfBrokerNodes}
            ];

            return datasource;
        },
        disableRestart() {
            const detail = this.data.get('detail');
            if (!detail) {
                return true;
            }
            return detail.status !== ClusterStatusType.ACTIVE;
        }
    };

    static filters: SanFilterProps = {
        renderArrZoneLabel: zone => {
            return renderArrZoneLabel([zone]);
        }
    };

    initData() {
        return {
            table: {
                columns: [
                    {
                        name: 'nodeId',
                        label: '节点ID',
                        width: 100,
                        fixed: 'left'
                    },
                    {
                        name: 'brokerId',
                        label: '所属节点组',
                        fixed: 'left',
                        width: 100
                    },
                    {
                        name: 'brokerRole',
                        label: '组内角色',
                        width: 100
                    },
                    {
                        name: 'status',
                        label: '节点状态',
                        filter: {
                            options: [...ALL_ENUM.toArray(), ...ClusterNodeClientStatus.toArray()],
                            value: ALL_ENUM.ALL
                        },
                        render(item: INodeItem) {
                            return renderStatus(ClusterNodeClientStatus.fromValue(item.status));
                        },
                        width: 100
                    },
                    {
                        name: 'zoneNames',
                        label: '可用区',
                        width: 100
                    },
                    // 隐藏
                    // {
                    //     name: 'cpuUsedPercent',
                    //     label: 'CPU使用率',
                    //     width: 100,
                    // },
                    // {
                    //     name: 'memUsedPercent',
                    //     label: '内存使用率',
                    //     width: 100,
                    // },
                    // {
                    //     name: 'dataDiskUsedPercent',
                    //     label: '磁盘使用率',
                    //     width: 100,
                    // },

                    // {
                    //     name: 'runningTime',
                    //     label: '启动时长',
                    //     width: 120,
                    // },
                    {name: 'operation', label: '操作', width: 100, fixed: 'le'}
                ],
                datasource: [],
                loading: false
            },
            pager: {
                ...PAGER_SUI
            }
        };
    }

    attached() {
        this.getTableList();
    }

    async getTableList() {
        try {
            const {clusterId} = this.data.get('route.query');
            this.data.set('table.loading', true);
            const {page: pageNo, pageSize} = this.data.get('pager');
            const {status} = this.data.get('');
            const params = pickEmpty({
                pageNo,
                pageSize,
                status
            });
            const res = await api.clusterNodes({
                clusterId,
                params
            });
            this.data.set('table.datasource', res?.result || []);
            this.data.set('pager.count', res.totalCount || 0);
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('table.loading', false);
        }
    }

    refresh() {
        this.getTableList();
        this.fire('getDetail', {needLoading: false});
        this.onInterval();
    }
    onInterval() {
        if (this.restartPrevPolling) {
            this.restartPrevPolling();
        }
        this.restartPrevPolling = requestInterval(
            async () => {
                this.fire('getDetail', {needLoading: false});
                this.getTableList();
            },
            {
                time: 60 * 1000
            }
        );
    }
    onReloadNode(row: INodeItem, rowIndex: string) {
        const {clusterId} = this.data.get('route.query');
        const {instanceId} = row;
        const nodeId = instanceId;
        Dialog.warning({
            okText: '确定',
            content: RestartNode,
            onOk: async () => {
                try {
                    await api.restartClusterNode({clusterId, params: {nodeId}}).then(() => {
                        Notification.success('重启请求提交成功');
                        this.refresh();
                    });
                } catch (err) {
                    console.error(err);
                }
            }
        });
    }
}
