/**
 * 任务管理列表
 * @file index.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';

import {BlockBox, CommonTable, RefreshBtn} from '@components/index';

import {Table, Pagination, Select, Button} from '@baidu/sui';
import {ClipBoard} from '@baidu/sui-biz';
import {PAGER_SUI, PAGINATION_LAYOUT, TABLE_SUI} from '@common/config';
import {renderStatus} from '@common/html';
import {ClusterActionStatus, ClusterActionStatusType, ClusterActionType, ClusterActionTypeList} from '@common/enums';
import {api} from '@common/client';
import './index.less';
import {pickEmpty, formatEmpty, requestInterval, formatTime} from '@common/utils';
import {Operations} from './operations';
import {ActionResult, RunningTime} from './index.d';

const klass = 'page-action-list';
export class ActionList extends CommonTable {
    static template = html`
        <template>
            <div class="${klass}">
                <block-box title="任务管理">
                    <div class="filter-area">
                        任务类型：
                        <s-select
                            value="{= actionType =}"
                            datasource="{{actionTypeList}}"
                            searchable
                            on-change="onActionTypeChange"
                        ></s-select>
                        <refresh-btn on-click="onRefresh" />
                    </div>
                    <s-table columns="{{table.columns}}" datasource="{{table.datasource}}" loading="{{table.loading}}">
                        <div slot="c-name">
                            <s-button skin="stringfy" on-click="onViewDetail($event, row)">
                                {{row.name | renderName}}
                            </s-button>
                        </div>
                        <div slot="c-actionId" class="action-id-wrap">
                            <p class="clamp-1 clamp-action-id">{{row.actionId}}</p>
                            <s-clip-board text="{{row.actionId}}" />
                        </div>
                        <!--bca-disable-next-line-->
                        <div slot="c-status">{{row.status | renderStatus | raw}}</div>
                        <div slot="c-createTime">{{row.createTime | formatTime}}</div>
                        <div slot="c-endTime">{{row.endTime | formatTime}}</div>
                        <div slot="c-runningTime">{{row.runningTime | renderRunningTime}}</div>
                        <div slot="c-actions" class="col-actions">
                            <s-button skin="stringfy" on-click="onViewDetail($event, row)">查看</s-button>
                        </div>
                    </s-table>
                    <s-pagination
                        s-if="{{pager.count > 10}}"
                        layout="${PAGINATION_LAYOUT}"
                        total="{{pager.count}}"
                        pageSize="{{pager.pageSize}}"
                        page="{{pager.page}}"
                        pageSizes="{{pager.pageSizes}}"
                        max-item="{{7}}"
                        on-pagerChange="onPageChange"
                        on-pagerSizeChange="onPageSizeChange"
                    />
                </block-box>
            </div>
        </template>
    `;

    static components = {
        'block-box': BlockBox,
        's-table': Table,
        's-pagination': Pagination,
        's-select': Select,
        's-button': Button,
        's-clip-board': ClipBoard,
        'refresh-btn': RefreshBtn
    };

    static filters = {
        renderName: (name: ClusterActionType) => {
            return ClusterActionTypeList.getTextFromValue(name) || name;
        },
        renderStatus: (status: ClusterActionStatusType) => {
            return renderStatus(ClusterActionStatus.fromValue(status));
        },
        renderRunningTime: (runningTime: RunningTime) => {
            const dayStr = runningTime.day ? `${runningTime.day}天` : '';
            const hourStr = runningTime.hour ? `${runningTime.hour}时` : '';
            const minStr = runningTime.minute ? `${runningTime.minute}分` : '';
            const secondStr = runningTime.hour ? `${runningTime.second}秒` : '';
            return formatEmpty(dayStr + hourStr + minStr + secondStr);
        },
        formatTime
    };

    initData() {
        return {
            actionTypeList: ClusterActionTypeList.toArray(),
            actionType: '',
            table: {
                columns: [
                    {label: '任务类型', name: 'name'},
                    {label: '任务ID', name: 'actionId'},
                    {label: '任务状态', name: 'status', width: 100},
                    {label: '创建时间', name: 'createTime'},
                    {label: '结束时间', name: 'endTime'},
                    {label: '执行时长', name: 'runningTime'},
                    {label: '操作', name: 'actions', width: 120}
                ],
                ...TABLE_SUI
            },
            pager: {
                ...PAGER_SUI
            }
        };
    }

    attached() {
        this.getComList();
        this.onInterval();
    }

    onInterval() {
        if (this.stopPrevPolling) {
            this.stopPrevPolling();
        }
        this.stopPrevPolling = requestInterval(async () => await this.getTableList(), {
            shouldPoll: () => {
                // 后续可能需要根据状态判断是否需要轮询
                return true;
            }
        });
    }

    refresh() {
        this.getComList();
        this.onInterval();
    }

    async getTableList() {
        const {actionType, route} = this.data.get('');
        const {clusterId} = route.query;
        const {page, pageSize} = this.data.get('pager');
        const params = pickEmpty({
            pageNo: page,
            pageSize,
            name: actionType
        });
        const res = (await api.actionList({
            clusterId,
            params
        })) as ListPage<ActionResult>;
        this.data.set('table.datasource', res?.result || []);
        this.data.set('pager.count', res?.totalCount || 0);
    }

    onActionTypeChange() {
        this.nextTick(() => {
            this.getComList();
        });
    }

    onViewDetail(e: Event, row: ActionResult) {
        e && e.stopPropagation();
        const {route} = this.data.get('');
        const detailDrawer = new Operations({
            data: {
                row,
                route
            }
        });

        detailDrawer.attach(document.body);
    }

    detached() {
        this.stopPrevPolling && this.stopPrevPolling();
    }
}
