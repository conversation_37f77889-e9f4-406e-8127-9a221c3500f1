/**
 * 任务列表信息
 */
export interface ActionResult {
    /**
     * 任务 ID，action-20UUID
     */
    actionId: string;
    /**
     * 任务创建时间
     */
    createTime: string;
    /**
     * 任务结束时间
     */
    endTime: string;
    /**
     * 任务类型
     */
    name: string;
    /**
     * 任务执行时长
     */
    runningTime: RunningTime;
    /**
     * 任务状态
     */
    status: string;
    [property: string]: any;
}

/**
 * 任务执行时长
 *
 * RunningTime
 */
export interface RunningTime {
    day: number;
    hour: number;
    minute: number;
    /**
     * 运行总时长，单位：毫秒
     */
    runningTimeMs: number;
    second: number;
    [property: string]: any;
}
