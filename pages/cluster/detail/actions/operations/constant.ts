// RocketMQ任务管理映射规则 (文档维护)
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/qMApQ04r-e/c69Mh3zXaa/1K3MkzSEllP-rR
export const ClusterOperationsMap = {
    CHECK_BCC_RESOURCE_FOR_BROKER: '检查BCC资源余量',
    CHECK_CDS_RESOURCE_FOR_BROKER: '检查CDS资源余量',
    APPLY_DEPLOY_SET_RESOURCE: '申请部署集',
    APPLY_BCC_RESOURCE_FOR_BROKER: '申请Broker节点资源',
    APPLY_BCC_RESOURCE_FOR_NAMESERVER: '申请NameServer节点资源',
    APPLY_BLB_RESOURCE: '申请BLB资源',
    APPLY_VPC_RESOURCE: '申请VPC资源',
    APPLY_DOMAIN_RESOURCE_FOR_BROKER: '申请Broker域名资源',
    APPLY_DOMAIN_RESOURCE_FOR_NAMESERVER: '申请NameServer域名资源',
    APPLY_EIP_RESOURCE_FOR_BROKER: '申请Broker EIP资源',
    APPLY_EIP_RESOURCE_FOR_NAMESERVER: '申请NameServer EIP资源',
    APPLY_CDS_RESOURCE_FOR_BROKER: '申请CDS资源',
    RESIZE_NODE_TYPE_RESOURCE_FOR_BROKER: '节点机型变更',
    INIT_DEPLOY_ENVIRONMENT: '初始化部署环境',
    RESIZE_FS_CAPACITY_FOR_BROKER: '文件系统扩容',
    START_ROCKETMQ_SERVICE: '启动RocketMQ服务',
    RESTART_WORKER_SERVICE: '重启Worker服务',
    RESTART_BROKER_NODE: '重启机器',
    UPDATE_METADATA_FOR_CLUSTER: '更新集群元信息',
    UPDATE_METADATA_FOR_BROKER: '更新节点元信息',
    ADJUST_SECURITY_GROUP: '调整安全组',
    RELEASE_ALL_RESOURCE: '释放资源',
    NOTHING: '空执行'
};
