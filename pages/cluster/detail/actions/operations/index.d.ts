export enum OperationButton {
    // 展开
    EXPAND = 'expand',
    // 收起
    COLLAPSE = 'collapse'
}
export interface OperationResult {
    /** 任务名称 */
    name: string;
    /** 任务状态 */
    status: string;
    /** 操作列表 */
    operations: OperationItem[];
}

/**
 * 返回结果，任务操作列表项
 */
export interface OperationItem {
    /**
     * 任务 ID，action-20UUID
     */
    actionId: string;
    /**
     * 操作结束时间
     */
    endTime: string;
    /**
     * 执行阶段列表
     */
    groups: Group[];
    /**
     * 操作 ID，32UUID
     */
    operationId: string;
    /**
     * 操作进度
     */
    process: number;
    /**
     * 操作运行时间
     */
    runningTime: string;
    /**
     * 操作启动时间
     */
    startTime: string;
    /**
     * 操作状态
     */
    state: string;
    /**
     * 操作类型
     */
    type: string;
    [property: string]: any;
    /** 操作按钮状态 */
    buttonState?: OperationButton;
}

/**
 * 执行阶段列表项
 */
export interface Group {
    /**
     * 是否开启阶段分析，是否出现查看详情按钮
     */
    analysis: boolean;
    /**
     * 阶段名称
     */
    groupName: string;
    /**
     * 阶段状态
     */
    state: string;
    [property: string]: any;
}

// 集群任务详情
export interface OperationDetail {
    operationId: string;
    process: number;
    groups: Array<{
        groupName: string;
        state: string;
        analysis: boolean;
        diagnosis: string;
    }>;
    sourceContext: string;
    targetContext: string;
    pager?: {
        page: number;
        pageSize: number;
    };
}
