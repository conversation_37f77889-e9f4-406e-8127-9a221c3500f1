.page-action-detail {
    .s-drawer-wrapper {
        .s-drawer-header {
            display: flex;

            .s-drawer-header-title {
                flex: 1;
                display: flex;
                align-items: center;

                .status {
                    margin-left: 8px;
                }
            }

            .s-drawer-header-close {
                margin-left: 8px;
                margin-top: -1px;
            }
        }
    }

    &__refresh-btn {
        margin-left: auto;
        padding: 0;
    }

    &__steps {
        height: auto;

        &-item-title {
            display: flex;
        }

        @keyframes loadingCircle {
            to {
                transform: rotate(1turn);
            }
        }

        .x-icon-refresh-animation {
            display: inline-flex;
            animation: loadingCircle 1s linear infinite;
        }

        .icon-circle {
            display: inline-block;
            width: 22px;
            height: 22px;
            border-radius: 50%;
            font-size: 12px;

            &.icon-pending {
                background: #fff;
                border: 1px solid var(--border-color);
                color: var(--border-color);
            }

            &.icon-new-suspended {
                background: var(--blue-color);
                color: #fff;
            }
        }

        &-item-progress {
            position: relative;
            top: 8px;
            margin-left: 8px;
        }

        &-item-description {
            position: relative;
            z-index: 1;

            &-text {
                display: flex;
                align-items: center;

                .s-button {
                    padding: 0 16px;

                    .s-icon {
                        margin-left: 4px;
                        transition: transform 0.3s;
                        transform: rotate(180deg);
                    }
                }

                .is-expanded {
                    .s-icon {
                        transform: rotate(0deg);
                    }
                }
            }
        }
    }

    &__operation-detail {
        &-title {
            margin: 12px 0;
            font-size: 12px;
            color: #151b26;
            line-height: 22px;
            font-weight: 500;
        }

        .s-pagination {
            display: flex;
            flex-direction: row-reverse;
            margin-top: 24px;
        }
    }
}
