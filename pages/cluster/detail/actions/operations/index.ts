/* eslint-disable max-len */
/**
 * 任务子流程
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Drawer, Button, Steps, Progress, Table, Loading, Pagination} from '@baidu/sui';
import {
    ActionsStatus,
    ClusterActionStatusType,
    ClusterActionType,
    ClusterActionTypeList,
    OperationType,
    OperationTypeList
} from '@common/enums';
import {renderStatus} from '@common/html';
import './index.less';
import {ButtonRefresh, CheckCircle2, CloseCircle2} from '@baidu/xicon-san';
import {api} from '@common/client';
import {OperationResult, OperationButton, OperationItem, OperationDetail, Group} from './index.d';
import {formatTime, requestInterval} from '@common/utils';
import {MonacoDiffEditor} from '@baidu/san-monaco-editor';
import {ClusterOperationsMap} from './constant';
import {OutlinedDown} from '@baidu/sui-icon';

const klass = 'page-action-detail';

export class Operations extends Component {
    static template = html`
        <template>
            <s-drawer class="${klass}" open="{{open}}" size="{{800}}" on-close="onClose">
                <template slot="title">
                    {{row.name | renderName}} {{row.status | renderStatus | raw}}
                    <s-button skin="stringfy" on-click="onRefresh" class="${klass}__refresh-btn">刷新</s-button>
                </template>
                <s-steps direction="vertical" class="${klass}__steps" current="{{operationList.length}}">
                    <s-step s-for="item, index in operationList">
                        <span slot="icon">
                            <x-icon-refresh
                                s-if="item.state === ClusterActionStatusType.RUNNING || item.state === ClusterActionStatusType.PENDING"
                                slot="icon"
                                size="22"
                                color="#2468F2"
                                class="x-icon-refresh-animation"
                            />
                            <x-icon-close
                                s-elif="item.state === ClusterActionStatusType.FAILED"
                                slot="icon"
                                size="22"
                                color="#F33E3E"
                            />
                            <x-icon-check
                                s-elif="item.state === ClusterActionStatusType.FINISHED"
                                slot="icon"
                                size="22"
                                color="#2468F2"
                            />
                            <!-- 待执行、暂停：蓝底白字 -->
                            <span
                                s-elif="item.state === ClusterActionStatusType.NEW || item.state === ClusterActionStatusType.SUSPENDED"
                                class="icon-circle icon-new-suspended"
                            >
                                {{index + 1}}
                            </span>
                            <!-- 待调度、预备、取消：灰框灰字 -->
                            <span s-else class="icon-circle icon-pending">{{index + 1}}</span>
                        </span>
                        <div slot="title" class="${klass}__steps-item-title">
                            <span class="${klass}__steps-item-title-text">{{item.type | renderType}}</span>
                            <s-progress
                                s-if="item.state === ClusterActionStatusType.RUNNING"
                                class="${klass}__steps-item-progress"
                                percent="{{item.process}}"
                                width="{{160}}"
                            ></s-progress>
                        </div>
                        <div slot="description" class="${klass}__steps-item-description">
                            <template>
                                <div class="${klass}__steps-item-description-text">
                                    <template s-if="item.startTime">{{item.startTime | formatTime}}</template>
                                    <template s-if="item.endTime"> ~ {{item.endTime | formatTime}}</template>
                                    <template s-if="item.runningTime"> {{item.runningTime}}</template>
                                    <s-button
                                        class="{{expandList[index].buttonState === '${OperationButton.EXPAND}' ? 'is-expanded' : ''}}"
                                        skin="stringfy"
                                        on-click="onOperationClick($event, item, index)"
                                    >
                                        {{expandList[index] | renderButtonState}} <s-icon-down />
                                    </s-button>
                                </div>
                                <template s-if="{{expandList[index].buttonState === '${OperationButton.EXPAND}'}}">
                                    <div
                                        s-if="{{expandList[index].operationDetail}}"
                                        class="${klass}__operation-detail"
                                    >
                                        <div s-if="{{expandList[index].operationDetail.groups.length}}">
                                            <div class="${klass}__operation-detail-title">执行进度</div>
                                            <s-table
                                                columns="{{detailColumns}}"
                                                datasource="{{expandList[index].operationDetail.groups}}"
                                                has-expand-row="{{true}}"
                                                on-exprow-collapse="onRowCollapse($event, index)"
                                                on-exprow-expand="onRowExpand($event, index)"
                                            >
                                                <div slot="c-state">{{row.state | renderStatus | raw}}</div>
                                                <div slot="c-diagnosis">{{row.diagnosis || '-'}}</div>
                                                <div slot="sub-row">
                                                    <s-table
                                                        columns="{{kindColumns}}"
                                                        datasource="{{row.kinds}}"
                                                        loading="{{row.loading}}"
                                                    >
                                                        <div slot="c-state">{{row.state | renderStatus | raw}}</div>
                                                        <div slot="empty">暂无数据</div>
                                                    </s-table>
                                                    <s-pagination
                                                        class="${klass}__operation-detail-pagination"
                                                        s-if="{{row.pager.count > 5}}"
                                                        page="{{row.pager.page || 1}}"
                                                        pageSize="{{5}}"
                                                        total="{{row.pager.count || 0}}"
                                                        type="simple"
                                                        layout="pager, total"
                                                        on-pagerChange="onKindPageChange($event, index, rowIndex)"
                                                    ></s-pagination>
                                                </div>
                                            </s-table>
                                        </div>
                                        <div s-if="{{expandList[index].operationDetail.sourceContext}}">
                                            <div class="${klass}__operation-detail-title">执行内容</div>
                                            <s-monaco-diff
                                                s-ref="monaco"
                                                originalValue="{{expandList[index].operationDetail.sourceContext | formatJson}}"
                                                modifiedValue="{{expandList[index].operationDetail.targetContext | formatJson}}"
                                                language="json"
                                                height="{{300}}"
                                                width="{{700}}"
                                                readonly
                                            ></s-monaco-diff>
                                        </div>
                                    </div>
                                    <div s-else><s-loading loading /></div>
                                </template>
                            </template>
                        </div>
                    </s-step>
                </s-steps>
            </s-drawer>
        </template>
    `;

    static components = {
        's-drawer': Drawer,
        's-button': Button,
        's-steps': Steps,
        's-step': Steps.Step,
        's-pagination': Pagination,
        's-monaco-diff': MonacoDiffEditor,
        's-table': Table,
        's-loading': Loading,
        's-progress': Progress,
        'x-icon-refresh': ButtonRefresh,
        'x-icon-close': CloseCircle2,
        'x-icon-check': CheckCircle2,
        's-icon-down': OutlinedDown
    };

    stopPrevPolling: ReturnType<typeof requestInterval> | undefined;

    static filters = {
        renderName: (name: ClusterActionType) => {
            return ClusterActionTypeList.getTextFromValue(name) || name;
        },
        renderStatus: (state: ClusterActionStatusType) => {
            return renderStatus(ActionsStatus.fromValue(state));
        },
        formatTime,
        renderType: (type: OperationType) => {
            return OperationTypeList.getTextFromValue(type) || type;
        },
        renderButtonState: (item?: {buttonState: OperationButton}) => {
            return item?.buttonState === OperationButton.EXPAND ? '收起' : '展开';
        },
        formatJson: (json: string) => {
            try {
                return JSON.stringify(JSON.parse(json), null, 2);
            } catch (e) {
                return json;
            }
        }
    };

    initData() {
        return {
            open: false,
            // ---- 挂载时传入---- start
            row: {},
            route: {query: {}},
            // ---- 挂载时传入---- end
            operationList: [],
            ClusterActionStatusType,
            detailColumns: [
                {
                    label: '阶段名称',
                    name: 'groupName',
                    render: (row: {groupName: string}) =>
                        ClusterOperationsMap[row.groupName as keyof typeof ClusterOperationsMap] || row.groupName
                },
                {label: '状态', name: 'state', width: 100},
                {label: '诊断信息', name: 'diagnosis'}
            ],
            kindColumns: [
                {label: '子阶段名称', name: 'name'},
                {label: '执行状态', name: 'state', width: 100}
            ],
            expandList: []
        };
    }

    attached() {
        this.nextTick(() => {
            this.data.set('open', true);
        });

        this.getOperationList();
        this.onInterval();
    }

    onInterval() {
        if (this.stopPrevPolling) {
            this.stopPrevPolling();
        }
        this.stopPrevPolling = requestInterval(async () => await this.getOperationList());
    }

    async getOperationList() {
        const row = this.data.get('row');
        const {clusterId} = this.data.get('route.query');
        const {operations, name, status} = (await api.operationList({
            clusterId,
            actionId: row.actionId
        })) as OperationResult;

        const res = operations || [];

        // 如果无进行中的任务 则取消轮询
        if (
            !res.some(
                item => item.state === ClusterActionStatusType.RUNNING || item.state === ClusterActionStatusType.PENDING
            )
        ) {
            this.stopPrevPolling && this.stopPrevPolling();
        }

        this.data.set('operationList', res);
        this.data.merge('row', {
            name,
            status
        });
    }

    onRefresh() {
        this.getOperationList();
        this.onInterval();
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }

    detached() {
        this.stopPrevPolling && this.stopPrevPolling();
        // 取消轮询
        this.data.get('expandList')?.forEach((item: any) => {
            item?.stopPrevPolling && item.stopPrevPolling();
            item?.operationDetail?.groups?.forEach((group: any) => {
                group?.stopPrevPolling && group.stopPrevPolling();
            });
        });
    }

    onOperationClick(e: Event, item: OperationItem, index: number) {
        e.stopPropagation();
        const expandList = this.data.get('expandList');
        // 如果现在是展开的 则取消轮询 卸载数据
        if (expandList[index]?.buttonState === OperationButton.EXPAND) {
            expandList[index]?.stopPrevPolling && expandList[index].stopPrevPolling();
            this.data.set(`expandList[${index}]`, undefined);
        }
        // 如果现在是收起的 则请求详情数据 开始轮询
        else {
            this.getOperationDetail(item, index);
            this.data.merge(`expandList[${index}]`, {
                buttonState: OperationButton.EXPAND,
                stopPrevPolling: requestInterval(async () => await this.getOperationDetail(item, index))
            });
        }
    }

    // 获取集群任务操作详情
    async getOperationDetail(item: OperationItem, index: number) {
        const row = this.data.get('row');
        const {clusterId} = this.data.get('route.query');
        const res = (await api.operationDetail({
            clusterId,
            actionId: row.actionId,
            operationId: item.operationId
        })) as OperationDetail;
        const expandList = this.data.get('expandList');
        // 如果子任务没有running 则取消轮询
        if (
            !res.groups.some(
                item => item.state === ClusterActionStatusType.RUNNING || item.state === ClusterActionStatusType.PENDING
            )
        ) {
            const stopPrevPolling = expandList[index]?.stopPrevPolling;
            stopPrevPolling && stopPrevPolling();
        }
        this.data.merge(`expandList[${index}]`, {
            operationDetail: {
                ...res,
                groups: res.groups.map((item, rowIndex) => ({
                    ...(expandList[index]?.operationDetail?.groups[rowIndex] || {}),
                    ...item,
                    subSlot: item.analysis ? 'sub-row' : ''
                }))
            }
        });
    }

    onRowExpand(e: {value: {rowIndex: number}}, index: number) {
        const rowIndex = e.value.rowIndex;
        const expandList = this.data.get('expandList');
        const item = expandList[index]?.operationDetail?.groups[rowIndex];
        if (item?.analysis) {
            this.data.set(`expandList[${index}].operationDetail.groups[${rowIndex}].loading`, true);
            this.getOperationKinds(item, expandList[index]?.operationDetail, index, rowIndex);
            this.data.merge(`expandList[${index}].operationDetail.groups[${rowIndex}]`, {
                stopPrevPolling: requestInterval(
                    async () => await this.getOperationKinds(item, expandList[index]?.operationDetail, index, rowIndex)
                )
            });
        }
    }

    onRowCollapse(e: {value: {rowIndex: number}}, index: number) {
        const rowIndex = e.value.rowIndex;
        const expandList = this.data.get('expandList');
        const item = expandList[index]?.operationDetail?.groups[rowIndex];
        item?.stopPrevPolling && item.stopPrevPolling();
    }

    async getOperationKinds(item: Group, detail: OperationDetail, index: number, rowIndex: number) {
        const row = this.data.get('row');
        const {clusterId} = this.data.get('route.query');
        const res = await api.operationKinds({
            clusterId,
            actionId: row.actionId,
            operationId: detail.operationId,
            groupName: item.groupName,
            params: {
                pageNo: item.pager?.page || 1,
                pageSize: item.pager?.pageSize || 5
            }
        });

        // 如果没有进行中的任务 则取消轮询
        if (
            !res.result.some(
                (item: any) =>
                    item.state === ClusterActionStatusType.RUNNING || item.state === ClusterActionStatusType.PENDING
            )
        ) {
            const stopPrevPolling = this.data.get(
                `expandList[${index}].operationDetail.groups[${rowIndex}].stopPrevPolling`
            );
            stopPrevPolling && stopPrevPolling();
        }
        this.data.merge(`expandList[${index}].operationDetail.groups[${rowIndex}]`, {
            loading: false,
            kinds: res.result,
            pager: {
                page: res.pageNo,
                pageSize: res.pageSize,
                count: res.totalCount
            }
        });
    }

    onKindPageChange(e: {value: {page: number; pageSize: number}}, index: number, rowIndex: number) {
        const expandList = this.data.get('expandList');
        const item = expandList[index]?.operationDetail?.groups[rowIndex];
        // 取消上个页面的轮询
        const stopPrevPolling = expandList[index]?.operationDetail?.groups[rowIndex]?.stopPrevPolling;
        stopPrevPolling && stopPrevPolling();
        // 更新当前页码
        const newItem = {...item, pager: {...item.pager, ...e.value}};

        // 获取新页码
        this.getOperationKinds(newItem, expandList[index]?.operationDetail, index, rowIndex);

        // 开始新页面的轮询
        this.data.merge(`expandList[${index}].operationDetail.groups[${rowIndex}]`, {
            loading: true,
            pager: {...e.value},
            stopPrevPolling: requestInterval(
                async () => await this.getOperationKinds(newItem, expandList[index]?.operationDetail, index, rowIndex)
            )
        });
    }
}
