/**
 * 价格项
 * @file index
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
const klass = 'price-item';
import './index.less';

export class PriceItem extends Component {
    static template = html`
        <div class="${klass}">
            <div class="${klass}__item-title}">
                <slot name="title">{{title}}</slot>
            </div>
            <div class="${klass}__item-price-info">
                <span class="price">{{price}}</span>
                <span class="price-desc">{{desc}}</span>
            </span>
        </div>
    `;

    initData() {
        return {
            title: '',
            price: '',
            desc: '',
        };
    }

    attached() {}
}
