/**
 * 集群更多操作：用于列表和详情
 * @file index.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Menu, Dropdown, Notification, Button, Tooltip, Link} from '@baidu/sui';
import {CLUSTER_MORE_OPERATIONS, ClusterOperationType} from '@common/enums';
import {IClusterItem, IFeClusterItem} from '@pages/cluster/list/index.d';
import {OutlinedDown} from '@baidu/sui-icon';
import {TICKET_LINK} from '@common/docs';
import {SingleDialog} from '@components/single-dialog';
import {api} from '@common/client';
import {redirect} from '@baiducloud/runtime';
import {ROUTE_PATH} from '@common/config';
import './index.less';
const klass = 'cluster-operations';

export class ClusterOperations extends Component {
    static template = html`
        <template>
            <s-dropdown class="${klass}">
                <s-menu slot="overlay">
                    <s-menu-item s-if="row" s-for="n, index in clusterOperationList" key="{{index}}">
                        <s-tooltip placement="left" class="${klass}__tooltip {{!n.tipText ? 'tooltip-hidden': ''}}">
                            <s-button
                                on-click="handleMenuItemClick(row, n)"
                                disabled="{{n.disabled}}"
                                skin="{{menuBtnSkin}}"
                                style="padding: 0;"
                            >
                                {{n.text}}
                            </s-button>
                            <!--bca-disable-next-line-->
                            <div s-if="{{n.tipText}}" slot="content">{{n.tipText | raw}}</div>
                        </s-tooltip>
                    </s-menu-item>
                </s-menu>
                <s-button skin="{{moreBtnSkin}}">更多 <s-icon-down /></s-button>
            </s-dropdown>
            <x-single-dialog s-ref="singleDialog" />
        </template>
    `;

    static computed: SanComputedProps = {
        clusterOperationList() {
            const row = this.data.get('row') as IFeClusterItem;
            if (!row) {
                return [];
            }
            const moreList = this.data.get('moreList') as EnumItem[];
            return moreList.map(item => {
                let disabled = false;
                let tipText = '';
                switch (item.value) {
                    case ClusterOperationType.DELETE:
                        disabled = !row.canDelete;
                        tipText = !row.canDelete
                            ? `集群删除，请<a href="${TICKET_LINK}" target="blank" class="${klass}__ticket-link">提交工单</a>`
                            : '';
                        break;
                    case ClusterOperationType.STOP:
                        disabled = !row.canStop;
                        break;
                    case ClusterOperationType.START:
                        disabled = !row.canStart;
                        break;
                    case ClusterOperationType.SCALE:
                        disabled = row.isAllUpdateMax || !row.canUpdate;
                        tipText = row.isAllUpdateMax ? '当前集群规格已达系统上限，不支持进行变更' : '';
                        break;
                    case ClusterOperationType.PAYMENT_UPDATE:
                        disabled = !row.canUpdatePayment;
                        break;
                    default:
                }
                return {
                    ...item,
                    tipText,
                    disabled
                };
            });
        }
    };

    static components = {
        's-dropdown': Dropdown,
        's-menu': Menu,
        's-menu-item': Menu.Item,
        's-icon-down': OutlinedDown,
        'x-single-dialog': SingleDialog,
        's-button': Button,
        's-tooltip': Tooltip,
        's-link': Link
    };

    initData() {
        return {
            moreBtnSkin: 'stringfy',
            menuBtnSkin: 'normal-stringfy',
            moreList: CLUSTER_MORE_OPERATIONS.toArray()
        };
    }

    handleMenuItemClick(row: IClusterItem, n: EnumItem) {
        const from = this.data.get('from');
        const urlSuffix = from ? `&from=${from}` : '';
        switch (n.value) {
            case ClusterOperationType.SCALE:
                redirect(`#${ROUTE_PATH.clusterScale}?clusterId=${row.clusterId}${urlSuffix}`);
                break;
            case ClusterOperationType.TICKET:
                // 提交工单
                window.open(TICKET_LINK, '_blank');
                break;
            case ClusterOperationType.DELETE:
                this.onDelete(row);
                break;
            case ClusterOperationType.STOP:
                this.onStop(row);
                break;
            case ClusterOperationType.START:
                this.onStart(row);
                break;
            case ClusterOperationType.PAYMENT_UPDATE:
                redirect(`#${ROUTE_PATH.clusterPaymentUpdate}?clusterId=${row.clusterId}${urlSuffix}`);
                break;
            default:
        }
    }

    // 删除
    onDelete(row: IClusterItem) {
        const contentHtml = html`<div>
            <div>集群：${row.name}</div>
            <div>删除后集群无法恢复，您确定删除此集群吗？</div>
        </div>`;
        this.ref('singleDialog').open('提示', contentHtml, async () => {
            await api.clusterDelete({
                clusterId: row.clusterId
            });
            Notification.success(`集群${row.name}删除成功`);
            this.fire('delete-success', {});
        });
    }

    // 停止集群
    onStop(row: IClusterItem) {
        const contentHtml = html`<div>
            <div>集群：${row.name}</div>
            <div>您确定停止此集群吗？</div>
        </div>`;
        this.ref('singleDialog').open('提示', contentHtml, async () => {
            await api.clusterStop({
                clusterId: row.clusterId
            });
            Notification.success(`集群${row.name}停止成功`);
            this.fire('refresh', {});
        });
    }

    // 启动
    onStart(row: IClusterItem) {
        const contentHtml = html`<div>
            <div>集群：${row.name}</div>
            <div>您确定启动此集群吗？</div>
        </div>`;
        this.ref('singleDialog').open('提示', contentHtml, async () => {
            await api.clusterStart({
                clusterId: row.clusterId
            });
            Notification.success(`集群${row.name}启动成功`);
            this.fire('refresh', {});
        });
    }
}
