.m-order-confirm-item {
    .price-info {
        display: flex;

        &-content {
            font-size: 12px;
            color: #e02020;
            line-height: 20px;
            font-weight: 500;
        }

        &-item:not(:last-child) {
            margin-right: 16px;
        }
    }

    .s-legend-label {
        flex: 1;
    }

    .s-detail-cell .detail-cell:has(.sub-cell) {
        display: flex;

        .value {
            flex: 1;
        }
    }

    .sub-cell {
        .s-detail-cell {
            display: flex;
            margin-top: 12px;
            background: var(--background-color);
            border-radius: 6px;
            padding: 16px;

            .detail-cell {
                flex: 1;
                margin-right: 16px;
                margin-bottom: 0;

                label {
                    width: auto;
                }
            }
        }
    }
}
