import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {BlockBox} from '@components/index';
import {AppDetailCell} from '@baidu/sui-biz';
import './index.less';

const klass = 'm-order-confirm-item';
export class OrderConfirmItem extends Component {
    static template = html`
        <div class="${klass} {{item.class}}">
            <block-box title="{{item.title}}">
                <div slot="extra" class="price-info">
                    <div class="price-info-item" s-for="priceItem, index in priceList">
                        <span class="price-info-title">{{priceItem.title}}</span>
                        <span class="price-info-content">{{priceItem.price}}</span>
                    </div>
                </div>
                <biz-detail-cell datasource="{{item.datasource}}">
                    <span slot="c-sub-cell" class="sub-cell">
                        <span>{{item.value}}</span>
                        <biz-detail-cell datasource="{{item.subDatasource}}" divide="{{item.subDivide}}" />
                    </span>
                </biz-detail-cell>
            </block-box>
        </div>
    `;

    static components = {
        'block-box': BlockBox,
        'biz-detail-cell': AppDetailCell
    };

    static computed: SanComputedProps = {
        priceList() {
            const item = this.data.get('item');
            if (!item?.priceInfo) {
                return [];
            }
            const {priceInfo} = item;

            return Array.isArray(priceInfo) ? priceInfo : [priceInfo];
        }
    };

    initData() {
        return {
            item: {
                title: '',
                priceInfo: {
                    title: '',
                    price: ''
                },
                datasource: []
            }
        };
    }
}
