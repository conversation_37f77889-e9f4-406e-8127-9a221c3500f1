/**
 * 代金券
 *
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Form, Button, Input, Link, Notification} from '@baidu/sui';
import {OutlinedCheck, OutlinedClose, OutlinedPlus, OutlinedLink} from '@baidu/sui-icon';
import {AppLegend} from '@baidu/sui-biz';

import './index.less';
import {BlockBox, CreateBtn, TipSelect} from '@components/index';
import {THIRD_ROUTE_PATH} from '@common/config';
import {thirdApi} from '@common/client';
import _ from 'lodash';
import {debounce, throttle} from '@common/decorators';
import {VALIDATE_ITEMS} from '@common/rules';

const klass = 'm-coupon';

const TextMap = {
    title: '代金券',
    activeCoupon: '代金券号码：',
};

export class CouponConfig extends Component {
    static template = html`
        <div class="${klass}">
            <m-block-box title="代金券">
                <div slot="extra" class="label-extra">
                    <create-btn on-click="addCoupon" skin="stringfy" disabled="{{showActiveInput}}">
                        激活代金券
                    </create-btn>

                    <s-link href="{{THIRD_ROUTE_PATH.couponList}}" target="blank" skin="primary">
                        <s-icon-link color="#2468f2" /> 代金券管理
                    </s-link>
                    <span class="${klass}__decount-price">
                        <span class="mr10">抵扣金额：</span>
                        <span class="price">¥{{decountPrice}}</span>
                    </span>
                </div>
                <div class="${klass}__content">
                    <s-form s-ref="form" class="${klass}__content__form" rules="{{rules}}" data="{{formData}}">
                        <s-form-item s-if="{{showActiveInput}}" label="${TextMap.activeCoupon}" prop="couponName">
                            <s-input
                                placeholder="{{placeholder}}"
                                value="{= formData.couponName =}"
                                on-input="onCouponNameInput"
                            />
                            <s-button skin="primary" on-click="activeCoupon">
                                <s-icon-confirm class="button-icon" is-button="{{false}}" />
                            </s-button>
                            <s-button on-click="clearCoupon">
                                <s-icon-cancel class="button-icon" is-button="{{false}}" />
                            </s-button>
                        </s-form-item>
                        <p class="mb20">代金券（{{couponList.length > 0 ? couponList.length - 1 : 0}}张可用）</p>
                        <s-form-item label="产品名称： RocketMQ" prop="coupon">
                            <tip-select
                                datasource="{{couponList}}"
                                value="{= formData.coupon =}"
                                on-change="onSelectChange"
                            />
                        </s-form-item>
                    </s-form>
                </div>
            </m-block-box>
        </div>
    `;

    static components = {
        'm-block-box': BlockBox,
        'create-btn': CreateBtn,
        's-link': Link,
        's-form': Form,
        's-form-item': Form.Item,
        's-append': AppLegend,
        'tip-select': TipSelect,
        's-button': Button,
        's-icon-confirm': OutlinedCheck,
        's-icon-cancel': OutlinedClose,
        's-icon-add': OutlinedPlus,
        's-icon-link': OutlinedLink,
        's-input': Input,
    };

    initData() {
        return {
            formData: {
                coupon: '',
                couponName: '',
            },
            rules: {
                couponName: [
                    VALIDATE_ITEMS.requiredInput,
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (this.data.get('errMsg')) {
                                return callback('代金券无效');
                            }
                            callback();
                        },
                    },
                ],
            },
            couponList: [],
            placeholder: '请输入代金券号码',
            showActiveInput: false,
            showError: false,
            THIRD_ROUTE_PATH,
            loading: {
                active: false,
            },
        };
    }

    attached() {
        this.getCouponList();
    }

    async getCouponList() {
        let param = {
            // serviceType,
            serviceType: 'KAFKA',
            region: 'bj',
            properties: [],
        };
        const result = await thirdApi.getCouponList(param);

        let couponList = [{text: '不选择代金券', value: '', balance: 0}];
        _.each(result, (coupon) => {
            couponList.push({
                ...coupon,
                text: this.formatText(coupon),
                value: coupon.id,
            });
        });

        this.data.set('couponList', couponList);
    }

    onCouponNameInput(e: {value: string}) {
        this.data.set('errMsg', '');
    }

    addCoupon() {
        this.data.set('showActiveInput', true);
    }

    onSelectChange(target: {value: string}) {
        const couponId = target.value;
        const couponList = this.data.get('couponList');
        const currentSelectCoupon = couponList.find((item) => item.id === couponId);
        this.fire('coupon-select-change', {currentSelectCoupon});
    }

    // 激活代金券事件
    @throttle(500, {trailing: false})
    async activeCoupon() {
        const {couponName} = this.data.get('formData');
        try {
            this.data.set('loading.active', true);
            this.data.set('errMsg', '');
            await thirdApi.activeCoupon({
                couponName,
            });
            this.getCouponList();
            Notification.success('代金券激活成功');
        } catch (err) {
            console.error(err);
            this.data.set('errMsg', '代金券无效');
        } finally {
            this.ref('form').validateFields();
            this.data.set('loading.active', false);
        }
    }

    formatText(coupon: {balance: string; productRuleDescription: string; conditionEffectDescription: string}) {
        let content =
            `￥${coupon.balance}` +
            ' ' +
            coupon.productRuleDescription +
            (coupon.conditionEffectDescription ? '（' + coupon.conditionEffectDescription + '）' : '');
        return content;
    }

    // 关闭代金券输入框
    clearCoupon() {
        this.data.set('formData.couponName', '');
        this.data.set('showActiveInput', false);
    }
}
