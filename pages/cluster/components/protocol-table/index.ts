/**
 * 访问协议表格
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Table} from '@baidu/sui';
import {ClipBoard} from '@baidu/sui-biz';
import {MultiToneSuccess, MultiToneError} from '@baidu/sui-icon';
import {formatEmpty} from '@common/utils';

export class ProtocolTable extends Component {
    static template = html`
        <template>
            <s-table class="protocol-table" columns="{{columns}}" datasource="{{datasource}}">
                <div slot="c-aclEnable">
                    <s-icon-succ s-if="row.aclEnable"></s-icon-succ>
                    <s-icon-err s-else></s-icon-err>
                </div>
                <div slot="c-encryptEnable">
                    <s-icon-succ s-if="row.encryptEnable"></s-icon-succ>
                    <s-icon-err s-else></s-icon-err>
                </div>
                <!-- 集群详情展示访问地址 -->
                <div slot="c-endpoint">
                    {{row.endpoint | formatEmpty}}
                    <s-clip-board s-if="row.endpoint" text="{{row.endpoint}}" />
                </div>
            </s-table>
        </template>
    `;

    static components = {
        's-table': Table,
        's-icon-succ': MultiToneSuccess,
        's-icon-err': MultiToneError,
        's-clip-board': ClipBoard,
    };

    static filters = {
        formatEmpty,
    };

    initData() {
        return {
            /** ----- 组件传入的数据 ---- start */
            columns: [],
            datasource: [],
            /** ----- 组件传入的数据 ---- end */
        };
    }
}
