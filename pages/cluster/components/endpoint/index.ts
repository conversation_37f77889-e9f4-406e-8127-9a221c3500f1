/**
 * 接入点
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Form} from '@baidu/sui';
import {ClipBoard} from '@baidu/sui-biz';
import {api, API_PREFIX} from '@common/client';
import {throttle} from '@common/decorators';

import './index.less';
import {EncryptionInTransitType} from '@common/enums';
import {downloadSSL, formatEmpty, isSsLPermissive} from '@common/utils';

const klass = 'show-endpoint';

export class EndPoint extends Component {
    static template = html`
        <template>
            <s-dialog
                title="接入点信息"
                open="{{open}}"
                width="500"
                class="${klass}"
                on-close="onClose"
                on-confirm="onConfirm"
            >
                <s-form>
                    <s-form-item label="集群名称："> {{clusterName}} </s-form-item>
                    <s-form-item label="接入点地址：">
                        <div s-for="item, index in nameServerEndpoints">
                            <span class="${klass}__endpoints_text">{{item.endpoint | formatEmpty}}</span>
                            <s-clip-board s-if="item.endpoint" class="clipboard-default" text="{{item.endpoint}}" />
                        </div>
                        <div s-if="isSsLPermissive" class="ssl-download">
                            开启了传输加密，请下载证书文件：
                            <s-button class="${klass}__certification" on-click="downloadSSL" skin="stringfy">
                                rocketmq-key.zip
                            </s-button>
                        </div>
                    </s-form-item>
                </s-form>
            </s-dialog>
        </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-button': Button,
        's-clip-board': ClipBoard
    };

    static computed = {
        isSsLPermissive(): boolean {
            const encryptionInTransit = this.data.get('encryptionInTransit') as EncryptionInTransitType[];
            return isSsLPermissive(encryptionInTransit);
        }
    };

    static filters = {
        formatEmpty
    };

    initData() {
        return {
            open: true
        };
    }

    // 确认
    async onConfirm() {
        this.onClose();
    }

    // 下载证书文件
    @throttle(1000)
    async downloadSSL() {
        const clusterId = this.data.get('clusterId');
        await downloadSSL({api: api.certs(clusterId)});
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
