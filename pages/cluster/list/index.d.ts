/**
 * 集群列表信息
 */

import {ArchType, AuthenticationModeType, EncryptionInTransitType} from '@common/enums';
import {ITag} from './tag-edit/index.d';
export type IClusterList = ListPage<IClusterItem>;

export interface IClusterItem {
    /**
     * 是否开启 ACL 鉴权，是否开启 ACL 鉴权；如果使用 SASL_PLAIN，则为 true；如果使用NONE，则为 false
     */
    aclEnabled: boolean;
    /**
     * 部署架构
     */
    arch: ArchType;
    /**
     * 认证方式列表
     */
    authenticationModes: AuthenticationModeType[];
    /** 是否可以删除 */
    canDelete: boolean;
    /** 是否可以停止集群 */
    canStop: boolean;
    /** 是否可以启动集群 */
    canStart: boolean;
    /**
     * 节点磁盘是否达到最大规格，如果为true则表示当前节点磁盘达到最大规格，不允许磁盘变更
     */
    isBrokerDiskCapacityMax: boolean;
    /**
     * 节点规格是否达到最大规格，如果为true则表示当前节点规格达到最大规格，不允许规格变更
     */
    isBrokerNodeTypeMax: boolean;
    /**
     * 节点数量是否达到最大规格，如果为true则表示当前集群数量达到最大规格，不允许数量变更
     */
    isBrokerNumberMax: boolean;
    /**
     * 集群 ID
     */
    clusterId: string;
    /**
     * 集群创建时间，格式：格式 2021-09-18T11:30:01Z
     */
    createTime: string;
    /**
     * 到期时间，预付费需要展示集群到期时间
     */
    expiration: string;
    /**
     * 可用区列表
     */
    zoneNames: string[];
    /**
     * 集群名称
     */
    name: string;
    /**
     * 付费方式
     */
    payment: string;
    /**
     * 集群状态
     */
    status: string;
    /**
     * 标签列表
     */
    tags: ITag[];
    /**
     * 集群版本
     */
    version: string;
    /**
     * 传输加密方式
     */
    encryptionInTransit: EncryptionInTransitType[];
    [property: string]: any;
}

export interface IFeClusterItemCustomProp {
    /** 是否可以变更规格：仅ACTIVE状态下才能变更 */
    canUpdate: boolean;
    /** 是否所有变更类型均已达最大限制: isBrokerNumberMax、isBrokerNodeTypeMax、isBrokerDiskCapacityMax均为true时 */
    isAllUpdateMax: boolean;
    /** 是否可以计费变更 */
    canUpdatePayment: boolean;
}

export type IFeClusterItem = IClusterItem & IFeClusterItemCustomProp;

/**
 * 当前集群允许的操作，当前集群的允许的操作列表
 */
export enum IAllowedActions {
    DeleteCluster = 'DELETE_CLUSTER',
    StartCluster = 'START_CLUSTER',
    StopCluster = 'STOP_CLUSTER',
    UpdateBrokerDiskCapacity = 'UPDATE_BROKER_DISK_CAPACITY',
    UpdateBrokerNodeType = 'UPDATE_BROKER_NODE_TYPE',
    UpdateBrokerNumber = 'UPDATE_BROKER_NUMBER'
}

/**
 * 返回结果，返回一个对象
 */
export interface AccessEndpointResult {
    /**
     * nameServer地址列表
     */
    nameServerEndpoints: AccessEndpoint[];
    [property: string]: any;
}

/**
 * nameServer地址列表项
 *
 * AccessEndpoint
 */
export interface AccessEndpoint {
    /**
     * 通信协议，可选：remoting \ gRpc
     */
    communicationProtocol: string;
    /**
     * 接入点地址
     */
    endpoint: string;
    [property: string]: any;
}
