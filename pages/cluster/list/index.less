.page-cluster-list {
    min-width: 1100px;

    .third-entry {
        position: relative;
        display: flex;
        align-items: center;
        background-image: linear-gradient(270deg, #eef3ff 6%, #ecf0f9 100%);
        padding: 0 0 0 16px;

        .horn-icon {
            background-image: url(~@static/img/horn.png);
            width: 37px;
            height: 37px;
            background-size: cover;
            margin-right: 6px;
        }

        .bg {
            width: 255px;
            height: 48px;
            background-image: url(~@static/img/list-banner.png);
            margin-left: auto;
        }

        .close-btn {
            position: absolute;
            right: 16px;
        }
    }

    .table-filter {
        display: flex;

        .s-search-box {
            height: 32px;
            border-color: #d4d6d9;

            .s-cascader-value {
                min-height: 30px;
            }
        }
    }

    .s-table {
        .s-button.s-button-skin-stringfy {
            padding-left: 0;
        }

        // .s-table-hcell-fixed:nth-child(2),
        // .s-table-row .s-table-cell-fixed:nth-child(2) {
        //     // selection列的宽度，随着页面拉伸，宽度会变大，且设置定宽不管用, 暂时这么写
        //     left: 42px !important;
        // }

        // .s-table-row:hover {
        //     background-color: rgb(230, 240, 254);
        // }

        .s-table-empty {
            padding: 0;
            margin-top: 120px;
        }
    }

    .s-table .s-table-thead .filter-icon.filter-selected .x-icon {
        color: var(--text-primary-color) !important;
    }

    .s-table .s-table-hcell-createTime .s-table-hcell-text .s-table-hcell-text-content {
        display: inline-flex;
    }

    .cluster-id-wrap {
        display: flex;

        .clamp-cluster-id {
            display: inline-block;
            max-width: calc(~'100% - 20px');
            margin-right: 4px;
        }
    }
}
