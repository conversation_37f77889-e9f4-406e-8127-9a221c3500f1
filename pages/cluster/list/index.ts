/**
 * 集群列表
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {Pagination, Table, Button, Notification, Menu, Dropdown, Loading, Tooltip, Link} from '@baidu/sui';
import {AppListPage, SearchBox, Empty, TableColumnToggle, ClipBoard} from '@baidu/sui-biz';
import {AnimationAcgLoading} from '@baidu/sui-icon';
import {
    TABLE_SUI,
    PAGER_SUI,
    ROUTE_PATH,
    PAGINATION_LAYOUT,
    SELECTION_SUI_MULTI,
    THIRD_ROUTE_PATH
} from '@common/config';
import {api, thirdApi} from '@common/client';
import {
    pickEmpty,
    formatTime,
    TagFilter,
    renderArrZoneLabel,
    formatToggleColumnData,
    filterTableColumns,
    formatArch,
    requestInterval,
    formatClusterItem,
    isOneCloudId
} from '@common/utils';
import {ClusterStatus, ALL_ENUM, PaymentType, CLUSTER_MORE_OPERATIONS, ArchList} from '@common/enums';
import {CommonTable, CreateBtn, ListTitle, EllipsisTip, DocLink, RefreshBtn} from '@components/index';
import {renderStatus} from '@common/html';

import './index.less';
import {AccessEndpointResult, IClusterItem} from './index.d';
import TagDialog from './tag-edit';
import {debounce} from '@common/decorators';
import {EndPoint, ClusterOperations} from '@pages/cluster/components';
import {Close1, Filter1} from '@baidu/xicon-san';
const klass = 'page-cluster-list';

export type VersionItem = {
    version: string;
};

const $flag = ServiceFactory.resolve('$flag');

const BackSelectPrefix = 's-table';
const allEnum = ALL_ENUM.toArray();
export class VersionFilter extends Component {
    static template = html` <span>
        <s-loading class="mt5" loading s-if="{{loading}}" size="small" />
        <s-dropdown class="${BackSelectPrefix}-filter" s-else>
            <s-menu slot="overlay" class="${BackSelectPrefix}-filter-menu" on-click="onFilter($event)">
                <s-menu-item
                    s-for="item, index in versions"
                    class="${BackSelectPrefix}-filter-item {{value === item.value ? 'item-selected': ''}}"
                    key="{{item.value}}"
                >
                    <span>{{item.text}}</span>
                </s-menu-item>
            </s-menu>
            <x-icon-filter class="filter-icon {{value ? 'filter-selected' : ''}}" />
        </s-dropdown>
    </span>`;

    static components = {
        'x-icon-filter': Filter1,
        's-dropdown': Dropdown,
        's-menu': Menu,
        's-menu-item': Menu.Item,
        's-loading': Loading
    };

    initData() {
        return {
            value: ALL_ENUM.ALL,
            // 版本列表由组件传入
            versions: []
        };
    }

    attached() {
        const value = this.data.get('value');
        value && this.data.set('value', value);
    }

    // 过滤
    onFilter(target: {value: string}) {
        this.data.set('value', target.value);
        this.fire('filter', {version: target.value});
    }
}

// 表格全量列，根据该数据
const TableColumns: Array<ToggleTableColumn> = [
    {
        name: 'name',
        label: '集群名称/ID',
        width: 200,
        fixed: 'left',
        disabled: true
    },
    {
        name: 'status',
        label: '状态',
        filter: {
            options: [...allEnum, ...ClusterStatus.toArray()],
            value: allEnum[0].value
        },
        width: 140,
        disabled: true
    },
    {
        name: 'payment',
        label: '付费方式',
        filter: {
            options: [...allEnum, ...PaymentType.toArray()],
            value: allEnum[0].value
        },
        width: 110,
        defaultShow: true
    },
    {
        name: 'arch',
        label: '部署架构',
        filter: {
            options: [...allEnum, ...ArchList.toArray()],
            value: allEnum[0].value
        },
        width: 100,
        defaultShow: true
    },
    {
        name: 'version',
        label: '版本',
        filter: {
            options: [],
            value: ''
        },
        width: 90,
        defaultShow: true
    },
    {
        name: 'zoneNames',
        label: '可用区',
        width: 150,
        defaultShow: true
    },
    {
        name: 'createTime',
        label: '创建时间',
        sortable: true,
        render: (item: IClusterItem) => formatTime(item.createTime),
        width: 150,
        defaultShow: true
    },
    {
        name: 'tags',
        label: '集群标签',
        width: 130
    },
    {
        name: 'operation',
        label: '操作',
        width: 150,
        fixed: 'right',
        disabled: true
    }
];

const toggleColumn = formatToggleColumnData(TableColumns);

@decorators.asPage(ROUTE_PATH.clusterList, '/')
@decorators.withSidebar({active: ROUTE_PATH.clusterList})
export default class ClusterList extends CommonTable {
    static template = html` <div class="${klass}">
        <!--bca-disable-->
        <app-list-page class="${klass}_content">
            <div slot="pageTitle" class="${klass}_content-header">
                <div s-if="showBanner" class="third-entry">
                    <i class="horn-icon"></i>
                    <span>快速入口：</span>
                    <s-link href="${THIRD_ROUTE_PATH.kafkaClusterCreate}" target="blank" skin="primary" class="ml6">
                        购买Kafka集群
                    </s-link>
                    <s-link href="${THIRD_ROUTE_PATH.rabbitMQClusterCreate}" target="blank" skin="primary" class="ml16">
                        购买RabbitMQ集群
                    </s-link>
                    <i class="bg"></i>
                    <s-button skin="normal-stringfy" on-click="onCloseBanner" class="close-btn">
                        <x-icon-close size="18" />
                    </s-button>
                </div>
                <list-title title="集群列表" showHelpDoc />
            </div>
            <div slot="bulk">
                <create-btn on-click="onCreate">创建集群</create-btn>
                <s-button
                    s-if="{{!isOneCloud}}"
                    class="ml8"
                    disabled="{= !selection.selectedIndex.length =}"
                    on-click="onEditTag"
                >
                    编辑标签
                </s-button>
            </div>
            <div slot="filter" class="table-filter">
                <s-searchbox
                    class="cluster-search-box"
                    placeholder="{{searchbox.placeholder}}"
                    on-keywordTypeChange="onKeywordTypeChange"
                    text-datasource="{{searchbox.textDataSource}}"
                    value="{= searchbox.keyword =}"
                    keyword-type="{= searchbox.keywordType =}"
                    datasource="{{searchbox.keywordTypes}}"
                    on-search="onSearch"
                    width="170"
                />
                <refresh-btn on-click="onRefresh" class="ml8" />
                <s-table-column-toggle
                    datasource="{{toggleColumn.datasource}}"
                    value="{{toggleColumn.value}}"
                    on-change="onTableColumnSelect"
                    class="ml8"
                />
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                selection="{{selection}}"
                on-selected-change="onSelectChange"
                on-sort="onSort"
                on-filter="onFilter"
            >
                <version-filter
                    slot="h-version-filter"
                    on-filter="onVersionFilter"
                    value="{{version}}"
                    loading="{{loading.versions}}"
                    versions="{{versions}}"
                />
                <div slot="c-name" class="${klass}__instant">
                    <div>
                        <s-link href="#${ROUTE_PATH.clusterDetail}?&clusterId={{row.clusterId}}" skin="primary">
                            {{row.name}}
                        </s-link>
                    </div>
                    <div class="cluster-id-wrap">
                        <p class="clamp-1 clamp-cluster-id">{{row.clusterId}}</p>
                        <s-clip-board text="{{row.clusterId}}" />
                    </div>
                </div>
                <div slot="c-payment" class="${klass}__instant">
                    <div>{{row.payment | filterPayment}}</div>
                    <div s-if="row.payment === PaymentType.Prepaid">{{row.expiration}}</div>
                </div>
                <div slot="c-arch">{{row.arch | formatArch}}</div>
                <div slot="c-status">{{row.status | filterStatus | raw}}</div>
                <div slot="c-zoneNames">{{row.zoneNames | renderArrZoneLabel}}</div>
                <div slot="c-tags">
                    <span class="tag-text" s-if="{{!row.tags || row.tags.length < 1}}">-</span>
                    <s-tooltip s-else class="tag-tip">
                        <div slot="content" class="tag-tip-content">{{row | tagTip | raw}}</div>
                        <div class="tag-text">{{row | tagText | raw}}</div>
                    </s-tooltip>
                </div>
                <div slot="c-operation">
                    <s-button skin="stringfy" class="table-btn-slim" on-click="onIpGet(row)"> 接入点 </s-button>
                    <cluster-operations on-refresh="onRefresh" row="{{row}}" on-delete-success="handleDeleteSuccess" />
                </div>
                <div slot="empty">
                    <s-empty vertical on-click="onCreate" emptyText="暂无数据。" />
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="${PAGINATION_LAYOUT}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-table-column-toggle': TableColumnToggle,
        's-pagination': Pagination,
        's-button': Button,
        'ellipsis-tip': EllipsisTip,
        's-link': Link,
        'doc-link': DocLink,
        'create-btn': CreateBtn,
        'version-filter': VersionFilter,
        'refresh-btn': RefreshBtn,
        's-tooltip': Tooltip,
        's-empty': Empty,
        'list-title': ListTitle,
        's-loading': Loading,
        'x-icon-close': Close1,
        's-clip-board': ClipBoard,
        's-icon-loading': AnimationAcgLoading,
        'cluster-operations': ClusterOperations
    };

    static filters: SanFilterProps = {
        filterStatus(status: string) {
            let temp = renderStatus(ClusterStatus.fromValue(status));
            if (_.includes([ClusterStatus.DEPLOYING], status)) {
                temp += html`<div class="desc">预计需要10~30分钟</div>`;
            }
            return temp;
        },
        filterPayment(payment: string) {
            return PaymentType.getTextFromValue(payment);
        },
        renderArrZoneLabel,
        ...TagFilter,
        formatArch
    };

    initData() {
        return {
            isOneCloud: isOneCloudId(),
            showBanner: true,
            loading: {
                versions: false
            },
            searchbox: {
                keyword: '',
                keywordType: ['NAME'],
                keywordTypes: [
                    {
                        value: 'NAME',
                        text: '集群名称'
                    },
                    {
                        value: 'ID',
                        text: '集群ID'
                    }
                ],
                allTextDataSource: []
            },
            toggleColumn: {
                ...toggleColumn
            },
            versions: [],
            table: {
                ...TABLE_SUI,
                columns: filterTableColumns({columns: [], TableColumns, value: toggleColumn.value})
            },
            TableColumns,
            selection: {
                ...SELECTION_SUI_MULTI
            },
            pager: {...PAGER_SUI},
            moreList: CLUSTER_MORE_OPERATIONS.toArray(),
            editName: this.editName.bind(this),
            check: this.check.bind(this)
        };
    }

    async attached() {
        this.getTags();
        this.getVersions();
        this.getComList();
        this.onInterval();
    }

    onInterval() {
        if (this.stopPrevPolling) {
            this.stopPrevPolling();
        }
        this.stopPrevPolling = requestInterval(async () => this.getTableList(), {time: 60 * 1000});
    }

    onRefresh() {
        this.getComList();
        this.onInterval();
    }

    // 获取版本
    async getVersions() {
        try {
            this.data.set('loading.versions', true);
            const res = await api.listAvailableVersion({});
            const {version} = res || {};
            this.data.set('versions', [
                ...allEnum,
                ..._.map(version, (i: VersionItem) => ({
                    value: i.version,
                    text: i.version
                }))
            ]);
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('loading.versions', false);
        }
    }

    onCloseBanner() {
        this.data.set('showBanner', false);
    }

    onSelectChange(event: {value: {selectedIndex: number[]; selectedItems: Array<Object>}}) {
        this.data.set('selection.selectedIndex', event.value.selectedIndex);
    }

    async onRegionChange(event: {id: string}) {
        this.data.set('pager.page', 1);
        this.onRefresh();
    }

    // 获取标签列表
    async getTags() {
        const result = await thirdApi.getSearchTagList({
            serviceType: 'ROCKETMQ',
            region: [this.$context.getCurrentRegionId()]
        });

        let allTextDataSource = [{text: '所有值', value: '所有值'}];
        result.forEach(item => {
            const data = {
                text: item.tagValue || '空值',
                value: item.tagValue || ''
            };
            const index = _.findIndex(allTextDataSource, i => i.value === data.value);
            index === -1 && allTextDataSource.push(data);
        });

        this.data.set('searchbox.allTextDataSource', allTextDataSource);
        let tags = _.groupBy(result, 'tagKey');
        _.each(tags, (tag, key) => {
            tags[key] = _.map(tag, item => {
                return {name: item.tagValue || '空值', value: item.tagValue};
            });
            tags[key].unshift({name: '所有值', value: '@@@'});
        });
        let tagKeys = _.map(_.keys(tags), key => {
            if (key === '@@@') {
                return {text: '(全部)', value: '@@@'};
            } else if (key === '') {
                return {text: '(无标签)', value: ''};
            }
            return {
                text: key,
                value: key,
                textDataSource: tags[key].map(i => ({
                    text: i.name,
                    value: i.name
                }))
            };
        });
        tagKeys.unshift({
            text: '(无标签)',
            value: '',
            textDataSource: result.map(i => ({
                text: i.name,
                value: i.name
            }))
        });
        tagKeys.unshift({
            text: '(全部)',
            value: '@@@',
            textDataSource: result.map(i => ({
                text: i.name,
                value: i.name
            }))
        });
        this.data.push('searchbox.keywordTypes', {
            text: '标签',
            value: 'TAG',
            children: tagKeys
        });
        return tags;
    }

    // search-box 的 keyword type改变时调用
    onKeywordTypeChange({value}) {
        let textDataSource = [];
        if (Array.isArray(value) && value.indexOf('TAG') > -1) {
            let keywordTypes = this.data.get('searchbox.keywordTypes');
            let tags = _.find(keywordTypes, item => item.value === 'TAG').children;
            if (value[1] === '@@@') {
                textDataSource = this.data.get('searchbox.allTextDataSource');
            } else if (value[1] === '') {
                textDataSource = [{text: '空值', value: ''}];
            } else {
                textDataSource = _.find(tags, tag => tag.text === value[1]).textDataSource;
            }
            this.data.set('searchbox.textDataSource', textDataSource);
        } else if (value.indexOf('TAG') === -1) {
            this.data.merge('searchbox', {
                keywordType: [value[0]],
                keyword: ''
            });
            this.data.set('searchbox.textDataSource', textDataSource);
            this.onRefresh();
        }
    }

    // 生成搜索关键字
    createKeywordParams() {
        let {keyword, keywordType} = this.data.get('searchbox');
        if (keywordType.length > 1) {
            keyword = keyword === '所有值' ? '@@@' : keyword === '空值' ? '' : keyword;
        }
        keywordType = keywordType[1];
        return {
            keywordType,
            keyword
        };
    }

    async getTableList() {
        const {searchbox, pager, status, orderBy, order, version, payment, arch} = this.data.get('');
        const {keywordType, keyword} = this.createKeywordParams();
        let param = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            status,
            payment,
            arch,
            order,
            orderBy,
            version,
            keywordType: searchbox.keywordType[0]
        });
        const params = {
            ...param,
            subKeywordType: keywordType || '',
            keyword: keyword || ''
        };
        const {totalCount, result} = (await api.clusterList(params)) as ListPage<IClusterItem>;
        this.data.set('pager.count', totalCount);
        this.data.set(
            'table.datasource',
            _.map(result, item => formatClusterItem(item))
        );
    }

    // 创建按钮点击
    async onCreate() {
        redirect(`#${ROUTE_PATH.clusterCreate}`);
    }

    @debounce(500)
    async onIpGet(row: IClusterItem) {
        const {clusterId, name: clusterName, encryptionInTransit} = row;
        const res = (await api.getAccessPoints({clusterId, params: {}})) as AccessEndpointResult;
        const dialog = new EndPoint({
            data: {
                ...res,
                clusterId,
                clusterName,
                encryptionInTransit
            }
        });
        dialog.attach(document.body);
    }

    // 版本过滤
    onVersionFilter(target: {version: string}) {
        this.data.set('version', target.version);
        this.onRefresh();
    }

    // 名称输入校验
    check(name: string, callback: (str: string) => void) {
        if (!name) {
            return callback('请输入');
        }
        if (name.length > 65) {
            return callback('不能超过65个字符');
        }
        if (!/^[a-zA-Z()_\/\-.\d]{1,}$/.test(name)) {
            return callback('输入字符格式有误');
        }
        callback('');
    }

    // 名称编辑
    async editName(name: string, rowIndex: number) {
        const item = this.data.get(`table.datasource[${rowIndex}]`);
        Notification.success('修改成功');
        this.data.set(`table.datasource[${rowIndex}].name`, name);
    }

    // 编辑标签
    onEditTag() {
        const {datasource} = this.data.get('table');
        const {selectedIndex} = this.data.get('selection');
        const selects = datasource.filter((item, index) => selectedIndex.includes(index));
        let data = {
            tags: [],
            mode: 'single',
            originTags: []
        };

        if (selects.length === 1) {
            data.tags = selects[0].tags;
            data.params = [
                {
                    resourceId: selects[0].clusterId,
                    tags: selects[0].tags
                }
            ];
        } else {
            data.mode = 'multi';
            data.originTags = selects.reduce((tags, item) => {
                if (item.tags) {
                    return tags.concat(item.tags);
                }
                return tags;
            }, []);
            data.params = selects.map(item => ({
                resourceId: item.clusterId,
                tags: item.tags
            }));
        }

        const dialog = new TagDialog({
            data
        });
        dialog.on('success', () => {
            this.onRefresh();
        });
        dialog.attach(document.body);
    }

    detached() {
        this.stopPrevPolling && this.stopPrevPolling();
    }
    handleDeleteSuccess() {
        this.onRefresh();
    }
}
