/**
 * 编辑标签
 *
 * @file index.js
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import _ from 'lodash';
import {Component} from 'san';
import {Dialog, Notification, Form, Button} from '@baidu/sui';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {api} from '@common/client';
import './index.less';
import {getSdk} from '@common/utils';

export default class TagDialog extends Component {
    static template = html`
        <template>
            <s-dialog
                open="{= open =}"
                title="编辑标签"
                on-confirm="onConfirm"
                on-close="onClose"
                loadingAfterConfirm="{{false}}"
                class="tag-dialog"
                width="580"
            >
                <s-form label-align="left">
                    <s-form-item>
                        <tag-edit-panel s-ref="tagPanel" instances="{{defaultInstances}}" sdk="{{tagSDK}}" />
                    </s-form-item>
                </s-form>
                <footer slot="footer">
                    <s-button on-click="onClose">取消</s-button>
                    <s-button on-click="onConfirm" skin="primary" loading="{{confirmLoading}}">确定</s-button>
                </footer>
            </s-dialog>
        </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        'tag-edit-panel': TagEditPanel,
        's-button': Button
    };

    initData() {
        return {
            open: true,
            tagSDK: getSdk('TAG'),
            mode: 'multi',
            id: ''
        };
    }

    static computed = {
        defaultInstances() {
            const tags = this.data.get('tags');
            const mode = this.data.get('mode');
            // 批量编辑时，集群原绑定的标签集合。
            const originTags = this.data.get('originTags');
            let instances = [{tags}];
            if (mode === 'multi') {
                // 添加模式下
                instances.push({
                    tags: originTags
                });
            }
            return instances;
        }
    };

    async onConfirm() {
        const tags = await this.ref('tagPanel').getTags();
        const {params = []} = this.data.get('');
        const single = params.length <= 1;
        const computeParams = {
            resources: params.map(item => ({
                id: item.id,
                resourceId: item.resourceId,
                tags: single ? tags : item.tags.concat(tags)
            }))
        };
        await api.tagAssign(computeParams);
        Notification.success('编辑成功！');
        this.fire('success', {});
        this.onClose();
    }

    onClose() {
        this.data.set('open', false);
        this.dispose();
    }
}
