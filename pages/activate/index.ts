/**
 * 产品开通页
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import _ from 'lodash';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {Notification, Alert} from '@baidu/sui';
import {AppOrderPage} from '@baidu/sui-biz';
import {isSandbox, OnlineRoleInfo, RoleName, ROUTE_PATH, SanboxRoleInfo} from '@common/config';
import {thirdApi} from '@common/client';

import './index.less';
import {DOC_LINK} from '@common/docs';
import {DocLink} from '@components/index';
import {AccountType, IAccountDetail} from './index.d';
import {throttle} from '@common/decorators';

const klass = 'page-activate';

export const checkActive = async () => {
    // 如果是激活页，直接打开
    if (window.location.hash.includes(ROUTE_PATH.activate)) {
        return;
    }
    const data = await thirdApi.iamStsRoleQuery({
        roleName: RoleName,
    });
    if (!data?.id) {
        redirect(`#${ROUTE_PATH.activate}`);
    }
};

@decorators.asPage(ROUTE_PATH.activate)
export default class extends Component {
    static template = html` <div class="${klass}">
        <div class="${klass}-order-container">
            <div class="${klass}__verify-tip" s-if="{{!isLoadingAccountInfo && !isVerify}}">
                <s-alert skin="warning">
                    产品开通提示：如需要开通消息服务，请先进行
                    <a href="/qualify/#/qualify/index" target="blank">实名认证</a>，
                    本产品开通并授权后才会开始服务计费。
                </s-alert>
            </div>
            <s-order-page
                useNewVersion
                title="{{title}}"
                bgImgSrc="{{bgImgSrc}}"
                logoSrc="{{logoSrc}}"
                confirmed
                protocal="{{protocal}}"
                advantage="{{advantage}}"
                openBtnDisabled="{{openBtnDisabled}}"
                on-click="onOrderCreate"
            >
                <div slot="desc" class="desc">
                    消息服务 for RocketMQ是百度智能云基于Apache
                    RocketMQ构建的低延迟、高并发、高可用、高可靠的分布式消息队列服务，适用于各类大规模、低延时、对可靠性要求高的在线消息场景，可广泛服务于电商、金融、loT等业务。
                    详请查看
                    <doc-link href="${DOC_LINK.rocketmqIntroduction}" class="ml4" skin="primary">帮助文档</doc-link>
                </div>
                <div slot="tip">
                    开通消息服务 for RocketMQ，会同时为您默认开通
                    <doc-link s-for="item in dependServices" href="{{item.href}}" skin="primary">
                        {{item.label}}
                    </doc-link>
                    {{dependServices.length}}个产品
                </div>
            </s-order-page>
        </div>
    </div>`;

    static components = {
        's-order-page': AppOrderPage,
        's-alert': Alert,
        'doc-link': DocLink,
    };

    static computed: SanComputedProps = {
        activeText() {
            return this.data.get('accountType') === 'enterprise' ? '企业认证' : '个人认证';
        },
        openBtnDisabled() {
            const isVerify = this.data.get('isVerify');
            // 只有为false时，才说明有请求结果
            return isVerify === false;
        },
    };

    initData() {
        return {
            isVerify: null,
            isLoadingAccountInfo: false,
            title: '消息服务 for RocketMQ',
            bgImgSrc: require('../../static/img/activate/bg.png'),
            logoSrc: require('../../static/img/activate/logo.png'),
            dependServices: [
                {label: '私有网络VPC、', href: DOC_LINK.vpcIntrduction},
                {label: '负载均衡 BLB、', href: DOC_LINK.blbIntroduction},
                {label: '弹性公网 EIP', href: DOC_LINK.eipIntroduction},
            ],
            protocal: {
                text: '同意使用',
                content: [
                    {
                        content: '《百度智能云用户服务协议》',
                        link: DOC_LINK.userServiceAgreement,
                    },
                ],
            },
            advantage: {
                content: [
                    {
                        title: '全托管',
                        desc: '完备的集群运维与监控告警体系，最大程度释放用户运维压力，业务人员可直接投入业务开发，无需关心集群、节点、服务的运维管理',
                        imgSrc: require('../../static/img/activate/advantage1.png'),
                    },
                    {
                        title: '高可靠',
                        desc: '基于Raft/Dledger协议实现节点管理，节点故障及时感知并自动进行流量迁移，保证业务的连续性',
                        imgSrc: require('../../static/img/activate/advantage2.png'),
                    },
                    {
                        title: '功能完备',
                        desc: '消息类型覆盖全面，支持普通消息、顺序消息、分布式事务消息、定时消息、延时消息，满足多业务场景消息服务需求',
                        imgSrc: require('../../static/img/activate/advantage3.png'),
                    },
                    {
                        title: '即开即用',
                        desc: '平台化构建专属RocketMQ服务，简单高效即可快速接入。兼容开源RocketMQ，业务代码无需改造',
                        imgSrc: require('../../static/img/activate/advantage4.png'),
                    },
                ],
            },
        };
    }

    inited() {
        this.getAccount();
    }

    // 获取用户实名认证信息
    async getAccount() {
        try {
            this.data.set('isLoadingAccountInfo', true);
            const res = (await thirdApi.iamAccountDetail()) as IAccountDetail;
            this.data.set('accountType', res.accountType);
            const isVerify =
                res.accountType === AccountType.Enterprise
                    ? this.$context.isEnterprise()
                    : this.$context.isVerifyUser();
            this.data.set('isVerify', isVerify);
        } catch (err) {
        } finally {
            this.data.set('isLoadingAccountInfo', false);
        }
    }

    // 服务开通
    @throttle(300)
    async onOrderCreate() {
        try {
            const params = {
                ...(isSandbox ? SanboxRoleInfo : OnlineRoleInfo),
                accountId: window.$context.getUserId(),
            };
            await thirdApi.iamStsRoleActivate(params);
            Notification.success('开通成功');
            redirect(`#${ROUTE_PATH.clusterList}`);
        } catch (e) {
            console.error(e);
        }
    }
}
