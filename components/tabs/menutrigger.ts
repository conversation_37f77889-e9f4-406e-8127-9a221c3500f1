import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Trigger, Menu} from '@baidu/sui';
import placements from '@common/config/placement';

export default class MenuTrigger extends Component {
    static template = html` <div class="rigger-div" style="display: inline-block">
        <s-trigger
            trigger="{{trigger}}"
            builtinPlacements="{{builtinPlacements}}"
            popupPlacement="{{placement}}"
            mouseEnterDelay="{{mouseEnterDelay}}"
            mouseLeaveDelay="{{mouseLeaveDelay}}"
            popupClassName="tab-menu-dropdown"
            popupVisible="{= visible =}"
            on-mouseLeave="mouseLeave"
            offset="{{offset}}"
        >
            <s-menu slot="popup" on-click="onFire">
                <s-menu-item key="one">
                    <span>关闭当前</span>
                </s-menu-item>
                <s-menu-item key="left">
                    <span>关闭左侧</span>
                </s-menu-item>
                <s-menu-item key="right">
                    <span>关闭右侧</span>
                </s-menu-item>
                <s-menu-item key="all">
                    <span>关闭全部</span>
                </s-menu-item>
                <s-menu-item key="other">
                    <span>关闭其他</span>
                </s-menu-item>
            </s-menu>
        </s-trigger>
    </div>`;

    static components = {
        's-trigger': Trigger,
        's-menu': Menu,
        's-menu-item': Menu.Item
    };

    initData() {
        return {
            builtinPlacements: placements
        };
    }

    mouseLeave() {
        // this.data.set('visible', false);
        // this.dispose && this.dispose();
    }

    onFire(target) {
        this.fire('del', target.value);
        this.dispose && this.dispose();
    }
}
