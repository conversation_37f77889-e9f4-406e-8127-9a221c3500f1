import {Component} from 'san';
import _ from 'lodash';
import {html, ServiceFactory} from '@baiducloud/runtime';
import {Dropdown, Menu as SMenu, Search, Tooltip} from '@baidu/sui';
import {Close1, Plus1, RightCircle, More2} from '@baidu/xicon-san';
import {throttle} from '@common/decorators';
import MenuTrigger from './menutrigger';
import './index.less';

const $flag = ServiceFactory.resolve('$flag');

export class CustomTabs extends Component {
    static template = html`
        <div class="tabs-tab ">
            <slot name="left"></slot>
            <div s-ref="tab-block" class="tab-block">
                <div
                    s-for="item, index in tabs"
                    s-ref="tab-item-{{index}}"
                    class="{{getTabCellClass(tabs, activeKey, item, index)}}"
                    on-click="handleClick(item)"
                    on-contextmenu="onContextMenu(item, index, $event)"
                >
                    <slot name="item" var-item="item">
                        <span>{{item.name}}</span>
                    </slot>
                    <div class="icon-area">
                        <i-close class="i-close" on-click="onClose(item, $event)"></i-close>
                    </div>
                </div>
                <div s-if="hasAddBtn" class="add-btn {{isTabScroll ? 'is-sticky' : ''}}" on-click="onAdd">
                    <slot name="add-btn">
                        <s-tooltip content="{{addBtnTooltip}}" placement="bottom">
                            <i-plus></i-plus>
                        </s-tooltip>
                    </slot>
                </div>
                <div class="placeholder"></div>
            </div>
            <div class="action-block">
                <div class="action-btn">
                    <s-dropdown class="tab-filter-dropdown">
                        <div class="tab-filter" slot="overlay">
                            <s-search
                                class="tab-search auto-search"
                                value="{=keyword=}"
                                clearable
                                on-search="onSearch"
                            ></s-search>
                            <div class="filter-list">
                                <div class="filter-item" s-for="tab in filteredTabs" on-click="onFilterItemClick(tab)">
                                    <slot name="item" var-item="tab">
                                        <span>{{tab.name}}</span>
                                    </slot>
                                </div>
                            </div>
                        </div>
                        <i-right-circle></i-right-circle>
                    </s-dropdown>
                </div>
                <div class="action-btn">
                    <s-dropdown class="tab-menu-dropdown">
                        <s-menu slot="overlay" on-click="onMenuClick">
                            <s-menu-item key="one">
                                <span>关闭当前</span>
                            </s-menu-item>
                            <s-menu-item key="left">
                                <span>关闭左侧</span>
                            </s-menu-item>
                            <s-menu-item key="right">
                                <span>关闭右侧</span>
                            </s-menu-item>
                            <s-menu-item key="all">
                                <span>关闭全部</span>
                            </s-menu-item>
                            <s-menu-item key="other">
                                <span>关闭其他</span>
                            </s-menu-item>
                        </s-menu>
                        <i-more></i-more>
                    </s-dropdown>
                </div>
            </div>
        </div>
    `;

    static components = {
        'i-close': Close1,
        'i-plus': Plus1,
        'i-right-circle': RightCircle,
        'i-more': More2,
        's-dropdown': Dropdown,
        's-menu': SMenu,
        's-menu-item': SMenu.Item,
        's-search': Search,
        's-tooltip': Tooltip
    };

    static filters = {};

    static computed = {};

    initData() {
        return {
            activeKey: '',
            tabs: [],
            filteredTabs: [],
            keyword: '',
            hasAddBtn: false,
            addBtnTooltip: '新建'
        };
    }

    attached() {
        this.onSearch();
        this.watch('tabs', tabs => {
            this.onSearch();
        });

        this.initResizeObserver();
        this.initMutationObserver();
    }

    detached() {
        this.resizeObserver?.unobserve(this.tabBlock);
        this.mutationObserver?.disconnect();
    }

    onSearch() {
        const tabs = this.data.get('tabs');
        const keyword = this.data.get('keyword');
        const res = _.filter(tabs, item => {
            const name = item.name?.toLocaleLowerCase();
            const kw = keyword.trim().toLocaleLowerCase();
            return !kw || name.indexOf(kw) > -1;
        });
        this.data.set('filteredTabs', res);
    }

    onFilterItemClick(tab) {
        this.handleClick(tab);
    }

    getTabCellClass(tabs, activeKey, item, index) {
        return `tab-cell ${activeKey === item.key ? 'active' : ''} ${index === tabs.length - 1 ? 'last-item' : ''}`;
    }

    addOne(item, options = {}) {
        const tabs = this.data.get('tabs');

        // 判断是否已经存在
        const existItem = _.find(tabs, {key: item.key});
        const existItemIndex = _.findIndex(tabs, {key: item.key});
        if (existItem) {
            this.handleClick(existItem);
            if (options.forceUpdate) {
                this.data.set(`tabs[${existItemIndex}]`, item);
            }
            return;
        }

        const activeKey = this.data.get('activeKey');
        const activeIndex = _.findIndex(tabs, {key: activeKey});
        if (typeof activeIndex === 'number' && activeIndex > -1) {
            this.data.splice('tabs', [activeIndex + 1, 0, item]);
        } else {
            this.data.push('tabs', item);
        }
        this.handleClick(item);
    }

    onAdd() {
        this.fire('add');
    }

    handleClick(item) {
        this.data.set('activeKey', item.key);
        this.fire('change', item);
        setTimeout(() => {
            this.scrollTo(item);
        }, 200);
    }

    scrollTo(item) {
        const tabs = this.data.get('tabs');
        const index = _.findIndex(tabs, {key: item.key});
        if (index >= 0) {
            const tabItem = this.ref(`tab-item-${index}`);
            tabItem?.scrollIntoView?.({behavior: 'smooth', block: 'nearest'});
        }
    }

    onClose(item, e) {
        e?.stopPropagation();
        const tabs = this.data.get('tabs');
        const closeIndex = _.findIndex(this.data.get('tabs'), {key: item.key});
        this.data.splice('tabs', [closeIndex, 1]);

        // 删除后，如果当前是激活的，则切换到上一个或下一个
        const activeKey = this.data.get('activeKey');
        if (activeKey === item.key) {
            const leftItem = tabs[closeIndex - 1];
            const rightItem = tabs[closeIndex + 1];
            if (leftItem) {
                this.handleClick(leftItem);
            } else if (rightItem) {
                this.handleClick(rightItem);
            } else {
            }
        }
    }

    onContextMenu(item, index, event) {
        event.preventDefault();
        const trigger = new MenuTrigger({
            data: {
                trigger: '',
                placement: 'bottom',
                mouseEnterDelay: 0,
                mouseLeaveDelay: 0,
                offset: 0,
                openTabing: false,
                visible: true
            }
        });
        trigger.attach(this.ref(`tab-item-${index}`));
        trigger.on('del', type => this.handleMenuEvents(type, item));
        return false;
    }

    handleMenuEvents(type, item) {
        if (type === 'all') {
            this.fire('close', {
                type,
                callback: () => {
                    this.data.set('tabs', []);
                    this.data.set('activeKey', '');
                }
            });
            return;
        }

        if (!item) {
            return;
        }

        if (type === 'one') {
            this.onClose(item);
        }
        if (type === 'left') {
            const tabs = this.data.get('tabs');
            const index = _.findIndex(tabs, {key: item.key});
            const newTabs = tabs.slice(index);
            this.data.set('tabs', newTabs);
            this.data.set('activeKey', item.key);
        }
        if (type === 'right') {
            const tabs = this.data.get('tabs');
            const index = _.findIndex(tabs, {key: item.key});
            const newTabs = tabs.slice(0, index + 1);
            this.data.set('tabs', newTabs);
            this.data.set('activeKey', item.key);
        }
        if (type === 'other') {
            this.data.set('tabs', [item]);
            this.data.set('activeKey', item.key);
        }
    }

    onMenuClick(e) {
        const item = _.find(this.data.get('tabs'), {key: this.data.get('activeKey')});
        this.handleMenuEvents(e.value, item);
    }

    initResizeObserver() {
        if (!window.ResizeObserver) {
            return;
        }
        this.tabBlock = this.ref('tab-block');

        this.resizeObserver = new ResizeObserver(() => {
            this.checkTabScroll('resize');
        });

        this.resizeObserver.observe(this.tabBlock);
    }

    initMutationObserver() {
        if (!window.MutationObserver) {
            return;
        }
        this.tabBlock = this.ref('tab-block');

        this.mutationObserver = new MutationObserver(() => {
            this.checkTabScroll('mutation');
        });

        const config = {childList: true, subtree: true, characterData: true};
        this.mutationObserver.observe(this.tabBlock, config);
    }

    @throttle(200)
    checkTabScroll(type) {
        const isScroll = this.tabBlock.scrollWidth > this.tabBlock.clientWidth;
        this.data.set('isTabScroll', isScroll);
    }
}
