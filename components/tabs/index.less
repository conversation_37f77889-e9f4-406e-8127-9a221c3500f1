.tabs-tab {
    background: #f7f7f9;
    display: flex;

    .tab-block {
        flex-grow: 1;
        display: flex;
        overflow-x: auto;

        // 隐藏滚动条
        scrollbar-width: none;

        &::-webkit-scrollbar {
            display: none;
        }

        .tab-cell {
            flex: none;
            cursor: pointer;
            padding: 0 12px;
            position: relative;
            height: 32px;
            display: flex;
            align-items: center;
            color: #151b26;
            max-width: 240px;
            border-bottom: 1px solid #e8e9eb;

            &:first-child {
                &::before {
                    display: none;
                }
            }

            // 右侧分隔线
            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translate(0, -50%);
                height: 18px;
                border-right: 1px solid #e8e9eb;
            }

            &:hover {
                background-color: #e6f0ff;

                .i-close {
                    display: initial;
                }
            }

            &.last-item {
                &::after {
                    content: '';
                    position: absolute;
                    right: 0;
                    top: 50%;
                    transform: translate(0, -50%);
                    height: 18px;
                    border-right: 1px solid #e8e9eb;
                }
            }

            // 选中状态
            &.active {
                background: #fff;
                border-top: 2px solid #2468f2;
                border-bottom: 1px solid #fff;
                // height: 33px;
                .i-close {
                    display: initial;
                }

                &::before {
                    display: none;
                }

                &::after {
                    display: none;
                }

                & + .tab-cell {
                    &::before {
                        display: none;
                    }
                }
            }

            .icon-area {
                margin-left: 14px;
                width: 16px;
                height: 16px;
            }

            .i-close {
                position: relative;
                top: -2px;
                display: none;
                color: #b8babf;

                &:hover {
                    color: #2468f2;
                }
            }
        }

        .add-btn {
            flex: none;
            position: -webkit-sticky;
            position: sticky;
            right: 0;
            width: 32px;
            height: 32px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            color: #151b26;
            background: #f7f7f9;
            border-bottom: 1px solid #e8e9eb;

            &:hover {
                color: #2468f2;
            }

            &.is-sticky {
                box-shadow: 2px 0 8px 0 rgba(7, 12, 20, 0.8);
            }
        }

        .placeholder {
            flex-grow: 1;
            flex-shrink: 1;
            border-bottom: 1px solid #e8e9eb;
        }
    }

    .action-block {
        flex: none;
        width: 64px;
        height: 32px;
        background: #f7f7f9;
        display: flex;
        border-bottom: 1px solid #e8e9eb;

        .action-btn {
            cursor: pointer;
            flex: none;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #151b26;

            &:hover {
                color: #2468f2;
            }
        }
    }
}

.tab-filter-dropdown {
    .tab-filter {
        padding: 12px;
        // max-height: 217px;

        .tab-search {
            margin-bottom: 12px;
        }

        .filter-list {
            margin: 0 -12px;
            max-height: 160px;
            overflow: auto;

            .filter-item {
                padding: 0 12px;
                display: flex;
                align-items: center;
                height: 32px;

                &:hover {
                    background: #e6f0ff;
                    cursor: pointer;
                }

                .tab-slot {
                    display: flex;
                    flex-wrap: nowrap;
                    overflow: hidden;

                    .tab-icon {
                        margin-right: 4px;
                        flex: none;
                    }

                    .tab-label {
                        width: calc(~'100% - 20px');
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }
        }
    }

    .s-popup-content-box {
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 2px 8px 0 rgba(7, 12, 20, 0.12);
        background-color: #fff;
    }
}

.tab-menu-dropdown {
    .s-menu {
        min-width: 120px !important;
        box-shadow: none !important;
    }

    .s-popup-content-box {
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 2px 8px 0 rgba(7, 12, 20, 0.12);
        background-color: #fff;
    }
}
