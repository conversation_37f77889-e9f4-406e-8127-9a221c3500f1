/**
 * @file index.ts 创建按钮
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Plus1} from '@baidu/xicon-san';
import {Button} from '@baidu/sui';
import './index.less';

export class CreateBtn extends Component {
    static template = html`
        <template>
            <s-button skin="{{skin}}" on-click="onClick" disabled="{{disabled}}" class="create-btn">
                <x-icon-plus s-if="hasIcon" size="{{16}}" class="mr4 flex plus-icon" />
                <slot />
            </s-button>
        </template>
    `;

    static components = {
        'x-icon-plus': Plus1,
        's-button': Button,
    };

    initData() {
        return {
            text: '',
            hasIcon: true,
            skin: 'primary',
        };
    }

    onClick() {
        this.fire('click', {});
    }
}
