/**
 * 简单弹出框组件，用于二次确认弹窗
 * 支持
 * @file index.js
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {StateWarningPrompt} from '@baidu/xicon-san';
import {Dialog, Button} from '@baidu/sui';
import './index.less';
export class SingleDialog extends Component {
    static template = html` <template>
        <!--bca-disable-->
        <s-dialog class="single-dialog" open="{= signDialog.open =}" on-close="handleSingleDialogCancel" width="500">
            <div slot="title" class="header-title">
                <x-icon-info theme="filled" size="{{20}}" color="#ff9326" class="tip-icon" />
                {{signDialog.title}}
            </div>
            <div class="dialog-text">{{signDialog.text | raw}}</div>
            <footer slot="footer">
                <s-button on-click="handleSingleDialogCancel">取消</s-button>
                <s-button on-click="handleSingleDialogConfirm" skin="primary" loading="{{confirmLoading}}">
                    确定
                </s-button>
            </footer>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        'x-icon-info': StateWarningPrompt
    };
    static computed = {};

    static filters = {
        isDisplay(bool: boolean) {
            return bool ? 'block' : 'none';
        }
    };

    initData() {
        return {
            confirmLoading: false,
            signDialog: {
                title: '提示',
                text: '',
                open: false,
                types: {},
                successFunc: null,
                failFunc: null
            }
        };
    }

    /**
     * singleDialog 是指单一的只有提示，没有表单提交的弹出框
     * type 当前弹出框类型
     * @param {string} type 类型
     * @param {string} title 标题
     * @param {string} text 内容
     * @param {Object} successFunc 确定回调函数
     * @param {Object} failFunc 取消回调函数
     */
    open(title = '提示', text: string, successFunc = () => {}, failFunc = () => {}) {
        this.data.set('signDialog', {
            title,
            text,
            open: true
        });
        !!successFunc && this.data.set('signDialog.successFunc', successFunc);
        !!failFunc && this.data.set('signDialog.failFunc', failFunc);
    }

    /**
     * singleDialog 确认，对应设置的字段
     */
    async handleSingleDialogConfirm() {
        this.fire('submit', this.data.get('signDialog.type'));
        const successFunc = this.data.get('signDialog.successFunc');
        try {
            this.data.set('confirmLoading', true);
            successFunc && (await successFunc());
            this.reset();
        } catch (err) {
            console.error(err);
        } finally {
            this.data.set('confirmLoading', false);
        }
    }

    /**
     * singleDialog 取消
     */
    handleSingleDialogCancel() {
        this.fire('cancel', this.data.get('signDialog.type'));
        const failFunc = this.data.get('signDialog.failFunc');
        failFunc && failFunc();
        this.reset();
    }

    /**
     * reset重置
     */
    reset() {
        this.data.set('signDialog', {
            open: false,
            successFunc: null,
            failFunc: null
        });
    }
}
