/**
 * @file index.ts 文档链接
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Link} from '@baidu/sui';
import {OutlinedExclamationCircle} from '@baidu/sui-icon';

import './index.less';

export class DocLink extends Component {
    static template = html`
        <template>
            <s-link class="help-link" href="{{href}}" target="blank" skin="{{skin}}">
                <s-outlined-exclamation-circle s-if="showIcon" class="icon link-icon" size="{{16}}" />
                <slot name="icon"></slot>
                <slot>{{text}}</slot>
            </s-link>
        </template>
    `;

    static components = {
        's-outlined-exclamation-circle': OutlinedExclamationCircle,
        's-link': Link,
    };

    initData() {
        return {
            showIcon: false,
        };
    }
}
