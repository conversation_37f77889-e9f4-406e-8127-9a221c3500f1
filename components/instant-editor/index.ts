/**
 * instant编辑
 *
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Input, Button, InputNumber} from '@baidu/sui';
import {OutlinedEditingSquare} from '@baidu/sui-icon';
import {PopConfirm} from '@baidu/sui-biz';

import {COLOR_CONF} from '@common/config';

import './index.less';

const klass = 'instance-editor';

export class InstantEditor extends Component {
    static template = html` <div class="${klass}" style="{{visible ? 'display:inline-block' : ''}}">
        <s-popconfirm
            trigger="{{!disabled ? 'click' : ''}}"
            getPopupContainer="{{getPopupContainer}}"
            visible="{= visible =}"
            placement="{{placement}}"
            submitting="{=loading=}"
            on-visibleChange="onVisibleChange"
            submittingText="修改中"
            confirmButtonDisable="{{disabledConfirm}}"
            confirmText="确定"
            cancelText="取消"
            on-confirm="onSure"
            on-cancel="onCancel"
        >
            <div class="${klass}-editor" s-if="{{!disabled}}">
                <slot name="edit">
                    <s-outlined-editing-square color="{{COLOR_CONF.defaultColor}}" />
                </slot>
            </div>
            <div slot="content">
                <div class="${klass}-line">
                    <s-input
                        s-if="{{type === 'input'}}"
                        placeholder="{{placeholder}}"
                        value="{= value =}"
                        width="265"
                        on-input="onInputChange"
                    />
                    <s-inputnumber
                        s-elif="{{type === 'number'}}"
                        value="{= value =}"
                        min="{{min}}"
                        max="{{max}}"
                        step="{{step}}"
                        stepStrictly="{{stepStrictly}}"
                    />
                </div>
                <div class="${klass}-desc desc mt5" s-if="{{desc}}">{{desc}}</div>
                <div class="${klass}-err err mt5" s-if="{{err}}">{{err}}</div>
            </div>
        </s-popconfirm>
    </div>`;

    static components = {
        's-popconfirm': PopConfirm,
        's-outlined-editing-square': OutlinedEditingSquare,
        's-input': Input,
        's-button': Button,
        's-inputnumber': InputNumber,
    };

    initData() {
        return {
            placeholder: '请输入',
            type: 'input',
            getPopupContainer: () => document.body,
            request: () => Promise.resolve(),
            err: '',
            loading: false,
            canEmpty: false,
            placement: 'top',
            COLOR_CONF: COLOR_CONF,
        };
    }

    static computed: SanComputedProps = {
        disabledConfirm() {
            const loading = this.data.get('loading');
            const canEmpty = this.data.get('canEmpty');
            const type = this.data.get('type');
            const value = this.data.get('value');
            return loading || (type === 'input' && !canEmpty && !value);
        },
    };

    inited() {
        this.data.set('valueBackup', this.data.get('value'));
    }

    // 弹框显隐改变
    onVisibleChange(target: {value: boolean}) {
        const disabled = this.data.get('disabled');
        if (disabled) {
            return;
        }
        this.data.set('value', this.data.get('valueBackup'));
        !target.value && this.data.set('err', '');
    }

    // 取消
    onCancel() {
        this.data.set('visible', false);
        this.data.set('err', '');
    }

    // 设置错误信息
    setErr(msg: string) {
        this.data.set('err', msg);
    }

    // 输入的改变
    onInputChange(target: {value: string}) {
        const check = this.data.get('check');
        check && check(target.value, this.setErr.bind(this));
    }

    // 确定
    async onSure() {
        const err = this.data.get('err');
        const value = this.data.get('value');
        const request = this.data.get('request');
        if (!!err) {
            this.data.set('loading', false);
            return;
        }
        try {
            this.data.set('loading', true);
            await request(value, this.data.get('info'));
            this.data.set('valueBackup', value);
            this.data.set('loading', false);
            this.data.set('visible', false);
        } catch (error) {
            this.data.set('loading', false);
        }
    }
}
