/**
 * 详情导航栏
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html, redirect} from '@baiducloud/runtime';
import {Button} from '@baidu/sui';
import {OutlinedLeft} from '@baidu/sui-icon';

import './index.less';

const klass = 'page-header';

export class PageHeader extends Component {
    static template = html`
        <div class="${klass}">
            <span class="back-btn" on-click="onBack">
                <outlined-left width="{{16}}" />
                <span class="back-text">{{backTitle || '返回'}}</span>
            </span>
            <span class="${klass}__title">
                {{title}}
                <slot name="title-right" />
            </span>
            <slot name="extra" />
            <span class="${klass}__right">
                <slot name="right" />
            </span>
        </div>
    `;

    static components = {
        's-button': But<PERSON>,
        'outlined-left': OutlinedLeft,
    };

    onBack() {
        redirect(this.data.get('back'));
    }
}
