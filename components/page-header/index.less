/**
* 顶部导航栏
* index.less
*/

.page-header {
    display: flex;
    width: 100%;
    background: #fff;
    padding: 0 16px 0 0;
    height: 48px;
    align-items: center;

    .back-btn {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: var(--grey-color);

        .back-text {
            margin-left: 4px;
            font-size: 14px;
        }

        .s-icon {
            fill: var(--grey-color);
        }

        &:hover {
            .back-text {
                color: var(--blue-color);
            }

            .s-icon {
                fill: var(--blue-color);
            }
        }
    }

    &__title {
        display: block;
        margin-left: 16px;
        font-size: 16px;
        color: var(--dark-color);
        font-weight: 500;
    }

    &__right {
        margin-left: auto;

        .s-button {
            width: 48px;
        }
    }
}
