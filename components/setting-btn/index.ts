/**
 * @file index.ts 刷新
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import './index.less';
import {GeneralSettingsBackup} from '@baidu/xicon-san';
import {Button} from '@baidu/sui';

export class SettingBtn extends Component {
    static template = html`
        <template>
            <s-button class="btn-setting" on-click="onClick">
                <x-icon-setting size="{{18}}" style="display: flex" />
            </s-button>
        </template>
    `;

    static components = {
        'x-icon-setting': GeneralSettingsBackup,
        's-button': Button,
    };

    onClick() {
        this.fire('click', {});
    }
}
