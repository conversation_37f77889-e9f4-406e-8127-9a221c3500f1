/**
 * @file index.ts 圆角卡片
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import './index.less';

export class BlockBox extends Component {
    static template = html`<template>
        <div class="m-block-box {{hasBottomBorder ? 'has-bottom-border' : ''}}">
            <biz-legend s-if="{{title}}" label="{{title}}" noHighlight>
                <slot name="extra"></slot>
            </biz-legend>
            <slot />
        </div>
    </template>`;

    static components = {
        'biz-legend': AppLegend,
    };

    initData() {
        return {
            title: '',
        };
    }
}
