import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Menu, Checkbox, Dropdown, Button} from '@baidu/sui';
import {Filter1} from '@baidu/xicon-san';

import './index.less';

const prefixCls = 's-table';

export class MultiFilter extends Component {
    static template = html` <template>
        <s-dropdown class="${prefixCls}-filter" visible="{=visible=}">
            <s-outlined-filter class="{{value.length ? 's-icon-button-able-click' : ''}}" />
            <s-menu slot="overlay" class="${prefixCls}-filter-menu">
                <s-menu-item class="${prefixCls}-filter-item">
                    <s-checkbox
                        on-change="onToggleCheckAll($event)"
                        label="全选"
                        checked="{{selectAll}}"
                        indeterminate="{{indeterminate}}"
                    />
                </s-menu-item>
                <s-menu-item class="${prefixCls}-filter-item" s-for="item, index in options" key="{{item.value}}">
                    <s-checkbox
                        label="{{item.text}}"
                        checked="{{item | checked(mValue)}}"
                        on-change="onCheckboxChange(index, $event)"
                    />
                </s-menu-item>
                <div class="multi-select-action">
                    <s-button
                        class="mr8"
                        width="{{46}}"
                        height="{{24px}}"
                        skin="primary"
                        size="small"
                        on-click="onConfirm"
                        >确定
                    </s-button>
                    <s-button width="{{46}}" size="small" on-click="onReset">重置</s-button>
                </div>
            </s-menu>
        </s-dropdown>
    </template>`;

    static components = {
        's-dropdown': Dropdown,
        's-menu': Menu,
        's-menu-item': Menu.Item,
        's-checkbox': Checkbox,
        's-outlined-filter': Filter1,
        's-button': Button
    };

    static filters = {
        checked(item: {label: string; value: string}, value: string) {
            return _.some(value, v => v === item.value);
        }
    };

    initData() {
        return {
            options: [],
            value: [],
            mValue: [],
            getPopupContainer: () => document.body,
            visible: false
        };
    }

    static computed: SanComputedProps = {
        selectAll() {
            const options = this.data.get('options');
            const mValue = this.data.get('mValue');
            return options.length === mValue.length;
        },
        indeterminate() {
            const options = this.data.get('options');
            const mValue = this.data.get('mValue');
            return mValue.length && options.length !== mValue.length;
        }
    };

    attached() {
        this.onReset();
        this.fireChange();
        this.watch('value', v => {
            this.data.set('mValue', v);
        });
        this.watch('visible', v => {
            if (!v) {
                this.onReset();
            }
        });
    }

    onConfirm() {
        this.fireChange();
    }

    onReset() {
        this.data.get('mValue');
    }

    /**
     * checkbox选中
     */
    onCheckboxChange(index: number, {value}: {value: string}) {
        const item = this.data.get(`options[${index}]`);
        const allValue = this.data.get('mValue');
        if (_.every(allValue, v => v !== item.value) && value) {
            this.data.push('mValue', item.value);
        } else {
            const removeIndex = _.findIndex(allValue, v => v === item.value);
            this.data.removeAt('mValue', removeIndex);
        }
    }

    /**
     * fire change
     */
    fireChange() {
        this.nextTick(() => this.fire('change', {value: this.data.get('mValue')}));
    }

    /**
     * 触发全选
     */
    onToggleCheckAll({value}: {value: string}) {
        const hasValue = !!value;
        this.data.set('mValue', hasValue ? _.map(this.data.get('options'), o => o.value) : []);
    }
}
