.multi-select {
    border-radius: 4px;
    box-shadow: 0px 2px 8px 0px rgba(7, 12, 20, 0.12);

    &:hover {
        background-color: #eaf6fe;
        color: #2468f2;
    }
    .s-checkbox {
        width: 100%;
    }

    .s-radio-text {
        margin-left: 5px;
        vertical-align: middle;
    }

    .s-checkbox-input {
        vertical-align: middle;
    }

    &-action {
        display: flex;
        border-top: 1px #e8e9eb solid;
        padding: 8px 12px;
    }
}
