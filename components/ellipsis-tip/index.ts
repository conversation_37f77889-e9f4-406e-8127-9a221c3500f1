import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Popover} from '@baidu/sui';
import ResizeObserver from 'resize-observer-polyfill';

import {SanComputedProps} from 'types/global';

import './index.less';
import _ from 'lodash';

const klass = 'text-ellipsis-tip';

export class EllipsisTip extends Component {
    static template = html` <div class="${klass}">
        <s-popover
            s-if="{{showTip}}"
            class="text-ellipsis-tip-popover {{class}}"
            trigger="hover"
            placement="{{placement}}"
        >
            <div class="ellipsis-text clamp-{{clampNum}}">
                <slot>{{text}}</slot>
            </div>
            <div slot="content">
                <slot name="content">{{text}}</slot>
            </div>
        </s-popover>
        <div s-else class="ellipsis-text clamp-{{clampNum}}">
            <slot>{{text}}</slot>
        </div>
    </div>`;

    static computed: SanComputedProps = {
        showTip() {
            const alwaysTip = this.data.get('alwaysTip');
            const isToShowTip = this.data.get('isToShowTip');
            return alwaysTip || isToShowTip;
        },
    };

    static components = {
        's-popover': Popover,
    };
    observer?: ResizeObserver;

    initData() {
        return {
            placement: 'left',
            alwaysTip: false,
            isToShowTip: false,
            clampNum: 1,
        };
    }

    attached() {
        this.handleResize = _.debounce(this.handleResize.bind(this), 200);
        this.observe();
    }

    detached() {
        this.observer && this.observer.disconnect();
    }

    observe() {
        const alwaysTip = this.data.get('alwaysTip');
        if (!this.el || alwaysTip) {
            return;
        }

        this.observer = new ResizeObserver(this.handleResize);
        this.observer.observe(this.el);
    }

    handleResize() {
        const clampNum = this.data.get('clampNum');
        if (this.el) {
            const textDom = this.el.querySelector('.ellipsis-text');
            let isToShowTip =
                clampNum > 1
                    ? (textDom?.scrollHeight || 0) > (textDom?.clientHeight || 0)
                    : (textDom?.scrollWidth || 0) > (textDom?.clientWidth || 0);

            this.data.set('isToShowTip', isToShowTip);
        }
    }
}
