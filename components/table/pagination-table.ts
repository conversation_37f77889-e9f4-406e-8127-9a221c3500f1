/**
 * 前端分页（带搜索）
 *
 * @file pagination-table.js
 * <AUTHOR>
 */

import _ from 'lodash';

import {CommonTable} from './index';

export abstract class PaginationTable extends CommonTable {
    abstract searchKey: string;

    searchKeyworkKey: string = 'searchbox.keyword';
    // 0.2 s
    time = 200;

    totalList: Array<NormalObject> = [];
    list: Array<NormalObject> = [];

    async getTableList() {}

    abstract getNewTableList(): Promise<Array<NormalObject>>;

    /**
     * 获取表格 list
     */
    async getComList() {
        this.data.set('table.loading', true);
        this.data.set('table.error', false);
        // 清除多选
        this.data.set('selection.selectedIndex', []);
        try {
            const totalList = await this.getNewTableList();
            this.totalList = totalList;
            this.list = totalList;
            this.renderTableByPage();
            this.data.set('pager.count', totalList.length);
            this.data.set('table.loading', false);
        } catch (error) {
            this.data.set('table.error', true);
            this.data.set('table.loading', false);
        }
    }

    /**
     * 分页
     * @param {Number} page page
     * @param {Number} pageSize pageSize
     */
    onPageChange(args: {value: {page: number; pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', args.value.page);
        this.renderTableByPage();
    }

    /**
     * 分页 pageSize 设置
     */
    onPageSizeChange(args: {value: {page: number; pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', 1);
        this.renderTableByPage();
    }

    // 前端分页函数
    renderTableByPage() {
        const {
            pager: {page, pageSize},
        } = this.data.get();
        this.data.set('table.loading', true);
        this.data.set('table.datasource', this.list.slice((page - 1) * pageSize, page * pageSize));
        setTimeout(() => this.data.set('table.loading', false), this.time);
    }

    /**
     * 搜索
     */
    onSearch(args: {value: string}) {
        this.data.set(this.searchKey, args.value);
        this.data.set('pager.page', 1);
        this.filterSearch(this.searchKey);
    }

    /**
     * 搜索方法
     */
    filterSearch(key: string) {
        const keyword = this.data.get(this.searchKeyworkKey);
        this.list = _.filter(this.totalList, (item) => item[key]?.indexOf(keyword) > -1);
        this.data.set('pager.count', this.list.length);
        this.data.set('pager.page', 1);
        this.renderTableByPage();
    }
}
