/**
 * 针对common-table 做的drawer-table
 *
 * @file drawer-table.js
 * <AUTHOR>
 */
import {SanComponent} from 'san/types';

import {PAGER_SUI} from '@common/config';

import {CommonTable} from './common-table';

export abstract class DrawerTable extends CommonTable {
    drawerDialog?: SanComponent<{}>;

    detached() {
        this.disposeDialog();
    }

    // dispose 抽屉弹框
    disposeDialog() {
        if (this.drawerDialog) {
            this.drawerDialog.dispose();
            this.drawerDialog = undefined;
        }
    }

    // region 切换
    onRegionChange() {
        this.disposeDialog();
        this.data.set('searchbox.keyword', '');
        this.data.merge('pager', {...PAGER_SUI});
        this.getComList();
    }
}
