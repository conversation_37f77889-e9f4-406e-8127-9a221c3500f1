/**
 * @file index.ts 刷新
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import './index.less';
import {Refresh} from '@baidu/xicon-san';
import {Button} from '@baidu/sui';

export class RefreshBtn extends Component {
    static template = html`
        <template>
            <s-button class="btn-refresh" on-click="onClick">
                <x-icon-refresh size="{{16}}" style="display: flex" />
            </s-button>
        </template>
    `;

    static components = {
        'x-icon-refresh': Refresh,
        's-button': Button,
    };

    onClick() {
        this.fire('click', {});
    }
}
