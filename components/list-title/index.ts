/**
 * 列表标题
 *
 * @file index.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Alert} from '@baidu/sui';

import './index.less';
import {DocLink} from '@components/index';

const klass = 'list-header';

export class ListTitle extends Component {
    static template = html` <div class="${klass}">
        <div class="${klass}__content">
            <span class="${klass}__content-title">{{title}}</span>
            <!-- TODO：文档链接 -->
            <doc-link s-if="showHelpDoc" class="${klass}__content-right" />
        </div>
    </div>`;

    static components = {
        's-alert': Alert,
        'doc-link': DocLink,
    };
}
