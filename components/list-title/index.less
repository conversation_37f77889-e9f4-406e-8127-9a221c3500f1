/**
* 列表标题
* index.less
*/

.list-header {
    background-color: #fff;
    padding: 12px 16px;

    &__content {
        display: flex;
        align-items: center;
        height: 24px;

        &-title {
            flex: 1;
            font-weight: 500;
            font-size: 16px;
            color: var(--text-base-color);
        }
    }

    &__footer {
        .s-alert {
            margin-top: 12px;
        }
    }
}
