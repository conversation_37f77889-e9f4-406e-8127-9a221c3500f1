/**
 * @file global.d.ts
 * <AUTHOR>
 */

// san Component computed
declare interface SanComputedProps {
    [x: string]: (this: {data: Data}) => any;
}
// san Component filters
declare interface SanFilterProps<T = Data> {
    [k: string]: (this: {data: T}, ...filterOption: any[]) => any;
}

declare interface SanMessageProps<T = Data> {
    [x: string]: (this: {data: T; fire: Function; [prop: string]: any}, e: any) => any;
}

// Object
declare interface Object {
    $context: any;
    $framework: any;
}

// EnumItem
declare interface EnumItem {
    alias: string;
    text: string;
    value: string | number;
    [key: string]: any;
}

declare type NormalObject = {[x: string]: any};

/**
 * 列表返回
 */
interface ListPage<T> {
    /**
     * 升降序
     */
    order: null | string;
    /**
     * 排序字段
     */
    orderBy: null | string;
    /**
     * 页码，页码，大于等于1
     */
    pageNo: number;
    /**
     * 每页大小，每页大小，范围 10 ~ 100
     */
    pageSize: number;
    /**
     * 列表数据
     */
    result: T[];
    /**
     * 总条数
     */
    totalCount: number;
    [property: string]: any;
}

interface ToggleTableColumn {
    name: string;
    label: string;
    filter?: {
        options: any[];
        value: any;
    };
    width?: number;
    fixed?: 'left' | 'right';
    sortable?: boolean;
    render?: Function;
    /** 控制自定义列展示时，该项是否禁用（即无法隐藏） */
    disabled?: boolean;
    /** 控制自定义列展示时，该项是否默认展示 */
    defaultShow?: boolean;
}

interface IRoute {
    query: {
        [key: string]: string;
    };
}

interface ISelectItem {
    /** 展示文案 */
    label: string;
    /** 值 */
    value: string;
    [prop: string]: any;
}
